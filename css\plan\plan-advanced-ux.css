/* Advanced UI/UX Enhancements for Plan.html */

/* Enhanced Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
  z-index: 1;
}

.dark .loading::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Skeleton Loading for Cards */
.card.loading {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.dark .card.loading {
  background: linear-gradient(
    90deg,
    #374151 25%,
    #4b5563 50%,
    #374151 75%
  );
  background-size: 200% 100%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Focus States for Accessibility */
.btn:focus-visible,
.day-button:focus-visible,
.tab-button:focus-visible {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

.card:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 4px;
}

/* Improved Error States */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  background: linear-gradient(145deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: var(--border-radius-xl);
  color: #dc2626;
}

.dark .error-state {
  background: linear-gradient(145deg, #450a0a 0%, #7f1d1d 100%);
  border-color: #dc2626;
  color: #fca5a5;
}

.error-state i {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.7;
}

.error-state h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
}

.error-state p {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-bottom: var(--spacing-4);
}

/* Enhanced Empty States */
.empty-state {
  background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px dashed #cbd5e1;
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-8);
  text-align: center;
  transition: all 0.3s ease;
}

.dark .empty-state {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-300);
}

.empty-state:hover {
  border-color: var(--color-primary-light);
  background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
}

.dark .empty-state:hover {
  border-color: var(--color-primary);
}

.empty-state i {
  font-size: 3rem;
  color: var(--color-gray-400);
  margin-bottom: var(--spacing-4);
  transition: all 0.3s ease;
}

.empty-state:hover i {
  color: var(--color-primary);
  transform: scale(1.1);
}

/* Weather Error States */
.weather-error-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--color-gray-500);
  background: linear-gradient(145deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: var(--border-radius-xl);
}

.dark .weather-error-state {
  background: linear-gradient(145deg, #450a0a 0%, #7f1d1d 100%);
  border-color: #dc2626;
  color: #fca5a5;
}

.weather-error-state i {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  color: var(--color-primary);
  opacity: 0.7;
}

.weather-error-state h4 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-2);
  color: var(--color-gray-700);
  font-weight: 600;
}

.dark .weather-error-state h4 {
  color: var(--color-gray-300);
}

.weather-empty-state {
  text-align: center;
  padding: var(--spacing-6);
  color: var(--color-gray-500);
  background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px dashed #cbd5e1;
  border-radius: var(--border-radius-xl);
}

.dark .weather-empty-state {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-300);
}

.weather-empty-state i {
  font-size: 2rem;
  margin-bottom: var(--spacing-3);
  color: var(--color-secondary);
  opacity: 0.7;
}

/* Budget Error States */
.budget-error-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--color-gray-500);
  background: linear-gradient(145deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: var(--border-radius-xl);
}

.dark .budget-error-state {
  background: linear-gradient(145deg, #450a0a 0%, #7f1d1d 100%);
  border-color: #dc2626;
  color: #fca5a5;
}

.budget-error-state i {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  color: var(--color-warning);
  opacity: 0.7;
}

.budget-error-state h4 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-2);
  color: var(--color-gray-700);
  font-weight: 600;
}

.dark .budget-error-state h4 {
  color: var(--color-gray-300);
}

/* Progressive Disclosure */
.collapsible {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapsible.collapsed {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.collapsible.expanded {
  max-height: 1000px;
  opacity: 1;
}

/* Enhanced Tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.tooltip::before {
  content: '';
  position: absolute;
  bottom: 115%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tooltip:hover::after,
.tooltip:hover::before {
  opacity: 1;
  visibility: visible;
}

/* Smooth Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced Scroll Indicators */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--color-primary) 0%,
    var(--color-secondary) 50%,
    var(--color-accent) 100%
  );
  transform-origin: left;
  transform: scaleX(0);
  z-index: 9999;
  transition: transform 0.1s ease;
}

/* Improved Button Interactions */
.btn {
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::after {
  width: 300px;
  height: 300px;
}

/* Enhanced Card Stacking Animation */
.card-stack {
  position: relative;
}

.card-stack .card:nth-child(1) {
  z-index: 3;
}

.card-stack .card:nth-child(2) {
  z-index: 2;
  transform: translateY(8px) scale(0.98);
  opacity: 0.8;
}

.card-stack .card:nth-child(3) {
  z-index: 1;
  transform: translateY(16px) scale(0.96);
  opacity: 0.6;
}

/* Micro-interactions for Better UX */
.interactive-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: translateY(-2px);
}

.interactive-element:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

/* Enhanced Visual Hierarchy */
.section-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-gray-300) 50%,
    transparent 100%
  );
  margin: var(--spacing-8) 0;
}

.dark .section-divider {
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-gray-600) 50%,
    transparent 100%
  );
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Touch Device Enhancements */
.touch-device .btn:active,
.touch-device .card:active,
.touch-device .day-button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.touch-active {
  transform: scale(0.98) !important;
  transition: transform 0.1s ease !important;
}

/* Keyboard Navigation Enhancement */
.keyboard-navigation *:focus {
  outline: 3px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

/* Animation States */
.animate-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

.budget-category:nth-child(1) { animation-delay: 0.1s; }
.budget-category:nth-child(2) { animation-delay: 0.2s; }
.budget-category:nth-child(3) { animation-delay: 0.3s; }
.budget-category:nth-child(4) { animation-delay: 0.4s; }

.weather-day:nth-child(1) { animation-delay: 0.1s; }
.weather-day:nth-child(2) { animation-delay: 0.2s; }
.weather-day:nth-child(3) { animation-delay: 0.3s; }

/* Enhanced Mobile Scroll Behavior */
@media (max-width: 768px) {
  .day-selector {
    scroll-snap-type: x mandatory;
    scroll-padding: 0 var(--spacing-4);
  }

  .day-button {
    scroll-snap-align: center;
  }

  /* Improve touch scrolling on iOS */
  .day-selector,
  .budget-summary {
    -webkit-overflow-scrolling: touch;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .card,
  .budget-category,
  .weather-day,
  .timeline-card,
  .meal-card {
    border: 2px solid currentColor;
  }

  .btn {
    border: 2px solid currentColor;
  }

  .budget-progress-bar,
  .category-progress-bar {
    border: 1px solid currentColor;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .card:hover,
  .btn:hover,
  .budget-category:hover,
  .weather-day:hover {
    transform: none !important;
  }

  .scroll-indicator {
    display: none;
  }
}

/* Print Styles */
@media print {
  .scroll-indicator,
  .btn,
  .day-navigation,
  .tab-navigation,
  .action-bar {
    display: none !important;
  }

  .card,
  .budget-category,
  .weather-day,
  .timeline-card,
  .meal-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }

  .overview-grid,
  .day-detail-grid {
    display: block !important;
  }

  .budget-summary,
  .weather-summary {
    margin-bottom: 2rem !important;
  }
}
