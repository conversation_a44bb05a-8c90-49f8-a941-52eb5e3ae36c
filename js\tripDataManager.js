// frontend/js/tripDataManager.js
// Handles trip data management with Supabase integration

import { supabase } from './supabaseClient.js';

/**
 * Trip Data Manager class for handling trip data operations
 */
export class TripDataManager {
  constructor() {
    this.currentTrip = null;
  }

  /**
   * Save trip data to Supabase
   * @param {Object} tripData - Trip data object
   * @returns {Promise<Object>} - Saved trip data with ID
   */
  async saveTripToDatabase(tripData) {
    try {
      console.log('Saving trip to database:', tripData);

      // Get current authenticated user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User must be authenticated to create trips. Please sign in first.');
      }

      // Prepare trip data for database (using existing schema)
      const tripRecord = {
        user_id: user.id,
        title: this.generateTripTitle(tripData),
        description: this.generateTripDescription(tripData),
        start_date: tripData.startDate,
        end_date: tripData.endDate,
        budget_total: parseFloat(tripData.budget) || 0,
        currency: tripData.currency || 'MAD',
        arrival_airport_id: tripData.arrivalAirport ? parseInt(tripData.arrivalAirport) : null,
        departure_airport_id: tripData.departureAirport ? parseInt(tripData.departureAirport) : null,
        is_local_traveler: tripData.isLocalTraveler || false,
        dietary_requirements: tripData.dietary || 'No specific requirements',
        accessibility_requirements: tripData.accessibility || 'No specific requirements',
        special_requests: tripData.specialRequests || '',
        status: 'draft'
      };

      // Insert trip record
      const { data: trip, error: tripError } = await supabase
        .from('trip')
        .insert([tripRecord])
        .select()
        .single();

      if (tripError) {
        throw tripError;
      }

      console.log('Trip saved successfully:', trip);

      // Save trip interests
      if (tripData.preferences && Object.keys(tripData.preferences).length > 0) {
        await this.saveTripInterests(trip.id, tripData.preferences, tripData.interestLevels);
      }

      this.currentTrip = trip;
      return trip;

    } catch (error) {
      console.error('Error saving trip to database:', error);
      throw error;
    }
  }

  /**
   * Save trip interests to database
   * @param {number} tripId - Trip ID
   * @param {Object} preferences - User preferences
   * @param {Object} interestLevels - Interest intensity levels
   */
  async saveTripInterests(tripId, preferences, interestLevels = {}) {
    try {
      // Map preference names to interest category IDs
      const interestMapping = {
        culture: 2, // Historical Sites & Museums
        nature: 4,  // Outdoor Activities & Nature
        luxury: 8,  // Luxury Experiences
        sightseeing: 9 // Sightseeing & Photography
      };

      const interestRecords = [];

      for (const [prefName, isSelected] of Object.entries(preferences)) {
        if (isSelected && interestMapping[prefName]) {
          const weight = this.convertInterestLevelToWeight(interestLevels[prefName] || 50);

          interestRecords.push({
            trip_id: tripId,
            interest_id: interestMapping[prefName],
            weight: weight
          });
        }
      }

      if (interestRecords.length > 0) {
        const { error } = await supabase
          .from('trip_interest_junction')
          .insert(interestRecords);

        if (error) {
          throw error;
        }

        console.log('Trip interests saved successfully:', interestRecords);
      }

    } catch (error) {
      console.error('Error saving trip interests:', error);
      throw error;
    }
  }

  /**
   * Convert interest level (0-100) to weight (1-10)
   * @param {number} level - Interest level from 0-100
   * @returns {number} - Weight from 1-10
   */
  convertInterestLevelToWeight(level) {
    // Convert 0-100 scale to 1-10 scale
    // 0-25: weight 1-3, 25-50: weight 4-6, 50-75: weight 7-8, 75-100: weight 9-10
    if (level <= 25) return Math.max(1, Math.ceil(level / 25 * 3));
    if (level <= 50) return Math.max(4, Math.ceil((level - 25) / 25 * 3) + 3);
    if (level <= 75) return Math.max(7, Math.ceil((level - 50) / 25 * 2) + 6);
    return Math.max(9, Math.ceil((level - 75) / 25 * 2) + 8);
  }

  /**
   * Generate trip title based on trip data
   * @param {Object} tripData - Trip data
   * @returns {string} - Generated title
   */
  generateTripTitle(tripData) {
    const startDate = new Date(tripData.startDate);
    const endDate = new Date(tripData.endDate);
    const duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    const preferences = Object.keys(tripData.preferences || {})
      .filter(key => tripData.preferences[key])
      .map(key => key.charAt(0).toUpperCase() + key.slice(1));

    let title = `${duration}-Day Morocco Trip`;

    if (preferences.length > 0) {
      title += ` - ${preferences.join(' & ')}`;
    }

    return title;
  }

  /**
   * Generate trip description based on trip data
   * @param {Object} tripData - Trip data
   * @returns {string} - Generated description
   */
  generateTripDescription(tripData) {
    const preferences = Object.keys(tripData.preferences || {})
      .filter(key => tripData.preferences[key]);

    let description = 'A personalized Morocco travel itinerary';

    if (preferences.length > 0) {
      description += ` focusing on ${preferences.join(', ')} experiences`;
    }

    if (tripData.specialRequests) {
      description += `. Special requirements: ${tripData.specialRequests}`;
    }

    return description;
  }

  /**
   * Get airport name by ID
   * @param {number} airportId - Airport location ID
   * @returns {Promise<string>} - Airport name
   */
  async getAirportName(airportId) {
    try {
      if (!airportId) return null;

      const { data, error } = await supabase
        .from('location')
        .select('name')
        .eq('id', airportId)
        .single();

      if (error) {
        throw error;
      }

      return data?.name || null;
    } catch (error) {
      console.error('Error fetching airport name:', error);
      return null;
    }
  }



  /**
   * Load trip data from database
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} - Trip data
   */
  async loadTripFromDatabase(tripId) {
    try {
      const { data: trip, error } = await supabase
        .from('trip')
        .select(`
          *,
          trip_interest_junction (
            interest_id,
            weight,
            interest_category (
              name,
              type
            )
          )
        `)
        .eq('id', tripId)
        .single();

      if (error) {
        throw error;
      }

      this.currentTrip = trip;
      return trip;

    } catch (error) {
      console.error('Error loading trip from database:', error);
      throw error;
    }
  }

  /**
   * Update trip data in database
   * @param {number} tripId - Trip ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Updated trip data
   */
  async updateTrip(tripId, updateData) {
    try {
      const { data: trip, error } = await supabase
        .from('trip')
        .update(updateData)
        .eq('id', tripId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      this.currentTrip = trip;
      return trip;

    } catch (error) {
      console.error('Error updating trip:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const tripDataManager = new TripDataManager();
