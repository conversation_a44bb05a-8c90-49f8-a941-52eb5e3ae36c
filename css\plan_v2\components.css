/* ===== COMPONENT STYLES ===== */

/* ===== CARDS ===== */

.card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-card-hover);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h2 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: 0;
}

.card-header h2 i {
  color: var(--primary-color);
}

/* ===== BUTTONS ===== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(242, 101, 34, 0.15);
  color: white;
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background-color: rgba(242, 101, 34, 0.1);
  transform: translateY(-2px);
  color: var(--color-primary);
}

.btn-white {
  background-color: white;
  color: var(--color-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.btn-white:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: var(--color-primary);
}

.btn-outline-white {
  background-color: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-white:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
  color: white;
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== PROGRESS BARS ===== */

.progress-bar {
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  height: 8px;
  position: relative;
}

.progress-bar.small {
  height: 4px;
}

.progress-fill {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== ACCOMMODATION CARD ===== */

.accommodation-card {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-6);
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.accommodation-image {
  position: relative;
  overflow: hidden;
}

.accommodation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.accommodation-card:hover .accommodation-image img {
  transform: scale(1.05);
}

.accommodation-badge {
  position: absolute;
  top: var(--spacing-3);
  left: var(--spacing-3);
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.accommodation-content {
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
}

.accommodation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.accommodation-header h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: 0;
}

.accommodation-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.stars {
  display: flex;
  gap: var(--spacing-1);
  color: var(--accent-color);
}

.rating-score {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
}

.accommodation-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.detail-item i {
  color: var(--gray-400);
  width: 16px;
}

.accommodation-amenities {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-6);
}

.amenity-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--gray-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  color: var(--gray-700);
}

.amenity-tag i {
  color: var(--gray-500);
}

.accommodation-pricing {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-1);
}

.price {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.period {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

.accommodation-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* ===== RESTAURANT CARDS ===== */

.restaurants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}

.restaurant-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.restaurant-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.restaurant-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.restaurant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.meal-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  background: var(--accent-color);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.restaurant-content {
  padding: var(--spacing-4);
}

.restaurant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.restaurant-header h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: 0;
}

.restaurant-time {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.restaurant-details {
  margin-bottom: var(--spacing-3);
}

.restaurant-details .detail-item {
  margin-bottom: var(--spacing-1);
}

.restaurant-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-4);
}

.restaurant-pricing {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.restaurant-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* ===== ENHANCED RESTAURANT STYLES ===== */

.restaurants-container {
  max-width: 1200px;
  margin: 0 auto;
}

.restaurants-header {
  text-align: center;
  margin-bottom: var(--spacing-6);
}

.restaurants-header h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.restaurants-header p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
}

.meal-time {
  position: absolute;
  top: var(--spacing-3);
  left: var(--spacing-3);
  background: rgba(0, 0, 0, 0.7);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.meal-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  text-transform: capitalize;
}

.restaurant-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.restaurant-rating .stars {
  display: flex;
  gap: 2px;
}

.restaurant-rating .stars i {
  color: var(--yellow-400);
  font-size: var(--font-size-sm);
}

.rating-score {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* ===== MENU MODAL STYLES ===== */

.menu-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-4);
}

.menu-modal {
  background: var(--white);
  border-radius: var(--radius-lg);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

.menu-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.menu-modal-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin: 0;
}

.menu-modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.menu-modal-close:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.menu-modal-content {
  padding: var(--spacing-4);
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.menu-section {
  margin-bottom: var(--spacing-6);
}

.menu-section:last-child {
  margin-bottom: 0;
}

.menu-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
  padding-bottom: var(--spacing-2);
  border-bottom: 2px solid var(--primary-color);
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.menu-item {
  padding: var(--spacing-3);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.menu-item:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.menu-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-1);
}

.menu-item-name {
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  flex: 1;
}

.menu-item-price {
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  margin-left: var(--spacing-2);
}

.menu-item-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Loading message styles */
.loading-message {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-600);
}

.loading-message i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.loading-message h3 {
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.loading-message p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* ===== ACTIVITY CARD ===== */

.activity-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.activity-duration {
  display: inline-flex;
  align-items: center;
  background: var(--primary-light);
  color: var(--primary-color);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.activity-content {
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
}

.activity-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.activity-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: 0;
}

.activity-time {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.activity-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.activity-description {
  margin-bottom: var(--spacing-4);
}

.activity-description p {
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-3);
}

.activity-highlights h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.activity-highlights ul {
  list-style: none;
  padding: 0;
}

.activity-highlights li {
  position: relative;
  padding-left: var(--spacing-4);
  margin-bottom: var(--spacing-1);
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.activity-highlights li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.activity-pricing {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.activity-actions {
  display: flex;
  gap: var(--spacing-2);
}
