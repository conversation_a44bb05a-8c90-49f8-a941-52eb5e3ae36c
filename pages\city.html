<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>City - Morocco Travel</title>
  <!-- Google Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap" rel="stylesheet">

  <!-- Font Awesome -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: '#F26522',
              dark: '#ff7733',
            },
            secondary: {
              DEFAULT: '#3F20FB',
              dark: '#4f46e5',
            },
            accent: {
              DEFAULT: '#FFF8F2',
              dark: '#1f1f1f',
            },
            dark: {
              bg: '#1f2937',
              'bg-alt': '#111827',
              'bg-accent': '#2d3748',
              border: '#374151',
            }
          },
        }
      },
    };
  </script>

  <!-- Base styles -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
  <link rel="stylesheet" href="../css/main.css">
  <link rel="stylesheet" href="../css/index.css">
  <link rel="stylesheet" href="../css/city.css">

  <style>
    /* Additional styles for dark mode transitions */
    html.transition,
    html.transition *,
    html.transition *:before,
    html.transition *:after {
      transition: all 0.3s ease-in-out !important;
      transition-delay: 0 !important;
    }
  </style>
</head>
<body class="dark:bg-dark-bg dark:text-gray-100" data-page="city">
  <!-- Scroll Progress Bar -->
  <div class="scroll-progress-container dark:bg-gray-700">
    <div class="scroll-progress-bar"></div>
  </div>

  <!-- Header Placeholder -->
  <header id="header-placeholder" class="bg-white dark:bg-dark-bg border-b border-gray-200 dark:border-dark-border">
    <!-- Header will be loaded here -->
  </header>

  <!-- Loading state -->
  <div id="loading-state" class="flex justify-center items-center h-screen">
    <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-orange-600 dark:border-orange-500"></div>
  </div>

  <!-- Content will be loaded here -->
  <div id="city-content" class="hidden">
    <!-- Hero Section -->
    <section id="destination-hero" class="relative h-[600px]">
      <div class="absolute inset-0">
        <img id="hero-image" class="w-full h-full object-cover" src="" alt="" />
        <div class="absolute inset-0 bg-black bg-opacity-40"></div>
      </div>
      <div class="relative container mx-auto px-6 h-full flex items-center">
        <div class="text-white">
          <h1 id="city-name" class="text-5xl md:text-6xl font-bold mb-6"></h1>
          <p id="city-short-description" class="text-xl max-w-2xl">
          </p>
        </div>
      </div>
    </section>

    <!-- Quick Facts Bar -->
    <section id="quick-facts" class="bg-white dark:bg-dark-bg-accent shadow-md relative -mt-16 z-10">
      <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 py-8">
          <div class="flex items-center space-x-4">
            <i class="fa-solid fa-calendar-alt text-3xl text-orange-600 dark:text-orange-500"></i>
            <div>
              <h3 class="font-semibold dark:text-gray-100">Best Time to Visit</h3>
              <p id="best-time" class="text-gray-600 dark:text-gray-300"></p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <i class="fa-solid fa-wallet text-3xl text-orange-600 dark:text-orange-500"></i>
            <div>
              <h3 class="font-semibold dark:text-gray-100">Average Budget</h3>
              <p id="budget" class="text-gray-600 dark:text-gray-300"></p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <i class="fa-solid fa-clock text-3xl text-orange-600 dark:text-orange-500"></i>
            <div>
              <h3 class="font-semibold dark:text-gray-100">Recommended Stay</h3>
              <p id="recommended-stay" class="text-gray-600 dark:text-gray-300"></p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- City Pass Card Section -->
    <section id="city-pass-card" class="py-12 bg-gradient-to-br from-orange-50 to-white dark:from-dark-bg-alt dark:to-dark-bg relative overflow-hidden">
      <!-- Decorative elements -->
      <div class="absolute top-0 right-0 w-64 h-64 bg-primary/5 dark:bg-primary-dark/5 rounded-full -mr-32 -mt-32 blur-2xl"></div>
      <div class="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 dark:bg-primary-dark/5 rounded-full -ml-48 -mb-48 blur-2xl"></div>

      <div class="container mx-auto px-6 relative z-10">
        <div class="max-w-5xl mx-auto">
          <!-- Section Header -->
          <div class="text-center mb-10">
            <span class="inline-block px-4 py-1 bg-orange-100 dark:bg-dark-bg-accent text-primary dark:text-primary-dark rounded-full text-sm font-medium mb-4">
              <i class="fas fa-ticket-alt mr-1"></i> City Card
            </span>
            <h2 class="text-3xl font-bold mb-2 dark:text-gray-100">Explore <span id="city-card-name"></span> with a City Card</h2>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Free museums, easy transport, one smart card for your entire visit</p>
          </div>

          <!-- City Pass Card Content -->
          <div class="grid md:grid-cols-2 gap-8 items-center">
            <!-- Left Content: Physical Card Image -->
            <div class="relative">
              <div class="city-card-3d-container">
                <!-- Mobile instruction -->
                <div class="flip-instruction"></div>
                <!-- Front of the card -->
                <div class="city-card-front">
                  <!-- City Card Image (dynamically set by JavaScript) -->
                  <img id="city-card-front-image" src="" alt="City Card Front" class="w-full h-full object-contain">
                </div>

                <!-- Back of the card (shared back) -->
                <div class="city-card-back">
                  <img src="../assets/image/back.png" alt="City Card Back" class="w-full h-full object-contain">
                </div>
              </div>
            </div>

            <!-- Right Content: Card Overview -->
            <div class="p-6 md:p-8 bg-white dark:bg-dark-bg-accent rounded-2xl shadow-xl relative overflow-hidden city-card-content">
              <!-- Decorative elements -->
              <div class="absolute top-0 right-0 w-40 h-40 bg-orange-100/30 dark:bg-orange-900/10 rounded-full -mr-20 -mt-20 blur-2xl"></div>
              <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-100/20 dark:bg-blue-900/10 rounded-full -ml-16 -mb-16 blur-xl"></div>
              <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-yellow-100/20 dark:bg-yellow-900/10 rounded-full blur-lg"></div>

              <div class="relative z-10">
                <!-- Card Header -->
                <div class="flex items-center mb-8">
                  <div class="w-14 h-14 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/20 rounded-full flex items-center justify-center mr-4 shadow-md transform transition-transform duration-300 hover:scale-110">
                    <i class="fas fa-ticket-alt text-2xl text-primary dark:text-primary-dark"></i>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold dark:text-gray-100"><span id="city-card-title"></span> City Card</h3>
                    <p class="text-gray-600 dark:text-gray-400 flex items-center">
                      <span class="bg-primary/10 dark:bg-primary-dark/10 px-2 py-0.5 rounded-md mr-2">
                        <span id="city-card-price" class="font-semibold">$45</span>
                      </span>
                      for 2 days access
                    </p>
                  </div>
                </div>

                <!-- Key Benefits -->
                <div class="mb-8">
                  <h4 class="text-lg font-semibold mb-4 dark:text-gray-100 flex items-center">
                    <span class="inline-block w-8 h-0.5 bg-primary dark:bg-primary-dark mr-2"></span>
                    Key Benefits
                  </h4>
                  <ul class="space-y-3">
                    <li class="flex items-center benefit-item">
                      <div class="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <i class="fas fa-piggy-bank text-green-600 dark:text-green-400"></i>
                      </div>
                      <span class="text-gray-700 dark:text-gray-300">Save up to 60% on attraction tickets</span>
                    </li>
                    <li class="flex items-center benefit-item">
                      <div class="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <i class="fas fa-bolt text-blue-600 dark:text-blue-400"></i>
                      </div>
                      <span class="text-gray-700 dark:text-gray-300">Skip the lines at popular attractions</span>
                    </li>
                    <li class="flex items-center benefit-item">
                      <div class="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <i class="fas fa-bus text-purple-600 dark:text-purple-400"></i>
                      </div>
                      <span class="text-gray-700 dark:text-gray-300">Unlimited public transportation</span>
                    </li>
                    <li class="flex items-center benefit-item">
                      <div class="flex-shrink-0 w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <i class="fas fa-mobile-alt text-amber-600 dark:text-amber-400"></i>
                      </div>
                      <span class="text-gray-700 dark:text-gray-300">Digital and physical card options</span>
                    </li>
                  </ul>
                </div>

                <!-- Top Attractions Preview -->
                <div class="mb-8">
                  <h4 class="text-lg font-semibold mb-4 dark:text-gray-100 flex items-center">
                    <span class="inline-block w-8 h-0.5 bg-primary dark:bg-primary-dark mr-2"></span>
                    Top Attractions
                  </h4>
                  <div id="city-card-attractions" class="flex flex-wrap gap-2">
                    <!-- Attractions will be inserted here by JavaScript -->
                  </div>
                </div>

                <!-- CTA Button -->
                <div class="relative">
                  <div class="absolute inset-0 bg-gradient-to-r from-primary to-primary-dark blur-md opacity-30 rounded-xl transform -rotate-1 scale-105"></div>
                  <a id="city-card-link" href="" class="city-card-cta relative inline-flex items-center justify-center w-full py-4 px-6 bg-primary hover:bg-primary-dark text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl hover:translate-y-[-2px] group">
                    <i class="fas fa-info-circle mr-2 group-hover:animate-pulse"></i>
                    <span>See Full Details</span>
                    <i class="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Description & Highlights -->
    <section id="description-highlights" class="py-16 dark:bg-dark-bg">
      <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div>
            <h2 class="text-3xl font-bold mb-6 dark:text-gray-100">About <span id="about-city-name"></span></h2>
            <p id="about-description" class="text-gray-600 dark:text-gray-300 mb-6">
            </p>
            <div id="highlights-container" class="grid grid-cols-2 gap-6 mt-8">
              <!-- Highlights will be inserted here -->
            </div>
          </div>
          <div id="gallery-container" class="grid grid-cols-2 gap-4">
            <!-- Gallery images will be inserted here -->
          </div>
        </div>
      </div>
    </section>

    <!-- Sample Itinerary -->
    <section id="sample-itinerary" class="py-16 bg-gray-50 dark:bg-dark-bg-alt">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold mb-12 text-center dark:text-gray-100">Sample 2-Day Itinerary</h2>
        <div id="itinerary-container" class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Itinerary days will be inserted here -->
        </div>
        <div class="text-center mt-12">
          <button class="bg-orange-600 dark:bg-orange-700 text-white px-8 py-3 rounded-full hover:bg-orange-700 dark:hover:bg-orange-800">
            Create Custom Itinerary
          </button>
        </div>
      </div>
    </section>

    <!-- Video Section -->
    <section id="video-section" class="py-16 dark:bg-dark-bg">
      <div class="container mx-auto px-6">
        <div class="relative rounded-xl overflow-hidden">
          <iframe id="city-video" class="w-full h-[600px] object-cover" src="" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen>
          </iframe>
        </div>
      </div>
    </section>

    <!-- Interactive Map -->
    <section id="map-section" class="py-16 bg-gray-50 dark:bg-dark-bg-alt">
      <div class="container mx-auto px-6">
        <h2 class="text-3xl font-bold mb-12 dark:text-gray-100">Explore <span id="map-city-name"></span></h2>
        <div class="bg-white dark:bg-dark-bg-accent rounded-xl shadow-lg p-4 h-[500px]">
          <iframe id="city-map" src="" class="w-full h-full rounded-lg" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
        </div>
      </div>
    </section>

    <!-- Contact Guide Section -->
    <section id="guide-section" class="py-16 dark:bg-dark-bg">
      <div class="container mx-auto px-6">
        <div class="bg-orange-50 dark:bg-dark-bg-accent/50 rounded-xl p-8 md:p-12">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-3xl font-bold mb-6 dark:text-gray-100">Connect with Local Experts</h2>
              <p class="text-gray-600 dark:text-gray-300 mb-8">
                Get insider knowledge and personalized recommendations from our experienced local guides who know <span id="guide-city-name"></span> inside out.
              </p>
              <button class="bg-orange-600 dark:bg-orange-700 text-white px-8 py-3 rounded-full hover:bg-orange-700 dark:hover:bg-orange-800">
                <a href="./guides.html">Browse Local Guides</a>
              </button>
            </div>
            <div id="guides-container" class="grid grid-cols-2 gap-4">
              <!-- Guide cards will be inserted here -->
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Footer Placeholder -->
  <footer id="footer-placeholder" class="bg-white dark:bg-dark-bg border-t border-gray-200 dark:border-dark-border">
    <!-- Footer will be loaded here -->
  </footer>

  <!-- Scripts -->
  <script type="module" src="../js/main.js"></script>
  <script type="module" src="../js/pages/city.js"></script>
</body>
</html>