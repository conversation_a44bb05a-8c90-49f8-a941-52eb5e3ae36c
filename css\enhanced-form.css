/* Enhanced Form Styles for Tsafira Travel Planner */

/* Form container styling */
.enhanced-form-container {
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%);
  border-radius: 1.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .enhanced-form-container {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.8) 100%);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.enhanced-form-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.07);
}

.dark .enhanced-form-container:hover {
  box-shadow: 0 15px 60px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.07);
}

/* Decorative elements */
.form-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(242, 101, 34, 0.2) 0%, rgba(242, 101, 34, 0.1) 100%);
  z-index: 0;
}

.form-decoration-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
}

.form-decoration-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
}

/* Form header styling */
.form-header {
  position: relative;
  z-index: 1;
  padding-bottom: 1.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .form-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.form-title {
  background: linear-gradient(90deg, #F26522 0%, #ff7733 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 800;
  letter-spacing: -0.02em;
}

/* Form field styling */
.form-field {
  position: relative;
  transition: transform 0.2s ease;
}

.form-field:focus-within {
  transform: translateY(-2px);
}

.form-label {
  display: flex;
  align-items: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: color 0.2s ease;
}

.form-label i {
  margin-right: 0.5rem;
  opacity: 0.7;
}

.form-field:focus-within .form-label {
  color: #F26522;
}

.dark .form-field:focus-within .form-label {
  color: #ff7733;
}

/* Input styling */
.enhanced-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid transparent;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.dark .enhanced-input {
  background-color: rgba(255, 255, 255, 0.05);
}

.enhanced-input:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .enhanced-input:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.enhanced-input:focus {
  outline: none;
  border-color: #F26522;
  background-color: rgba(242, 101, 34, 0.03);
  box-shadow: 0 0 0 4px rgba(242, 101, 34, 0.15);
}

.dark .enhanced-input:focus {
  border-color: #ff7733;
  background-color: rgba(255, 119, 51, 0.05);
  box-shadow: 0 0 0 4px rgba(255, 119, 51, 0.15);
}

/* Date input styling */
input[type="date"].enhanced-input {
  appearance: none;
  padding-right: 2.5rem;
}

input[type="date"].enhanced-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  width: 2.5rem;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
}

/* Range slider styling */
.slider-container {
  padding: 1.5rem 0.5rem 0.5rem;
  position: relative;
}

.enhanced-range {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  outline: none;
}

.dark .enhanced-range {
  background: rgba(255, 255, 255, 0.1);
}

.enhanced-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #F26522;
  cursor: pointer;
  box-shadow: 0 0 0 4px rgba(242, 101, 34, 0.2);
  transition: all 0.2s ease;
}

.dark .enhanced-range::-webkit-slider-thumb {
  background: #ff7733;
  box-shadow: 0 0 0 4px rgba(255, 119, 51, 0.2);
}

.enhanced-range::-moz-range-thumb {
  width: 22px;
  height: 22px;
  border: none;
  border-radius: 50%;
  background: #F26522;
  cursor: pointer;
  box-shadow: 0 0 0 4px rgba(242, 101, 34, 0.2);
  transition: all 0.2s ease;
}

.dark .enhanced-range::-moz-range-thumb {
  background: #ff7733;
  box-shadow: 0 0 0 4px rgba(255, 119, 51, 0.2);
}

.enhanced-range::-webkit-slider-thumb:hover {
  box-shadow: 0 0 0 6px rgba(242, 101, 34, 0.3);
  transform: scale(1.1);
}

.dark .enhanced-range::-webkit-slider-thumb:hover {
  box-shadow: 0 0 0 6px rgba(255, 119, 51, 0.3);
}

.enhanced-range::-moz-range-thumb:hover {
  box-shadow: 0 0 0 6px rgba(242, 101, 34, 0.3);
  transform: scale(1.1);
}

.dark .enhanced-range::-moz-range-thumb:hover {
  box-shadow: 0 0 0 6px rgba(255, 119, 51, 0.3);
}

.range-tooltip {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #F26522;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
}

.dark .range-tooltip {
  background: #ff7733;
}

.range-tooltip.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.range-tooltip:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #F26522;
}

.dark .range-tooltip:after {
  border-top-color: #ff7733;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.range-label {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.5);
  transition: color 0.2s ease;
}

.dark .range-label {
  color: rgba(255, 255, 255, 0.5);
}

.range-label.active {
  color: #F26522;
  font-weight: 600;
}

.dark .range-label.active {
  color: #ff7733;
}

/* Checkbox styling */
.enhanced-checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.enhanced-checkbox {
  position: relative;
}

.enhanced-checkbox input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  background: rgba(0, 0, 0, 0.03);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.dark .checkbox-label {
  background: rgba(255, 255, 255, 0.05);
}

.checkbox-label i {
  margin-right: 0.5rem;
  font-size: 1rem;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.enhanced-checkbox input:checked + .checkbox-label {
  background: rgba(242, 101, 34, 0.1);
  color: #F26522;
  border: 1px solid rgba(242, 101, 34, 0.3);
}

.dark .enhanced-checkbox input:checked + .checkbox-label {
  background: rgba(255, 119, 51, 0.15);
  color: #ff7733;
  border: 1px solid rgba(255, 119, 51, 0.3);
}

.enhanced-checkbox input:checked + .checkbox-label i {
  opacity: 1;
  transform: scale(1.1);
}

.checkbox-label:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.dark .checkbox-label:hover {
  background: rgba(255, 255, 255, 0.08);
}

.enhanced-checkbox input:checked + .checkbox-label:hover {
  background: rgba(242, 101, 34, 0.15);
}

.dark .enhanced-checkbox input:checked + .checkbox-label:hover {
  background: rgba(255, 119, 51, 0.2);
}

/* City card option styling */
.city-card-option {
  border-radius: 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem;
  background: rgba(0, 0, 0, 0.01);
  transition: all 0.2s ease;
}

.dark .city-card-option {
  border: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(255, 255, 255, 0.02);
}

.city-card-option:hover {
  background: rgba(0, 0, 0, 0.02);
}

.dark .city-card-option:hover {
  background: rgba(255, 255, 255, 0.03);
}

.recommended-badge {
  background: linear-gradient(90deg, #F26522 0%, #ff7733 100%);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 1rem;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 2px 5px rgba(242, 101, 34, 0.3);
}

.recommended-badge i {
  font-size: 0.6rem;
  margin-right: 0.25rem;
}

/* Duration options styling */
.duration-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.duration-option {
  position: relative;
}

.duration-option input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.duration-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 0.875rem;
}

.dark .duration-label {
  background: rgba(255, 255, 255, 0.05);
}

.duration-option input:checked + .duration-label {
  background: rgba(242, 101, 34, 0.1);
  color: #F26522;
  border: 1px solid rgba(242, 101, 34, 0.3);
  font-weight: 600;
}

.dark .duration-option input:checked + .duration-label {
  background: rgba(255, 119, 51, 0.15);
  color: #ff7733;
  border: 1px solid rgba(255, 119, 51, 0.3);
}

.duration-label:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.dark .duration-label:hover {
  background: rgba(255, 255, 255, 0.08);
}

/* Submit button styling */
.submit-button {
  width: 100%;
  padding: 1rem;
  border-radius: 1rem;
  background: linear-gradient(90deg, #F26522 0%, #ff7733 100%);
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(242, 101, 34, 0.3);
}

.submit-button:active {
  transform: translateY(0);
}

.submit-button i {
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
}

.submit-button:hover i {
  transform: translateX(4px);
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.submit-button:hover::before {
  left: 100%;
}

/* Form animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease forwards;
}

.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }

/* Slide up animation for city card options */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

.animate-slide-down {
  animation: slideDown 0.3s ease forwards;
  overflow: hidden;
}

/* Error styling */
.input-error {
  border-color: #ef4444 !important;
  background-color: rgba(239, 68, 68, 0.05) !important;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.2s ease;
}

.error-message.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-checkbox-group {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .form-title {
    font-size: 1.5rem !important;
  }
  
  .enhanced-input {
    padding: 0.75rem;
  }
  
  .checkbox-label {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .checkbox-label i {
    font-size: 0.875rem;
  }
  
  .submit-button {
    padding: 0.875rem;
    font-size: 1rem;
  }
}
