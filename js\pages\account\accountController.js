/**
 * Account Controller - Clean, consolidated account page management
 * Handles all account functionality without duplication
 */

import accountDataManager from './accountDataManager.js';
import { supabase } from '../../supabaseClient.js';

class AccountController {
  constructor() {
    this.initialized = false;
    this.userData = null;
    this.saveTimeout = null;
    this.completionUpdateTimeout = null;
  }

  /**
   * Initialize the account controller
   */
  async initialize() {
    if (this.initialized) return;

    try {
      console.log('Initializing Account Controller...');

      // Load user data
      this.userData = await accountDataManager.loadUserData();
      
      // Populate UI with data
      this.populateUI();
      
      // Set up all event listeners
      this.setupEventListeners();

      this.initialized = true;
      console.log('Account Controller initialized successfully');
    } catch (error) {
      console.error('Error initializing Account Controller:', error);
      this.showToast('error', 'Error', 'Failed to load account data');
    }
  }

  /**
   * Populate UI with user data
   */
  populateUI() {
    if (!this.userData?.userData) return;

    const user = this.userData.userData;

    // Populate basic profile info
    this.setElementValue('#first-name', user.name?.split(' ')[0] || '');
    this.setElementValue('#last-name', user.name?.split(' ').slice(1).join(' ') || '');
    this.setElementValue('#email', user.email || '');
    this.setElementValue('#phone', user.phone || '');

    // Populate preferences
    this.setElementValue('#currency', user.preferences?.currency || 'USD');
    this.setElementValue('#language', user.preferences?.language || 'English');
    this.setElementValue('#timezone', user.preferences?.timezone || 'UTC');
    this.setElementValue('#date-format', user.preferences?.dateFormat || 'YYYY-MM-DD');
    this.setElementValue('#theme-preference', user.preferences?.theme || 'auto');

    // Populate profile info in sidebar
    this.setElementText('.profile-name', user.name || 'User');
    this.setElementText('.profile-email span', user.email || '');

    // Set avatar images with cache-busting
    if (user.avatar_url) {
      const avatarImages = document.querySelectorAll('.profile-photo, .profile-image');
      const cacheBustedUrl = this.getCacheBustedAvatarUrl(user.avatar_url);
      avatarImages.forEach(img => {
        img.src = cacheBustedUrl;
      });
    }

    // Populate travel preferences
    this.populateTravelPreferences(user.travelPreferences);
  }

  /**
   * Populate travel preferences
   */
  populateTravelPreferences(preferences) {
    // If no preferences, set all to default (Medium = 0.5)
    if (!preferences || Object.keys(preferences).length === 0) {
      const allControls = document.querySelectorAll('.preference-control');
      allControls.forEach(control => {
        const options = control.querySelectorAll('.preference-option');
        options.forEach(option => option.classList.remove('active'));

        // Find the medium option (0.5) and activate it
        const mediumOption = Array.from(options).find(option =>
          parseFloat(option.dataset.value) === 0.5
        );
        if (mediumOption) {
          mediumOption.classList.add('active');
        }
      });
      return;
    }

    Object.entries(preferences).forEach(([interestId, preferenceData]) => {
      const control = document.querySelector(`[data-interest-id="${interestId}"]`);
      if (control) {
        // Remove active class from all options
        const options = control.querySelectorAll('.preference-option');
        options.forEach(option => option.classList.remove('active'));

        // Find and activate the matching option
        const weight = preferenceData.weight;
        const matchingOption = Array.from(options).find(option => {
          return Math.abs(parseFloat(option.dataset.value) - weight) < 0.01;
        });

        if (matchingOption) {
          matchingOption.classList.add('active');
        }
      }
    });
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Avatar upload
    this.setupAvatarUpload();
    
    // Travel preferences
    this.setupTravelPreferences();
    
    // Theme preference
    this.setupThemePreference();
    
    // Form submission
    this.setupFormSubmission();
  }

  /**
   * Set up avatar upload
   */
  setupAvatarUpload() {
    const photoUploadOverlay = document.querySelector('.photo-upload-overlay');

    if (photoUploadOverlay) {
      photoUploadOverlay.addEventListener('click', async () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        
        input.addEventListener('change', async (event) => {
          const file = event.target.files[0];
          if (!file) return;

          if (file.size > 5 * 1024 * 1024) {
            this.showToast('error', 'Error', 'File size must be less than 5MB');
            return;
          }

          this.showToast('info', 'Uploading', 'Uploading avatar...');

          try {
            const result = await accountDataManager.uploadAvatar(file);

            // Remove loading toast
            const loadingToasts = document.querySelectorAll('.toast-notification.info');
            loadingToasts.forEach(toast => {
              if (toast.textContent.includes('Uploading')) {
                toast.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => toast.remove(), 300);
              }
            });

            if (result.success) {
              // Update all avatar images with cache-busted URL
              const avatarImages = document.querySelectorAll('.profile-photo, .profile-image');
              avatarImages.forEach(img => {
                img.src = result.avatarUrl;
              });

              // Trigger header auth manager to refresh with new avatar
              if (window.headerAuthManager) {
                window.headerAuthManager.loadUserData().then(() => {
                  window.headerAuthManager.updateHeaderUI();
                });
              }

              this.showToast('success', 'Success', 'Avatar updated successfully!');
            } else {
              this.showToast('error', 'Error', result.error || 'Failed to upload avatar');
            }
          } catch (error) {
            // Remove loading toast
            const loadingToasts = document.querySelectorAll('.toast-notification.info');
            loadingToasts.forEach(toast => {
              if (toast.textContent.includes('Uploading')) {
                toast.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => toast.remove(), 300);
              }
            });
            this.showToast('error', 'Error', 'Failed to upload avatar');
          }
        });

        input.click();
      });
    }
  }

  /**
   * Set up travel preferences
   */
  setupTravelPreferences() {
    const preferenceControls = document.querySelectorAll('.preference-control');

    preferenceControls.forEach(control => {
      const options = control.querySelectorAll('.preference-option');
      
      options.forEach(option => {
        option.addEventListener('click', () => {
          // Remove active class from all options in this control
          options.forEach(opt => opt.classList.remove('active'));

          // Add active class to clicked option
          option.classList.add('active');

          // Save preferences (debounced)
          clearTimeout(this.saveTimeout);
          this.saveTimeout = setTimeout(() => {
            this.saveTravelPreferences();
          }, 500);
        });
      });
    });
  }

  /**
   * Save travel preferences
   */
  async saveTravelPreferences() {
    try {
      const preferences = {};
      const preferenceControls = document.querySelectorAll('.preference-control');

      preferenceControls.forEach(control => {
        const interestId = control.dataset.interestId;
        const activeOption = control.querySelector('.preference-option.active');

        if (activeOption && interestId) {
          preferences[interestId] = parseFloat(activeOption.dataset.value);
        }
      });

      console.log('Travel preferences to save:', preferences);
      const result = await accountDataManager.updateTravelInterests(preferences);

      if (result.success) {
        console.log('Travel preferences saved successfully');
        return { success: true };
      } else {
        console.error('Failed to save travel preferences:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error saving travel preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Set up theme preference
   */
  setupThemePreference() {
    const themeSelect = document.getElementById('theme-preference');
    if (themeSelect) {
      themeSelect.addEventListener('change', async (e) => {
        const theme = e.target.value;
        
        // Apply theme immediately
        this.applyTheme(theme);
        
        // Save to database
        try {
          const result = await accountDataManager.updatePreferences({
            theme_preference: theme
          });
          
          if (result.success) {
            console.log('Theme preference saved successfully');
          }
        } catch (error) {
          console.error('Error saving theme preference:', error);
        }
      });
    }
  }

  /**
   * Apply theme
   */
  applyTheme(theme) {
    localStorage.setItem('theme', theme);
    
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark-mode');
    } else if (theme === 'light') {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark-mode');
    } else { // auto
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark-mode');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark-mode');
      }
    }
  }

  /**
   * Set up form submission
   */
  setupFormSubmission() {
    const saveButton = document.querySelector('.section-header .primary-button');

    if (saveButton) {
      saveButton.addEventListener('click', async (e) => {
        e.preventDefault();
        await this.handleFormSubmission();
      });
    }
  }

  /**
   * Handle form submission
   */
  async handleFormSubmission() {
    try {
      this.showToast('info', 'Saving', 'Saving changes...');

      // Collect form data
      const formData = this.collectFormData();
      console.log('Form data collected:', formData);

      // Update profile and preferences
      console.log('Updating profile and preferences...');
      const [profileResult, preferencesResult] = await Promise.all([
        accountDataManager.updateProfile({
          name: formData.name,
          phone: formData.phone
        }),
        accountDataManager.updatePreferences({
          default_currency: formData.currency,
          default_language: formData.language,
          timezone: formData.timezone,
          date_format: formData.dateFormat,
          theme_preference: formData.theme
        })
      ]);

      console.log('Profile result:', profileResult);
      console.log('Preferences result:', preferencesResult);

      // Save travel preferences
      console.log('Saving travel preferences...');
      const travelPrefsResult = await this.saveTravelPreferences();
      console.log('Travel preferences result:', travelPrefsResult);

      // Remove loading toast
      const loadingToasts = document.querySelectorAll('.toast-notification.info');
      loadingToasts.forEach(toast => {
        if (toast.textContent.includes('Saving')) {
          toast.style.animation = 'slideOut 0.3s ease-in forwards';
          setTimeout(() => toast.remove(), 300);
        }
      });

      if (profileResult.success && preferencesResult.success) {
        this.showToast('success', 'Success', 'Profile updated successfully!');

        // Reload user data and refresh UI
        console.log('Reloading user data...');
        this.userData = await accountDataManager.loadUserData();
        this.populateUI();

        // Update header with new data
        if (window.headerAuthManager) {
          window.headerAuthManager.loadUserData().then(() => {
            window.headerAuthManager.updateHeaderUI();
          });
        }
      } else {
        const error = profileResult.error || preferencesResult.error || 'Unknown error';
        console.error('Form submission failed:', error);
        this.showToast('error', 'Error', `Failed to update profile: ${error}`);
      }
    } catch (error) {
      console.error('Error saving profile:', error);

      // Remove loading toast
      const loadingToasts = document.querySelectorAll('.toast-notification.info');
      loadingToasts.forEach(toast => {
        if (toast.textContent.includes('Saving')) {
          toast.style.animation = 'slideOut 0.3s ease-in forwards';
          setTimeout(() => toast.remove(), 300);
        }
      });

      this.showToast('error', 'Error', `Failed to save changes: ${error.message}`);
    }
  }

  /**
   * Collect form data
   */
  collectFormData() {
    const firstName = this.getElementValue('#first-name') || '';
    const lastName = this.getElementValue('#last-name') || '';

    return {
      name: `${firstName} ${lastName}`.trim(),
      phone: this.getElementValue('#phone') || '',
      currency: this.getElementValue('#currency') || 'USD',
      language: this.getElementValue('#language') || 'English',
      timezone: this.getElementValue('#timezone') || 'UTC',
      dateFormat: this.getElementValue('#date-format') || 'YYYY-MM-DD',
      theme: this.getElementValue('#theme-preference') || 'auto'
    };
  }



  /**
   * Show toast notification
   */
  showToast(type, title, message) {
    // Remove existing toasts of the same type
    const existingToasts = document.querySelectorAll(`.toast-notification.${type}`);
    existingToasts.forEach(toast => {
      toast.style.animation = 'slideOut 0.3s ease-in forwards';
      setTimeout(() => toast.remove(), 300);
    });

    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.className = 'toast-container';
      toastContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        display: flex;
        flex-direction: column;
        gap: 8px;
        pointer-events: none;
      `;
      document.body.appendChild(toastContainer);
    }

    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;

    const colors = {
      success: { bg: '#059669', border: '#10b981' },
      error: { bg: '#dc2626', border: '#ef4444' },
      info: { bg: '#2563eb', border: '#3b82f6' }
    };

    const icons = {
      success: 'fa-check-circle',
      error: 'fa-exclamation-circle',
      info: 'fa-info-circle'
    };

    toast.style.cssText = `
      background: ${colors[type].bg};
      border-left: 4px solid ${colors[type].border};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 320px;
      min-width: 280px;
      font-size: 14px;
      line-height: 1.4;
      animation: slideIn 0.3s ease-out;
      pointer-events: auto;
      cursor: pointer;
    `;

    toast.innerHTML = `
      <div style="display: flex; align-items: flex-start; gap: 10px;">
        <i class="fas ${icons[type]}" style="margin-top: 2px; font-size: 16px;"></i>
        <div style="flex: 1;">
          <div style="font-weight: 600; margin-bottom: 2px;">${title}</div>
          <div style="font-size: 13px; opacity: 0.95;">${message}</div>
        </div>
        <button class="toast-close" style="
          background: none;
          border: none;
          color: white;
          cursor: pointer;
          padding: 0;
          margin-left: 8px;
          opacity: 0.7;
          font-size: 14px;
        ">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;

    // Add click to dismiss
    toast.addEventListener('click', () => {
      toast.style.animation = 'slideOut 0.3s ease-in forwards';
      setTimeout(() => toast.remove(), 300);
    });

    toastContainer.appendChild(toast);

    // Auto-remove after 4 seconds (except loading messages)
    if (type !== 'info' || !message.toLowerCase().includes('loading')) {
      setTimeout(() => {
        if (toast.parentNode) {
          toast.style.animation = 'slideOut 0.3s ease-in forwards';
          setTimeout(() => toast.remove(), 300);
        }
      }, 4000);
    }
  }

  /**
   * Get cache-busted avatar URL
   */
  getCacheBustedAvatarUrl(avatarUrl) {
    if (!avatarUrl) return null;
    const separator = avatarUrl.includes('?') ? '&' : '?';
    return `${avatarUrl}${separator}t=${Date.now()}`;
  }

  /**
   * Helper method to set element value
   */
  setElementValue(selector, value) {
    const element = document.querySelector(selector);
    if (element) {
      element.value = value;
    }
  }

  /**
   * Helper method to get element value
   */
  getElementValue(selector) {
    const element = document.querySelector(selector);
    return element ? element.value : '';
  }

  /**
   * Helper method to set element text
   */
  setElementText(selector, text) {
    const element = document.querySelector(selector);
    if (element) {
      element.textContent = text;
    }
  }
}

// Create and export singleton instance
const accountController = new AccountController();
export default accountController;
