/**
 * Authentication Guard for Plan V2
 * 
 * Handles authentication and authorization checks for the plan page.
 * Ensures users are signed in and can only access their own trips.
 */

import { supabase } from '../supabaseClient.js';
import { isAuthenticated, getCurrentUser } from '../core/auth.js';

/**
 * Authentication Guard Class
 * 
 * Responsibilities:
 * - Check user authentication status
 * - Verify trip ownership
 * - Handle unauthorized access attempts
 * - Provide secure data loading patterns
 */
export class AuthGuard {
  constructor() {
    this.currentUser = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the authentication guard
   * @returns {Promise<boolean>} - True if user is authenticated, false otherwise
   */
  async initialize() {
    try {
      console.log('🔐 AuthGuard: Initializing authentication check...');
      
      // Check if user is authenticated
      const authenticated = await isAuthenticated();
      
      if (!authenticated) {
        console.log('❌ AuthGuard: User not authenticated');
        this.handleUnauthenticatedAccess();
        return false;
      }

      // Get current user details
      this.currentUser = await getCurrentUser();
      
      if (!this.currentUser) {
        console.log('❌ AuthGuard: Unable to get current user details');
        this.handleUnauthenticatedAccess();
        return false;
      }

      console.log('✅ AuthGuard: User authenticated successfully');
      console.log('👤 Current user ID:', this.currentUser.id);
      
      this.isInitialized = true;
      return true;

    } catch (error) {
      console.error('❌ AuthGuard: Error during authentication check:', error);
      this.handleAuthenticationError(error);
      return false;
    }
  }

  /**
   * Verify that a trip belongs to the current authenticated user
   * @param {number} tripId - Trip ID to verify ownership
   * @returns {Promise<boolean>} - True if user owns the trip, false otherwise
   */
  async verifyTripOwnership(tripId) {
    try {
      if (!this.isInitialized || !this.currentUser) {
        console.error('❌ AuthGuard: Not initialized or no current user');
        return false;
      }

      console.log(`🔍 AuthGuard: Verifying ownership of trip ${tripId} for user ${this.currentUser.id}`);

      // Query the trip table to check ownership
      const { data: tripData, error } = await supabase
        .from('trip')
        .select('id, user_id, title')
        .eq('id', tripId)
        .single();

      if (error) {
        console.error('❌ AuthGuard: Error checking trip ownership:', error);
        
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No rows returned - trip doesn't exist
          this.handleTripNotFound(tripId);
          return false;
        }
        
        throw error;
      }

      if (!tripData) {
        console.log(`❌ AuthGuard: Trip ${tripId} not found`);
        this.handleTripNotFound(tripId);
        return false;
      }

      // Check if the trip belongs to the current user
      if (tripData.user_id !== this.currentUser.id) {
        console.log(`❌ AuthGuard: Trip ${tripId} belongs to user ${tripData.user_id}, not ${this.currentUser.id}`);
        this.handleUnauthorizedTripAccess(tripId, tripData.title);
        return false;
      }

      console.log(`✅ AuthGuard: User ${this.currentUser.id} owns trip ${tripId} (${tripData.title})`);
      return true;

    } catch (error) {
      console.error('❌ AuthGuard: Error verifying trip ownership:', error);
      this.handleAuthorizationError(error);
      return false;
    }
  }

  /**
   * Secure wrapper for loading trip data
   * Only loads data if user is authenticated and owns the trip
   * @param {number} tripId - Trip ID to load
   * @returns {Promise<Object|null>} - Trip data or null if unauthorized
   */
  async secureLoadTripData(tripId) {
    try {
      // Ensure authentication is initialized
      if (!this.isInitialized) {
        const authenticated = await this.initialize();
        if (!authenticated) {
          return null;
        }
      }

      // Verify trip ownership
      const hasAccess = await this.verifyTripOwnership(tripId);
      if (!hasAccess) {
        return null;
      }

      console.log(`🔒 AuthGuard: Loading trip data for authorized trip ${tripId}`);
      
      // If we reach here, user is authenticated and owns the trip
      // Return the user ID for use in data loading
      return {
        userId: this.currentUser.id,
        tripId: tripId,
        authorized: true
      };

    } catch (error) {
      console.error('❌ AuthGuard: Error in secure trip data loading:', error);
      return null;
    }
  }

  /**
   * Handle unauthenticated access attempts
   */
  handleUnauthenticatedAccess() {
    console.log('🚫 AuthGuard: Redirecting unauthenticated user to signin');

    // Save the current URL for redirect after login (with error handling)
    const currentUrl = window.location.href;
    try {
      localStorage.setItem('redirectAfterLogin', currentUrl);
    } catch (error) {
      console.warn('⚠️ Could not save redirect URL to localStorage:', error.message);
    }
    
    // Show a brief message before redirect
    this.showAccessDeniedMessage(
      'Authentication Required',
      'Please sign in to view your trip plans.',
      () => {
        window.location.href = '/pages/signin.html';
      }
    );
  }

  /**
   * Handle trip not found scenarios
   * @param {number} tripId - Trip ID that wasn't found
   */
  handleTripNotFound(tripId) {
    console.log(`🚫 AuthGuard: Trip ${tripId} not found`);
    
    this.showAccessDeniedMessage(
      'Trip Not Found',
      `The trip you're looking for (ID: ${tripId}) doesn't exist or has been deleted.`,
      () => {
        window.location.href = '/pages/account.html';
      }
    );
  }

  /**
   * Handle unauthorized trip access attempts
   * @param {number} tripId - Trip ID user tried to access
   * @param {string} tripTitle - Title of the trip (if available)
   */
  handleUnauthorizedTripAccess(tripId, tripTitle = 'Unknown Trip') {
    console.log(`🚫 AuthGuard: Unauthorized access attempt to trip ${tripId}`);
    
    this.showAccessDeniedMessage(
      'Access Denied',
      `You don't have permission to view "${tripTitle}". You can only access your own trips.`,
      () => {
        window.location.href = '/pages/account.html';
      }
    );
  }

  /**
   * Handle authentication errors
   * @param {Error} error - Authentication error
   */
  handleAuthenticationError(error) {
    console.error('🚫 AuthGuard: Authentication error:', error);
    
    this.showAccessDeniedMessage(
      'Authentication Error',
      'There was a problem verifying your identity. Please try signing in again.',
      () => {
        window.location.href = '/pages/signin.html';
      }
    );
  }

  /**
   * Handle authorization errors
   * @param {Error} error - Authorization error
   */
  handleAuthorizationError(error) {
    console.error('🚫 AuthGuard: Authorization error:', error);
    
    this.showAccessDeniedMessage(
      'Access Error',
      'There was a problem checking your access permissions. Please try again.',
      () => {
        window.location.href = '/pages/account.html';
      }
    );
  }

  /**
   * Show access denied message with redirect
   * @param {string} title - Error title
   * @param {string} message - Error message
   * @param {Function} redirectCallback - Function to call for redirect
   */
  showAccessDeniedMessage(title, message, redirectCallback) {
    // Create and show error overlay
    const overlay = document.createElement('div');
    overlay.className = 'access-denied-overlay';
    overlay.innerHTML = `
      <div class="access-denied-content">
        <div class="access-denied-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <h2>${title}</h2>
        <p>${message}</p>
        <div class="access-denied-actions">
          <button class="btn btn-primary" id="redirect-btn">
            <i class="fas fa-arrow-right"></i>
            Continue
          </button>
        </div>
      </div>
    `;

    // Add styles
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      font-family: Inter, sans-serif;
    `;

    const content = overlay.querySelector('.access-denied-content');
    content.style.cssText = `
      background: white;
      padding: 2rem;
      border-radius: 12px;
      text-align: center;
      max-width: 400px;
      margin: 1rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    `;

    const icon = overlay.querySelector('.access-denied-icon');
    icon.style.cssText = `
      font-size: 3rem;
      color: #dc3545;
      margin-bottom: 1rem;
    `;

    const button = overlay.querySelector('#redirect-btn');
    button.style.cssText = `
      background: #007bff;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1rem;
      margin-top: 1rem;
    `;

    // Add to page
    document.body.appendChild(overlay);

    // Handle redirect
    button.addEventListener('click', () => {
      document.body.removeChild(overlay);
      redirectCallback();
    });

    // Auto-redirect after 5 seconds
    setTimeout(() => {
      if (document.body.contains(overlay)) {
        document.body.removeChild(overlay);
        redirectCallback();
      }
    }, 5000);
  }

  /**
   * Get current authenticated user
   * @returns {Object|null} - Current user object or null
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Check if guard is initialized and user is authenticated
   * @returns {boolean} - True if initialized and authenticated
   */
  isReady() {
    return this.isInitialized && !!this.currentUser;
  }
}

// Export singleton instance
export const authGuard = new AuthGuard();
