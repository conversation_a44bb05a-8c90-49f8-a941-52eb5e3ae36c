/**
 * Timeline Tab Module
 * 
 * Handles rendering and functionality for the timeline tab
 */

/**
 * Get transport icon based on mode
 * @param {string} mode - Transport mode
 * @returns {string} Font Awesome icon class
 */
function getTransportIcon(mode) {
  const iconMap = {
    'car': 'car',
    'bus': 'bus',
    'train': 'train',
    'plane': 'plane',
    'walking': 'walking',
    'taxi': 'taxi',
    'bike': 'bicycle'
  };

  return iconMap[mode?.toLowerCase()] || 'car';
}

/**
 * Render timeline tab with real data
 * @param {Object} dayData - Day data from trip plan
 * @param {Object} AppState - Global application state (unused but kept for consistency)
 * @returns {void}
 */
function renderTimelineTab(dayData, AppState) {
  console.log('⏰ Rendering timeline tab for day', dayData.day_number);

  const timelineTab = document.getElementById('timeline-tab');
  if (!timelineTab) return;

  try {
    // Collect all events for the day
    const events = [];

    // Add accommodation events
    if (dayData.lodging) {
      events.push({
        time: '15:00',
        type: 'accommodation',
        icon: 'bed',
        title: 'Hotel Check-in',
        description: dayData.lodging.name || 'Accommodation',
        category: 'Accommodation'
      });
    }

    // Add meal events
    if (dayData.meals && dayData.meals.length > 0) {
      dayData.meals.forEach(meal => {
        events.push({
          time: meal.time || 'TBD',
          type: 'meal',
          icon: 'utensils',
          title: `${meal.type || 'Meal'} at ${meal.restaurant?.name || 'Restaurant'}`,
          description: meal.restaurant?.description || 'Delicious local cuisine',
          category: 'Meal'
        });
      });
    }

    // Add activity events
    if (dayData.activities && dayData.activities.length > 0) {
      dayData.activities.forEach(activity => {
        events.push({
          time: activity.time || 'TBD',
          type: 'activity',
          icon: 'map-marker-alt',
          title: activity.name || 'Activity',
          description: activity.description || 'Exciting local experience',
          category: 'Activity'
        });
      });
    }

    // Add transport events
    const allTransport = [...(dayData.intra_city || []), ...(dayData.inter_city || [])];
    allTransport.forEach(transport => {
      events.push({
        time: transport.departure_time || 'TBD',
        type: 'transport',
        icon: getTransportIcon(transport.mode),
        title: `${transport.mode || 'Transport'} to ${transport.to_location || 'Destination'}`,
        description: `${transport.distance_km || 'N/A'} km • ${transport.duration_minutes ? Math.round(transport.duration_minutes / 60) + 'h ' + (transport.duration_minutes % 60) + 'min' : 'Duration TBD'}`,
        category: 'Transport'
      });
    });

    // Sort events by time (basic sorting, could be improved)
    events.sort((a, b) => {
      if (a.time === 'TBD') return 1;
      if (b.time === 'TBD') return -1;
      return a.time.localeCompare(b.time);
    });

    if (events.length === 0) {
      timelineTab.innerHTML = `
        <div class="timeline-container">
          <div class="timeline-header">
            <h3>Complete Day Timeline</h3>
            <p>No events scheduled for Day ${dayData.day_number}</p>
          </div>
          <div class="no-data-message">
            <i class="fas fa-clock"></i>
            <h3>No timeline data</h3>
            <p>No events scheduled for this day.</p>
          </div>
        </div>
      `;
      return;
    }

    const timelineHtml = events.map(event => `
      <div class="timeline-item">
        <div class="timeline-time">${event.time}</div>
        <div class="timeline-icon ${event.type}">
          <i class="fas fa-${event.icon}"></i>
        </div>
        <div class="timeline-content">
          <h4>${event.title}</h4>
          <p>${event.description}</p>
          <span class="timeline-category">${event.category}</span>
        </div>
      </div>
    `).join('');

    timelineTab.innerHTML = `
      <div class="timeline-container">
        <div class="timeline-header">
          <h3>Complete Day Timeline</h3>
          <p>All activities, meals, and transport for Day ${dayData.day_number}</p>
        </div>
        <div class="timeline">
          ${timelineHtml}
        </div>
      </div>
    `;

  } catch (error) {
    console.error('❌ Error rendering timeline tab:', error);
    timelineTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading timeline data</h3>
        <p>Please try refreshing the page.</p>
      </div>
    `;
  }
}

export default renderTimelineTab;
