/**
 * Transport Calculator Service
 * 
 * Comprehensive transport calculation system that follows the exact daily flow:
 * 1. Hotel → Restaurant (breakfast)
 * 2. Restaurant → Activity 
 * 3. Activity → Restaurant (lunch)
 * 4. Restaurant → Hotel
 * 5. Hotel → Restaurant (dinner)
 * 6. Restaurant → Hotel
 * 
 * Plus inter-city transport on last day of each city.
 */

import { supabase } from '../supabaseClient.js';

export class TransportCalculator {
  constructor() {
    // Constants for transport calculations
    this.DAILY_CAR_RENTAL_USD = 20.0;
    this.USD_TO_MAD_RATE = 9.5;
    this.FUEL_EFFICIENCY_KMPL = 17.5; // km per liter
    this.GASOLINE_PRICE_USD = 1.20; // per liter
    this.DIESEL_PRICE_USD = 1.10; // per liter
    
    // Taxi pricing constants
    this.TAXI_BASE_FARE_MAD = 7.50;
    this.TAXI_RATE_PER_80M_MAD = 0.20;
    this.TAXI_MARGIN = 1.20; // 20% margin
    
    // Walking threshold
    this.WALKING_THRESHOLD_KM = 0.5;
    this.WALKING_SPEED_KMH = 4.0;
    
    // OSRM API base URL
    this.OSRM_BASE_URL = 'https://osrm-morocco-308196743891.europe-west1.run.app';
  }

  /**
   * Calculate complete transport system for a trip
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Complete transport calculation results
   */
  async calculateTripTransport(tripId) {
    console.log(`🚗 TransportCalculator: Starting calculation for trip ${tripId}`);
    
    try {
      // Get trip data with all locations
      const tripData = await this.getTripLocationData(tripId);
      if (!tripData.success) {
        throw new Error(tripData.error);
      }

      const results = {
        success: true,
        tripId,
        totalCostUSD: 0,
        dailyTransport: [],
        interCityTransport: [],
        budgetAllocation: {
          dailyAllocations: [],
          tripLevelAllocation: 0
        }
      };

      // Calculate intra-city transport for each day
      for (const day of tripData.days) {
        console.log(`📅 Calculating transport for Day ${day.dayNumber}`);
        
        const dailyTransport = await this.calculateDailyTransport(day);
        results.dailyTransport.push(dailyTransport);
        results.totalCostUSD += dailyTransport.totalCostUSD;
      }

      // Calculate inter-city transport
      const interCityTransport = await this.calculateInterCityTransport(tripData.days);
      results.interCityTransport = interCityTransport;
      results.totalCostUSD += interCityTransport.reduce((sum, transport) => sum + transport.primaryOption.priceUSD, 0);

      // Calculate budget allocations
      results.budgetAllocation = this.calculateBudgetAllocations(results.dailyTransport, results.interCityTransport);

      console.log(`✅ Transport calculation completed. Total cost: $${results.totalCostUSD.toFixed(2)}`);
      return results;

    } catch (error) {
      console.error('❌ Transport calculation failed:', error);
      return {
        success: false,
        error: error.message,
        tripId
      };
    }
  }

  /**
   * Get trip location data for transport calculations
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Trip data with locations
   */
  async getTripLocationData(tripId) {
    try {
      // Get trip data using existing function
      const { data: tripData, error } = await supabase.rpc('get_trip_plan_json', {
        p_trip_id: tripId,
        p_current_day: 1 // We need all days, current day doesn't matter for transport calc
      });

      if (error) throw error;
      if (!tripData || !tripData.days) throw new Error('No trip data found');

      // Check if trip has any meaningful data for transport calculation
      const hasData = tripData.days.some(day =>
        (day.lodging && day.lodging.location) ||
        (day.meals && day.meals.length > 0) ||
        (day.activities && day.activities.length > 0)
      );

      if (!hasData) {
        throw new Error('Trip has no accommodation, meal, or activity data. Transport calculation requires location data.');
      }

      // Transform data for transport calculations
      const days = tripData.days.map(day => ({
        dayNumber: day.day_number,
        date: day.date,
        cityName: day.location.city,
        tripDayId: null, // Will be populated when saving to database
        
        // Extract locations with coordinates
        accommodation: this.extractLocationData(day.lodging, 'accommodation'),
        breakfast: this.extractMealLocation(day.meals, 'breakfast'),
        lunch: this.extractMealLocation(day.meals, 'lunch'),
        dinner: this.extractMealLocation(day.meals, 'dinner'),
        activities: day.activities.map(activity => this.extractLocationData(activity, 'activity'))
      }));

      return {
        success: true,
        tripId,
        tripName: tripData.trip.name,
        days
      };

    } catch (error) {
      console.error('Error getting trip location data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract location data from accommodation/activity objects
   * @param {Object} item - Accommodation or activity object
   * @param {string} type - Type of location
   * @returns {Object} Location data with coordinates
   */
  extractLocationData(item, type) {
    if (!item || !item.location) return null;

    return {
      id: item.id,
      name: item.name,
      type,
      coordinates: {
        lat: item.location.coordinates.lat,
        lng: item.location.coordinates.lng
      },
      address: item.location.address
    };
  }

  /**
   * Extract meal location from meals array
   * @param {Array} meals - Array of meals
   * @param {string} mealType - Type of meal (breakfast, lunch, dinner)
   * @returns {Object} Meal location data
   */
  extractMealLocation(meals, mealType) {
    const meal = meals.find(m => m.type.toLowerCase() === mealType);
    if (!meal || !meal.restaurant) return null;

    return {
      id: meal.id,
      name: meal.restaurant.name,
      type: 'restaurant',
      mealType,
      coordinates: {
        lat: meal.restaurant.location.coordinates.lat,
        lng: meal.restaurant.location.coordinates.lng
      },
      address: meal.restaurant.location.address
    };
  }

  /**
   * Calculate daily intra-city transport for a single day
   * @param {Object} day - Day data with locations
   * @returns {Promise<Object>} Daily transport calculation
   */
  async calculateDailyTransport(day) {
    const segments = [];
    let totalCostUSD = 0;

    // Define the exact transport sequence
    const transportSequence = [
      { from: day.accommodation, to: day.breakfast, description: 'Hotel → Breakfast' },
      { from: day.breakfast, to: day.activities[0], description: 'Breakfast → Activity' },
      { from: day.activities[0], to: day.lunch, description: 'Activity → Lunch' },
      { from: day.lunch, to: day.accommodation, description: 'Lunch → Hotel' },
      { from: day.accommodation, to: day.dinner, description: 'Hotel → Dinner' },
      { from: day.dinner, to: day.accommodation, description: 'Dinner → Hotel' }
    ];

    // Calculate each transport segment
    for (let i = 0; i < transportSequence.length; i++) {
      const { from, to, description } = transportSequence[i];
      
      if (!from || !to) {
        console.warn(`⚠️ Missing location for segment: ${description}`);
        continue;
      }

      console.log(`🔄 Calculating segment: ${description}`);
      
      const segmentResult = await this.calculateTransportSegment(from, to, description, i + 1);
      segments.push(segmentResult);
      totalCostUSD += segmentResult.primaryOption.priceUSD;
    }

    return {
      dayNumber: day.dayNumber,
      date: day.date,
      cityName: day.cityName,
      segments,
      totalCostUSD,
      totalSegments: segments.length
    };
  }

  /**
   * Calculate transport options for a single segment
   * @param {Object} from - Origin location
   * @param {Object} to - Destination location
   * @param {string} description - Segment description
   * @param {number} sequence - Segment sequence number
   * @returns {Promise<Object>} Transport segment calculation
   */
  async calculateTransportSegment(from, to, description, sequence) {
    try {
      // Get route data from OSRM
      const routeData = await this.getOSRMRoute(from.coordinates, to.coordinates);
      
      const distanceKm = routeData.distance / 1000;
      const durationMinutes = routeData.duration / 60;

      // Check if walking is appropriate
      if (distanceKm <= this.WALKING_THRESHOLD_KM) {
        const walkingOption = this.calculateWalkingOption(distanceKm, durationMinutes);
        return {
          sequence,
          description,
          from: from.name,
          to: to.name,
          distance: distanceKm,
          duration: durationMinutes,
          primaryOption: walkingOption,
          alternativeOption: null,
          recommendedMode: 'walking'
        };
      }

      // Calculate taxi and car rental options
      const taxiOption = this.calculateTaxiOption(distanceKm, durationMinutes);
      const carRentalOption = this.calculateCarRentalOption(distanceKm, durationMinutes);

      // Select most economical as primary
      const primaryOption = taxiOption.priceUSD <= carRentalOption.priceUSD ? taxiOption : carRentalOption;
      const alternativeOption = taxiOption.priceUSD <= carRentalOption.priceUSD ? carRentalOption : taxiOption;

      return {
        sequence,
        description,
        from: from.name,
        to: to.name,
        distance: distanceKm,
        duration: durationMinutes,
        primaryOption,
        alternativeOption,
        recommendedMode: primaryOption.mode
      };

    } catch (error) {
      console.error(`Error calculating segment ${description}:`, error);
      
      // Return fallback calculation
      return this.getFallbackSegment(from, to, description, sequence);
    }
  }

  /**
   * Get route data from OSRM API
   * @param {Object} fromCoords - Origin coordinates {lat, lng}
   * @param {Object} toCoords - Destination coordinates {lat, lng}
   * @returns {Promise<Object>} Route data with distance and duration
   */
  async getOSRMRoute(fromCoords, toCoords) {
    const url = `${this.OSRM_BASE_URL}/${fromCoords.lng},${fromCoords.lat};${toCoords.lng},${toCoords.lat}?overview=false&alternatives=false&steps=false`;
    
    try {
      const response = await fetch(url);
      if (!response.ok) throw new Error(`OSRM API error: ${response.status}`);
      
      const data = await response.json();
      if (!data.routes || data.routes.length === 0) {
        throw new Error('No route found');
      }

      return {
        distance: data.routes[0].distance, // meters
        duration: data.routes[0].duration  // seconds
      };

    } catch (error) {
      console.warn('OSRM API failed, using fallback calculation:', error);
      
      // Fallback to straight-line distance with road factor
      const straightDistance = this.calculateStraightLineDistance(fromCoords, toCoords);
      return {
        distance: straightDistance * 1.3, // Apply road factor
        duration: (straightDistance * 1.3 / 1000) / 30 * 3600 // Assume 30 km/h average speed
      };
    }
  }

  /**
   * Calculate straight-line distance between two coordinates
   * @param {Object} from - Origin coordinates {lat, lng}
   * @param {Object} to - Destination coordinates {lat, lng}
   * @returns {number} Distance in meters
   */
  calculateStraightLineDistance(from, to) {
    const R = 6371000; // Earth's radius in meters
    const φ1 = from.lat * Math.PI / 180;
    const φ2 = to.lat * Math.PI / 180;
    const Δφ = (to.lat - from.lat) * Math.PI / 180;
    const Δλ = (to.lng - from.lng) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * Calculate walking option
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Object} Walking transport option
   */
  calculateWalkingOption(distanceKm, durationMinutes) {
    const walkingDuration = (distanceKm / this.WALKING_SPEED_KMH) * 60;

    return {
      mode: 'walking',
      modeName: 'Walking',
      icon: 'fa-walking',
      priceUSD: 0,
      priceMAD: 0,
      distanceKm: Math.round(distanceKm * 100) / 100,
      durationMinutes: Math.round(walkingDuration),
      description: `Walking (${Math.round(walkingDuration)} min)`,
      reasoning: 'Short distance - walking is most economical and healthy',
      isRecommended: true
    };
  }

  /**
   * Calculate taxi option
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Object} Taxi transport option
   */
  calculateTaxiOption(distanceKm, durationMinutes) {
    const distanceMeters = distanceKm * 1000;
    const taxiCostMAD = (this.TAXI_BASE_FARE_MAD + (distanceMeters / 80.0) * this.TAXI_RATE_PER_80M_MAD) * this.TAXI_MARGIN;
    const taxiCostUSD = taxiCostMAD / this.USD_TO_MAD_RATE;

    return {
      mode: 'taxi',
      modeName: 'Taxi',
      icon: 'fa-taxi',
      priceUSD: Math.round(taxiCostUSD * 100) / 100,
      priceMAD: Math.round(taxiCostMAD * 100) / 100,
      distanceKm: Math.round(distanceKm * 100) / 100,
      durationMinutes: Math.round(durationMinutes),
      description: `Taxi - $${(Math.round(taxiCostUSD * 100) / 100).toFixed(2)}`,
      reasoning: 'No parking hassles, convenient',
      isRecommended: false
    };
  }

  /**
   * Calculate car rental option
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Object} Car rental transport option
   */
  calculateCarRentalOption(distanceKm, durationMinutes) {
    // Calculate fuel cost (using gasoline as default)
    const fuelCostUSD = (distanceKm / this.FUEL_EFFICIENCY_KMPL) * this.GASOLINE_PRICE_USD;

    // Prorate daily rental fee (assume 6 segments per day)
    const proratedRentalFeeUSD = this.DAILY_CAR_RENTAL_USD / 6.0;

    const totalCostUSD = fuelCostUSD + proratedRentalFeeUSD;
    const totalCostMAD = totalCostUSD * this.USD_TO_MAD_RATE;

    return {
      mode: 'car_rental',
      modeName: 'Car Rental',
      icon: 'fa-car',
      priceUSD: Math.round(totalCostUSD * 100) / 100,
      priceMAD: Math.round(totalCostMAD * 100) / 100,
      distanceKm: Math.round(distanceKm * 100) / 100,
      durationMinutes: Math.round(durationMinutes),
      fuelCostUSD: Math.round(fuelCostUSD * 100) / 100,
      rentalFeeUSD: Math.round(proratedRentalFeeUSD * 100) / 100,
      description: `Car Rental - $${(Math.round(totalCostUSD * 100) / 100).toFixed(2)} (includes rental fee)`,
      reasoning: 'Includes $20/day rental fee, fuel efficient',
      isRecommended: false
    };
  }

  /**
   * Get fallback segment calculation when OSRM fails
   * @param {Object} from - Origin location
   * @param {Object} to - Destination location
   * @param {string} description - Segment description
   * @param {number} sequence - Segment sequence
   * @returns {Object} Fallback transport segment
   */
  getFallbackSegment(from, to, description, sequence) {
    const fallbackDistance = 2.0; // 2km fallback
    const fallbackDuration = 8; // 8 minutes fallback

    const taxiOption = this.calculateTaxiOption(fallbackDistance, fallbackDuration);
    const carRentalOption = this.calculateCarRentalOption(fallbackDistance, fallbackDuration);

    const primaryOption = taxiOption.priceUSD <= carRentalOption.priceUSD ? taxiOption : carRentalOption;
    const alternativeOption = taxiOption.priceUSD <= carRentalOption.priceUSD ? carRentalOption : taxiOption;

    return {
      sequence,
      description,
      from: from.name,
      to: to.name,
      distance: fallbackDistance,
      duration: fallbackDuration,
      primaryOption,
      alternativeOption,
      recommendedMode: primaryOption.mode,
      isFallback: true
    };
  }

  /**
   * Calculate inter-city transport between cities
   * @param {Array} days - Array of day data
   * @returns {Promise<Array>} Inter-city transport calculations
   */
  async calculateInterCityTransport(days) {
    const interCityTransports = [];

    // Group days by city to identify city changes
    const cityGroups = this.groupDaysByCity(days);

    for (let i = 0; i < cityGroups.length - 1; i++) {
      const currentCityGroup = cityGroups[i];
      const nextCityGroup = cityGroups[i + 1];

      // Get last day of current city and first day of next city
      const lastDayCurrentCity = currentCityGroup.days[currentCityGroup.days.length - 1];
      const firstDayNextCity = nextCityGroup.days[0];

      console.log(`🏙️ Calculating inter-city transport: ${currentCityGroup.cityName} → ${nextCityGroup.cityName}`);

      const interCityTransport = await this.calculateInterCitySegment(
        lastDayCurrentCity,
        firstDayNextCity,
        currentCityGroup.cityName,
        nextCityGroup.cityName
      );

      interCityTransports.push(interCityTransport);
    }

    return interCityTransports;
  }

  /**
   * Group days by city name
   * @param {Array} days - Array of day data
   * @returns {Array} Array of city groups with days
   */
  groupDaysByCity(days) {
    const cityGroups = [];
    let currentGroup = null;

    for (const day of days) {
      if (!currentGroup || currentGroup.cityName !== day.cityName) {
        currentGroup = {
          cityName: day.cityName,
          days: []
        };
        cityGroups.push(currentGroup);
      }
      currentGroup.days.push(day);
    }

    return cityGroups;
  }

  /**
   * Calculate inter-city transport segment
   * @param {Object} fromDay - Last day of current city
   * @param {Object} toDay - First day of next city
   * @param {string} fromCity - Current city name
   * @param {string} toCity - Next city name
   * @returns {Promise<Object>} Inter-city transport calculation
   */
  async calculateInterCitySegment(fromDay, toDay, fromCity, toCity) {
    try {
      const fromLocation = fromDay.accommodation;
      const toLocation = toDay.accommodation;

      if (!fromLocation || !toLocation) {
        throw new Error('Missing accommodation data for inter-city transport');
      }

      // Get route data for car rental option
      const routeData = await this.getOSRMRoute(fromLocation.coordinates, toLocation.coordinates);
      const distanceKm = routeData.distance / 1000;
      const durationMinutes = routeData.duration / 60;

      // Calculate car rental option
      const carRentalOption = this.calculateInterCityCarRental(distanceKm, durationMinutes);

      // Check for train options
      const trainOptions = await this.getTrainOptions(fromCity, toCity);

      let primaryOption, alternativeOptions;

      if (trainOptions.length > 0) {
        // Compare all options and select most economical
        const allOptions = [carRentalOption, ...trainOptions];
        allOptions.sort((a, b) => a.priceUSD - b.priceUSD);

        primaryOption = allOptions[0];
        alternativeOptions = allOptions.slice(1);
      } else {
        // Only car rental available
        primaryOption = carRentalOption;
        alternativeOptions = [];
      }

      return {
        fromCity,
        toCity,
        fromDay: fromDay.dayNumber,
        toDay: toDay.dayNumber,
        distance: distanceKm,
        duration: durationMinutes,
        primaryOption,
        alternativeOptions,
        recommendedMode: primaryOption.mode,
        description: `${fromCity} → ${toCity}`,
        isInterCity: true
      };

    } catch (error) {
      console.error(`Error calculating inter-city transport ${fromCity} → ${toCity}:`, error);

      // Return fallback inter-city calculation
      return this.getFallbackInterCityTransport(fromDay, toDay, fromCity, toCity);
    }
  }

  /**
   * Calculate inter-city car rental option
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Object} Inter-city car rental option
   */
  calculateInterCityCarRental(distanceKm, durationMinutes) {
    // For inter-city, use full daily rental fee
    const fuelCostUSD = (distanceKm / this.FUEL_EFFICIENCY_KMPL) * this.GASOLINE_PRICE_USD;
    const totalCostUSD = fuelCostUSD + this.DAILY_CAR_RENTAL_USD;
    const totalCostMAD = totalCostUSD * this.USD_TO_MAD_RATE;

    return {
      mode: 'car_rental',
      modeName: 'Car Rental',
      icon: 'fa-car',
      priceUSD: Math.round(totalCostUSD * 100) / 100,
      priceMAD: Math.round(totalCostMAD * 100) / 100,
      distanceKm: Math.round(distanceKm * 100) / 100,
      durationMinutes: Math.round(durationMinutes),
      fuelCostUSD: Math.round(fuelCostUSD * 100) / 100,
      rentalFeeUSD: this.DAILY_CAR_RENTAL_USD,
      description: `Car Rental - $${(Math.round(totalCostUSD * 100) / 100).toFixed(2)} (includes full daily fee)`,
      reasoning: 'Flexible schedule, door-to-door convenience',
      isRecommended: false
    };
  }

  /**
   * Get train options between cities
   * @param {string} fromCity - Origin city
   * @param {string} toCity - Destination city
   * @returns {Promise<Array>} Array of train options
   */
  async getTrainOptions(fromCity, toCity) {
    try {
      // For now, return empty array since train_lines table doesn't exist
      // TODO: Implement proper train data query when train system is ready
      console.log(`🚂 Train data not available yet for ${fromCity} → ${toCity}`);
      return [];

      // Future implementation when train_lines table exists:
      /*
      const { data: trainData, error } = await supabase
        .from('train_lines')
        .select('*')
        .or(`from_city.ilike.%${fromCity}%,to_city.ilike.%${fromCity}%`)
        .or(`from_city.ilike.%${toCity}%,to_city.ilike.%${toCity}%`);

      if (error) throw error;
      */

      // This code will be used when train_lines table is implemented
      /*
      const exactRoute = trainData.find(train =>
        (train.from_city.toLowerCase().includes(fromCity.toLowerCase()) &&
         train.to_city.toLowerCase().includes(toCity.toLowerCase())) ||
        (train.from_city.toLowerCase().includes(toCity.toLowerCase()) &&
         train.to_city.toLowerCase().includes(fromCity.toLowerCase()))
      );

      if (!exactRoute) {
        console.log(`🚂 No train route found between ${fromCity} and ${toCity}`);
        return [];
      }

      const trainOptions = [];

      // Add Comfort 1 option if available
      if (exactRoute.comfort_1_price_usd) {
        trainOptions.push({
          mode: 'train',
          modeName: 'Train (Comfort 1)',
          icon: 'fa-train',
          priceUSD: exactRoute.comfort_1_price_usd,
          priceMAD: exactRoute.comfort_1_price_usd * this.USD_TO_MAD_RATE,
          distanceKm: exactRoute.distance_km,
          durationMinutes: exactRoute.duration_minutes,
          description: `Train Comfort 1 - $${exactRoute.comfort_1_price_usd.toFixed(2)}`,
          reasoning: 'Premium comfort, reliable schedule',
          isRecommended: false,
          trainClass: 'comfort_1'
        });
      }

      // Add Comfort 2 option if available
      if (exactRoute.comfort_2_price_usd) {
        trainOptions.push({
          mode: 'train',
          modeName: 'Train (Comfort 2)',
          icon: 'fa-train',
          priceUSD: exactRoute.comfort_2_price_usd,
          priceMAD: exactRoute.comfort_2_price_usd * this.USD_TO_MAD_RATE,
          distanceKm: exactRoute.distance_km,
          durationMinutes: exactRoute.duration_minutes,
          description: `Train Comfort 2 - $${exactRoute.comfort_2_price_usd.toFixed(2)}`,
          reasoning: 'Good value, reliable schedule',
          isRecommended: false,
          trainClass: 'comfort_2'
        });
      }

      console.log(`🚂 Found ${trainOptions.length} train options for ${fromCity} → ${toCity}`);
      return trainOptions;
      */

    } catch (error) {
      console.error('Error getting train options:', error);
      return [];
    }
  }

  /**
   * Get fallback inter-city transport
   * @param {Object} fromDay - Origin day
   * @param {Object} toDay - Destination day
   * @param {string} fromCity - Origin city
   * @param {string} toCity - Destination city
   * @returns {Object} Fallback inter-city transport
   */
  getFallbackInterCityTransport(fromDay, toDay, fromCity, toCity) {
    const fallbackDistance = 200; // 200km fallback for inter-city
    const fallbackDuration = 180; // 3 hours fallback

    const carRentalOption = this.calculateInterCityCarRental(fallbackDistance, fallbackDuration);

    return {
      fromCity,
      toCity,
      fromDay: fromDay.dayNumber,
      toDay: toDay.dayNumber,
      distance: fallbackDistance,
      duration: fallbackDuration,
      primaryOption: carRentalOption,
      alternativeOptions: [],
      recommendedMode: carRentalOption.mode,
      description: `${fromCity} → ${toCity}`,
      isInterCity: true,
      isFallback: true
    };
  }

  /**
   * Calculate budget allocations for transport
   * @param {Array} dailyTransport - Daily transport calculations
   * @param {Array} interCityTransport - Inter-city transport calculations
   * @returns {Object} Budget allocation data
   */
  calculateBudgetAllocations(dailyTransport, interCityTransport) {
    const dailyAllocations = [];
    let tripLevelAllocation = 0;

    // Calculate daily allocations
    for (const day of dailyTransport) {
      const dailyAllocation = {
        dayNumber: day.dayNumber,
        allocatedAmount: day.totalCostUSD,
        spentAmount: day.totalCostUSD, // Same as allocated for primary recommendations
        currency: 'USD'
      };

      dailyAllocations.push(dailyAllocation);
      tripLevelAllocation += day.totalCostUSD;
    }

    // Add inter-city transport to trip level
    for (const interCity of interCityTransport) {
      tripLevelAllocation += interCity.primaryOption.priceUSD;
    }

    return {
      dailyAllocations,
      tripLevelAllocation,
      currency: 'USD'
    };
  }
}
