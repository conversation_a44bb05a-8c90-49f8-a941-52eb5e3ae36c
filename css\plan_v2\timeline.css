/* ===== TIMELINE COMPONENT STYLES ===== */

.timeline-container {
  padding: var(--spacing-6);
}

.timeline-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.timeline-header h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.timeline-header p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 80px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-full);
}

.timeline-item {
  position: relative;
  display: grid;
  grid-template-columns: 60px 40px 1fr;
  gap: var(--spacing-4);
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-6);
}

.timeline-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
}

.timeline-time {
  text-align: right;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  padding: var(--spacing-2);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.timeline-icon {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: var(--white);
  z-index: 2;
  box-shadow: 0 0 0 4px var(--white), var(--shadow-md);
}

.timeline-icon.accommodation {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.timeline-icon.transport {
  background: linear-gradient(135deg, var(--info-color), #1e40af);
}

.timeline-icon.meal {
  background: linear-gradient(135deg, var(--accent-color), #d97706);
}

.timeline-icon.activity {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.timeline-content {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  position: relative;
}

.timeline-content:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid var(--white);
}

.timeline-content h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.timeline-content p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-relaxed);
}

.timeline-category {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--gray-100);
  color: var(--gray-700);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timeline-category.accommodation {
  background: var(--primary-light);
  color: var(--primary-color);
}

.timeline-category.transport {
  background: var(--info-light);
  color: var(--info-color);
}

.timeline-category.meal {
  background: var(--accent-light);
  color: var(--accent-color);
}

.timeline-category.activity {
  background: var(--success-light);
  color: var(--success-color);
}

/* Transport specific styles */
.transport-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.transport-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.transport-icon {
  width: 50px;
  height: 50px;
  background: var(--info-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-lg);
}

.transport-info {
  flex: 1;
}

.transport-info h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.transport-route {
  color: var(--gray-600);
  font-size: var(--font-size-base);
  margin-bottom: 0;
}

.transport-status .status-badge {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.transport-details {
  padding: var(--spacing-6);
}

.transport-timeline {
  margin-bottom: var(--spacing-6);
}

.transport-point {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.transport-point:last-child {
  margin-bottom: 0;
}

.point-time {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  min-width: 80px;
  text-align: center;
  padding: var(--spacing-2);
  background: var(--primary-light);
  border-radius: var(--radius-md);
}

.point-location strong {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.point-location p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

.transport-journey {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin: var(--spacing-4) 0;
  padding-left: 40px;
}

.journey-line {
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, var(--info-color), var(--info-light));
  border-radius: var(--radius-full);
}

.journey-info {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.duration,
.distance {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-weight: var(--font-weight-medium);
}

.transport-summary {
  background: var(--gray-50);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.transport-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.transport-specs {
  display: grid;
  gap: var(--spacing-2);
}

.spec-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.spec-item i {
  color: var(--gray-500);
  width: 16px;
}

/* ===== TRANSPORT INTEGRATION STYLES ===== */

.transport-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-bg-alt);
}

.transport-header h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.transport-header h3 i {
  color: var(--color-primary);
}

.transport-header p {
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-3);
}

.transport-summary {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
}

.segment-count,
.total-cost {
  background: var(--color-bg);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.segment-count {
  color: var(--color-text-muted);
}

.total-cost {
  color: var(--color-primary);
  background: var(--primary-light);
}

.transport-segments {
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.transport-card {
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
}

.transport-card:hover {
  box-shadow: var(--shadow-card-hover);
  transform: translateY(-2px);
}

.transport-route {
  padding: var(--spacing-4);
  background: var(--color-bg-alt);
  border-bottom: 1px solid var(--color-border);
}

.route-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.route-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.route-details .from,
.route-details .to {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

.route-details i {
  color: var(--color-primary);
  font-size: var(--font-size-xs);
}

.route-stats {
  display: flex;
  gap: var(--spacing-3);
}

.distance,
.duration {
  background: var(--color-bg);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-muted);
}

.transport-option {
  padding: var(--spacing-4);
}

.transport-option.primary {
  background: var(--color-bg);
}

.transport-option.alternative {
  background: var(--color-bg-alt);
  border-top: 1px solid var(--color-border);
}

.option-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-2);
}

.option-header i {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

.transport-option.primary .option-header i {
  background: var(--color-primary);
  color: white;
}

.transport-option.alternative .option-header i {
  background: var(--color-text-muted);
  color: white;
}

.mode-name {
  flex: 1;
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.price {
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

.option-details {
  margin-left: 32px;
}

.reasoning {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
  font-style: italic;
}

/* Inter-city transport styles */
.inter-city-transport {
  margin-top: var(--spacing-6);
  border-top: 2px solid var(--color-border);
}

.inter-city-header {
  padding: var(--spacing-6);
  background: var(--color-bg-accent);
}

.inter-city-header h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.inter-city-header h3 i {
  color: var(--info-color);
}

.inter-city-header p {
  color: var(--color-text-muted);
  font-size: var(--font-size-base);
  margin: 0;
}

.inter-city-card {
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  margin: var(--spacing-6);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.inter-city-route {
  padding: var(--spacing-4);
  background: linear-gradient(135deg, var(--info-light) 0%, var(--color-bg-alt) 100%);
  border-bottom: 1px solid var(--color-border);
}

.inter-city-route h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-3);
}

.inter-city-route .route-stats {
  justify-content: flex-start;
}

.inter-city-route .distance,
.inter-city-route .duration {
  background: var(--color-bg);
  color: var(--info-color);
  font-weight: var(--font-weight-semibold);
}

.transport-pricing {
  background: var(--gray-50);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-breakdown {
  display: grid;
  gap: var(--spacing-1);
}

.price-item {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.price-item.total {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  border-top: 1px solid var(--gray-300);
  padding-top: var(--spacing-1);
  margin-top: var(--spacing-1);
}

.transport-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* Timeline animations */
.timeline-item {
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInTimeline 0.6s ease-out forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.1s; }
.timeline-item:nth-child(2) { animation-delay: 0.2s; }
.timeline-item:nth-child(3) { animation-delay: 0.3s; }
.timeline-item:nth-child(4) { animation-delay: 0.4s; }
.timeline-item:nth-child(5) { animation-delay: 0.5s; }
.timeline-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInTimeline {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive timeline */
@media (max-width: 768px) {
  .timeline::before {
    left: 30px;
  }
  
  .timeline-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    text-align: center;
  }
  
  .timeline-time {
    order: -1;
    text-align: center;
    margin-bottom: var(--spacing-2);
  }
  
  .timeline-icon {
    margin: 0 auto var(--spacing-3);
  }
  
  .timeline-content::before {
    display: none;
  }
  
  .transport-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }
  
  .transport-point {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }
  
  .transport-journey {
    padding-left: 0;
    justify-content: center;
  }
  
  .transport-pricing {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
}
