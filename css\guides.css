/* Guides page specific styles */

/* Guide cards */
.guide-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  overflow: hidden;
  position: relative;
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
}

.guide-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.guide-card__image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: var(--shadow-sm);
  transition: transform 0.3s ease;
}

.dark-mode .guide-card__image {
  border-color: var(--color-border);
}

.guide-card:hover .guide-card__image {
  transform: scale(1.05);
}

.guide-card__rating {
  display: flex;
  align-items: center;
}

.guide-card__rating .fa-star {
  color: #f59e0b;
  margin-right: 0.25rem;
}

.guide-card__languages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.guide-card__language {
  background-color: var(--color-input-bg);
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-full);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.guide-card__contact {
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: var(--border-radius-full);
  font-weight: 500;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.guide-card__contact:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
}

.guide-card__contact:active {
  transform: translateY(0);
}

/* Filter buttons */
.filter-btn {
  transition: all 0.3s ease;
}

.filter-btn:hover {
  transform: translateY(-2px);
}

.filter-btn.active {
  background-color: var(--color-primary) !important;
  color: white !important;
}

/* Modal styles */
#guide-detail-modal {
  transition: opacity 0.3s ease;
}

#guide-detail-modal.opacity-100 {
  opacity: 1;
}

#modal-content {
  transition: transform 0.3s ease, opacity 0.3s ease, background-color 0.3s ease;
}

#guide-detail-modal.opacity-100 #modal-content.scale-100 {
  transform: scale(1);
  opacity: 1;
}

.modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: color 0.3s ease, transform 0.2s ease;
}

.modal-close:hover {
  color: var(--color-error);
  transform: rotate(90deg);
}

.guide-detail__image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: var(--shadow-md);
}

.dark-mode .guide-detail__image {
  border-color: var(--color-border);
}

.guide-detail__rating {
  display: flex;
  align-items: center;
}

.guide-detail__rating .fa-star {
  color: #f59e0b;
}

.guide-detail__specialties {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.guide-detail__specialty {
  display: flex;
  align-items: center;
}

.guide-detail__specialty i {
  color: var(--color-primary);
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.guide-detail__form {
  background-color: var(--color-input-bg);
  border-radius: var(--border-radius-xl);
  padding: 1.5rem;
  transition: background-color 0.3s ease;
}

.guide-detail__form-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text);
  transition: color 0.3s ease;
}

.guide-detail__form-submit {
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: var(--border-radius-lg);
  font-weight: 500;
  width: 100%;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.guide-detail__form-submit:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
}

.guide-detail__form-submit:active {
  transform: translateY(0);
}

/* Loader animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.guides-loading {
  animation: pulse 1.5s infinite;
}

/* Guide card counter */
.guides-count {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin-bottom: 1rem;
  text-align: center;
  transition: color 0.3s ease;
}

/* No guides found message */
.no-guides {
  grid-column: span 2;
  text-align: center;
  padding: 3rem 0;
}

.no-guides p {
  font-size: 1.25rem;
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

.no-guides button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-full);
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.no-guides button:hover {
  background-color: var(--color-primary-hover);
}

/* Fade in animation for guide cards */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sequential animation delay for guide cards */
.guide-card:nth-child(1) {
  animation-delay: 0.1s;
}

.guide-card:nth-child(2) {
  animation-delay: 0.2s;
}

.guide-card:nth-child(3) {
  animation-delay: 0.3s;
}

.guide-card:nth-child(4) {
  animation-delay: 0.4s;
}

.guide-card:nth-child(5) {
  animation-delay: 0.5s;
}

.guide-card:nth-child(6) {
  animation-delay: 0.6s;
}

/* User authentication styles */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-primary);
  transition: transform 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
}

.auth-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Dark mode specific guide card adjustments */
.dark-mode .guide-card {
  border-color: var(--color-border);
}

.dark-mode .guide-card__language {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .guide-detail__specialties {
    grid-template-columns: 1fr;
  }
  
  .filter-btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

/* City name loading state */
[data-city-name]:empty::before,
[data-city-name="Loading..."] {
  content: "Loading...";
  color: var(--color-primary);
  animation: pulse 1.5s infinite;
}

/* Only show loading animation for initially empty elements */
[data-city-name]:not(:empty) {
  animation: none;
}

/* Header auth styles */
#nav-actions a img,
#mobile-nav-actions a img {
  object-fit: cover;
  transition: transform 0.2s ease;
}

#nav-actions a:hover img,
#mobile-nav-actions a:hover img {
  transform: scale(1.1);
}

/* Fix for header auth links alignment */
#nav-actions a.flex {
  display: flex;
  align-items: center;
}

#nav-actions .hidden.sm\:inline {
  margin-left: 0.5rem;
}