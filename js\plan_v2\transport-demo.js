/**
 * Transport System Demo
 * 
 * Demonstration script showing how to integrate the comprehensive transport
 * calculation system with the plan_v2 page.
 * 
 * This script can be used to test the transport system and as a reference
 * for full integration.
 */

import { TransportService } from '../services/transport-service.js';
import { TransportIntegration } from './components/transport-integration.js';

class TransportDemo {
  constructor() {
    this.transportService = new TransportService();
    this.transportIntegration = null;
    this.currentTripId = null;
  }

  /**
   * Initialize transport demo
   * @param {number} tripId - Trip ID to demonstrate with
   */
  async initialize(tripId) {
    console.log(`🚀 TransportDemo: Initializing for trip ${tripId}`);
    
    this.currentTripId = tripId;
    
    try {
      // Create mock state manager for demo
      const mockStateManager = this.createMockStateManager();
      
      // Initialize transport integration
      this.transportIntegration = new TransportIntegration(mockStateManager);
      await this.transportIntegration.initialize(tripId);
      
      // Add demo controls to page
      this.addDemoControls();
      
      console.log('✅ Transport demo initialized successfully');
      
    } catch (error) {
      console.error('❌ Error initializing transport demo:', error);
    }
  }

  /**
   * Create mock state manager for demo purposes
   * @returns {Object} Mock state manager
   */
  createMockStateManager() {
    const state = {};
    
    return {
      setState: (key, value) => {
        console.log(`🔄 State updated: ${key} =`, value);
        state[key] = value;
      },
      
      getState: (key) => {
        return state[key];
      },
      
      subscribe: (key, callback) => {
        console.log(`👂 Subscribed to state: ${key}`);
        return () => console.log(`🔇 Unsubscribed from state: ${key}`);
      }
    };
  }

  /**
   * Add demo controls to the page
   */
  addDemoControls() {
    // Check if controls already exist
    if (document.getElementById('transport-demo-controls')) {
      return;
    }

    const controlsContainer = document.createElement('div');
    controlsContainer.id = 'transport-demo-controls';
    controlsContainer.className = 'transport-demo-controls';
    
    controlsContainer.innerHTML = `
      <div class="demo-header">
        <h3>🚗 Transport System Demo</h3>
        <p>Test the comprehensive transport calculation system</p>
      </div>
      
      <div class="demo-actions">
        <button id="calculate-transport-btn" class="btn btn-primary">
          <i class="fas fa-calculator"></i>
          Calculate Transport
        </button>
        
        <button id="recalculate-transport-btn" class="btn btn-outline">
          <i class="fas fa-redo"></i>
          Recalculate
        </button>
        
        <button id="show-status-btn" class="btn btn-outline">
          <i class="fas fa-info-circle"></i>
          Show Status
        </button>
        
        <button id="clear-transport-btn" class="btn btn-outline" style="color: #ef4444; border-color: #ef4444;">
          <i class="fas fa-trash"></i>
          Clear Data
        </button>
      </div>
      
      <div id="demo-output" class="demo-output">
        <p>Click "Calculate Transport" to start the demonstration.</p>
      </div>
    `;

    // Add styles
    const styles = document.createElement('style');
    styles.textContent = `
      .transport-demo-controls {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 350px;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        z-index: 1000;
        font-family: Inter, sans-serif;
      }
      
      .demo-header {
        padding: 16px;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;
        border-radius: 12px 12px 0 0;
      }
      
      .demo-header h3 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
      
      .demo-header p {
        margin: 0;
        font-size: 14px;
        color: #6b7280;
      }
      
      .demo-actions {
        padding: 16px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
      }
      
      .demo-actions .btn {
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 8px;
        border: 1px solid;
        background: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
      }
      
      .demo-actions .btn-primary {
        background: #F26522;
        color: white;
        border-color: #F26522;
      }
      
      .demo-actions .btn-primary:hover {
        background: #e05a1a;
      }
      
      .demo-actions .btn-outline {
        color: #6b7280;
        border-color: #d1d5db;
      }
      
      .demo-actions .btn-outline:hover {
        background: #f3f4f6;
      }
      
      .demo-output {
        padding: 16px;
        border-top: 1px solid #e5e7eb;
        background: #f9fafb;
        border-radius: 0 0 12px 12px;
        max-height: 300px;
        overflow-y: auto;
      }
      
      .demo-output p {
        margin: 0 0 8px 0;
        font-size: 13px;
        line-height: 1.4;
      }
      
      .demo-output .success {
        color: #059669;
      }
      
      .demo-output .error {
        color: #dc2626;
      }
      
      .demo-output .info {
        color: #2563eb;
      }
      
      @media (max-width: 768px) {
        .transport-demo-controls {
          position: relative;
          top: auto;
          right: auto;
          width: 100%;
          margin: 20px 0;
        }
      }
    `;
    
    document.head.appendChild(styles);

    // Insert controls at the top of the page
    const container = document.querySelector('.container') || document.body;
    container.insertBefore(controlsContainer, container.firstChild);

    // Add event listeners
    this.addDemoEventListeners();
  }

  /**
   * Add event listeners for demo controls
   */
  addDemoEventListeners() {
    const calculateBtn = document.getElementById('calculate-transport-btn');
    const recalculateBtn = document.getElementById('recalculate-transport-btn');
    const statusBtn = document.getElementById('show-status-btn');
    const clearBtn = document.getElementById('clear-transport-btn');
    const output = document.getElementById('demo-output');

    const updateOutput = (message, type = 'info') => {
      const p = document.createElement('p');
      p.className = type;
      p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      output.appendChild(p);
      output.scrollTop = output.scrollHeight;
    };

    calculateBtn?.addEventListener('click', async () => {
      if (!this.currentTripId) {
        updateOutput('No trip ID set', 'error');
        return;
      }

      updateOutput('Starting transport calculation...', 'info');
      calculateBtn.disabled = true;
      calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calculating...';

      try {
        const result = await this.transportService.calculateAndSaveTransport(this.currentTripId);
        
        if (result.success) {
          updateOutput(`✅ Transport calculated successfully!`, 'success');
          updateOutput(`📊 ${result.summary.totalSegments} segments, $${result.summary.totalCostUSD.toFixed(2)} total`, 'success');
          
          // Load the data in integration component
          if (this.transportIntegration) {
            await this.transportIntegration.loadTransportData();
          }
        } else {
          updateOutput(`❌ Calculation failed: ${result.error}`, 'error');
        }
      } catch (error) {
        updateOutput(`❌ Error: ${error.message}`, 'error');
      } finally {
        calculateBtn.disabled = false;
        calculateBtn.innerHTML = '<i class="fas fa-calculator"></i> Calculate Transport';
      }
    });

    recalculateBtn?.addEventListener('click', async () => {
      if (!this.currentTripId) {
        updateOutput('No trip ID set', 'error');
        return;
      }

      updateOutput('Recalculating transport...', 'info');
      
      try {
        const result = await this.transportService.recalculateTransport(this.currentTripId);
        
        if (result.success) {
          updateOutput(`✅ Transport recalculated successfully!`, 'success');
        } else {
          updateOutput(`❌ Recalculation failed: ${result.error}`, 'error');
        }
      } catch (error) {
        updateOutput(`❌ Error: ${error.message}`, 'error');
      }
    });

    statusBtn?.addEventListener('click', async () => {
      if (!this.currentTripId) {
        updateOutput('No trip ID set', 'error');
        return;
      }

      try {
        const status = await this.transportService.getTransportStatus(this.currentTripId);
        
        if (status.success) {
          updateOutput(`📊 Transport Status:`, 'info');
          updateOutput(`   Has data: ${status.hasTransportData}`, 'info');
          updateOutput(`   Records: ${status.recordCount}`, 'info');
          if (status.lastCalculated) {
            updateOutput(`   Last calculated: ${new Date(status.lastCalculated).toLocaleString()}`, 'info');
          }
        } else {
          updateOutput(`❌ Status check failed: ${status.error}`, 'error');
        }
      } catch (error) {
        updateOutput(`❌ Error: ${error.message}`, 'error');
      }
    });

    clearBtn?.addEventListener('click', async () => {
      if (!this.currentTripId) {
        updateOutput('No trip ID set', 'error');
        return;
      }

      if (!confirm('Are you sure you want to clear all transport data? This cannot be undone.')) {
        return;
      }

      updateOutput('Clearing transport data...', 'info');
      
      try {
        await this.transportService.dataManager.clearExistingTransportData(this.currentTripId);
        updateOutput(`✅ Transport data cleared successfully!`, 'success');
      } catch (error) {
        updateOutput(`❌ Error clearing data: ${error.message}`, 'error');
      }
    });
  }

  /**
   * Remove demo controls
   */
  removeDemoControls() {
    const controls = document.getElementById('transport-demo-controls');
    if (controls) {
      controls.remove();
    }
  }
}

// Export for use in other scripts
window.TransportDemo = TransportDemo;

// Auto-initialize if trip ID is available in URL or global scope
document.addEventListener('DOMContentLoaded', () => {
  // Try to get trip ID from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const tripId = urlParams.get('trip_id') || window.tripId;
  
  if (tripId) {
    console.log(`🎯 Auto-initializing transport demo for trip ${tripId}`);
    const demo = new TransportDemo();
    demo.initialize(parseInt(tripId));
  } else {
    console.log('ℹ️ No trip ID found. Transport demo not initialized.');
    console.log('💡 To use the demo, add ?trip_id=YOUR_TRIP_ID to the URL or set window.tripId');
  }
});
