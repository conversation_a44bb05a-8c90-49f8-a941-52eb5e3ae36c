/* Transport Tab */
.transport-banner {
  position: relative;
  height: 250px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-6);
}

.transport-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.transport-banner:hover img {
  transform: scale(1.05);
}

.transport-banner-content {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
}

.transport-banner-content h2 {
  font-size: 1.875rem;
  margin-bottom: var(--spacing-2);
}

.transport-banner-content p {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-4);
}

.transport-total {
  display: inline-block;
  background-color: var(--color-primary);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-full);
  font-weight: 500;
}

.journey-map-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  margin-top: var(--spacing-8);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
}

.dark .journey-map-container {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
}

.journey-map-container:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
}

.journey-map {
  height: 400px;
  background-color: var(--color-gray-100);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-4);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.dark .journey-map {
  background-color: var(--color-gray-200);
}

.journey-map img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.journey-map:hover img {
  transform: scale(1.02);
}

.fullscreen-btn {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  background-color: white;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all var(--transition-fast);
  z-index: 5;
}

.dark .fullscreen-btn {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
}

.fullscreen-btn:hover {
  background-color: var(--color-gray-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dark .fullscreen-btn:hover {
  background-color: var(--color-gray-200);
}

.map-legend {
  display: flex;
  gap: var(--spacing-6);
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.legend-color {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}

.legend-color.blue {
  background-color: var(--color-accent);
}

.legend-color.green {
  background-color: var(--color-success);
}

.legend-color.orange {
  background-color: var(--color-primary);
}

.transport-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.transport-card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  opacity: 1;
  transform: translateY(0);
  transition: all var(--transition-normal);
  display: grid;
  grid-template-columns: auto 1fr;
}

.transport-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.dark .transport-card {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-300);
  color: var(--color-gray-800);
}

.transport-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 60px;
  background-color: var(--color-primary);
  color: white;
  font-size: 1.5rem;
  position: relative;
  overflow: hidden;
}

.transport-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, transparent 70%);
}

.transport-card:hover .transport-icon {
  transform: scale(1.05);
}

.transport-content {
  padding: 0;
  display: flex;
  flex-direction: column;
}

.transport-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
}

.dark .transport-header {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
}

.transport-header h3 {
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
  color: var(--color-gray-800);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dark .transport-header h3 {
  color: var(--color-gray-700);
}

.transport-type {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: var(--border-radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.transport-route {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--color-gray-700);
  padding: var(--spacing-3) var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dark .transport-route {
  color: var(--color-gray-500);
}

.transport-route::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: var(--color-accent);
  border-radius: var(--border-radius-sm);
}

.transport-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  padding: 0 var(--spacing-4) var(--spacing-3);
  color: var(--color-gray-600);
  font-size: 0.9rem;
}

.dark .transport-meta {
  color: var(--color-gray-500);
}

.transport-meta div {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.transport-meta i {
  color: var(--color-primary);
}

.transport-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  background-color: var(--color-gray-50);
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
  margin-top: auto;
}

.dark .transport-info {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
}

.transport-info .info-label {
  font-size: 0.8rem;
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dark .info-label {
  color: var(--color-gray-500);
}

.transport-info .info-value {
  font-weight: 500;
  color: var(--color-gray-800);
}

.dark .info-value {
  color: var(--color-gray-700);
}

.transport-price {
  padding: var(--spacing-3) var(--spacing-4);
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);
  border-top: 1px dashed var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.dark .transport-price {
  border-color: var(--color-gray-300);
  background-color: var(--color-gray-100);
}

.transport-price::before {
  content: 'Price:';
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--color-gray-600);
}

.transport-summary {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

.transport-tips {
  background-color: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.dark .transport-tips {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
  color: var(--color-gray-800);
}

.transport-tips:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.transport-tips h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.transport-tips h3::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 1.25rem;
  background-color: var(--color-accent);
  border-radius: var(--border-radius-sm);
}

.dark .transport-tips h3::before {
  background-color: var(--color-accent-light);
}

.transport-summary .summary-box {
  padding: var(--spacing-8);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow);
}

.transport-summary .summary-box:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-lg);
}

/* Carousel for Transport Tips */
#transport-carousel {
  position: relative;
  overflow: hidden;
  height: 12rem;
  margin-bottom: var(--spacing-4);
  background-color: white;
  border-radius: var(--border-radius-lg);
  color: var(--color-gray-800);
  text-align: center;
  border: 1px solid var(--color-gray-200);
}

.dark #transport-carousel {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
  border-color: var(--color-gray-300);
}

#transport-carousel .carousel-slide {
  position: absolute;
  inset: 0;
  padding: var(--spacing-4);
  opacity: 0;
  transition: opacity var(--transition-slow), transform var(--transition-slow);
  transform: translateX(100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#transport-carousel .carousel-slide.active {
  opacity: 1;
  transform: translateX(0);
}

#carousel-dots { /* Note: This ID is used by the transport carousel */
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
}

#transport-carousel .carousel-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
}

.dark #transport-carousel .carousel-dot {
  background-color: var(--color-gray-400);
}

#transport-carousel .carousel-dot.active {
  background-color: var(--color-primary);
  transform: scale(1.2);
}

#transport-carousel .carousel-dot:hover {
  background-color: var(--color-primary-light);
  transform: scale(1.1);
}

#transport-carousel .nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  color: var(--color-gray-600);
  background-color: transparent;
  border: none;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark #transport-carousel .nav-arrow {
  color: var(--color-gray-600);
}

#transport-carousel #prev-slide {
  left: 0.5rem;
}

#transport-carousel #next-slide {
  right: 0.5rem;
}

#transport-carousel .carousel-slide i {
  color: var(--color-primary);
  font-size: 2rem;
  margin-bottom: 1rem;
}

#transport-carousel .carousel-slide h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.summary-total {
  display: flex;
  justify-content: space-between;
  padding-top: var(--spacing-3);
  margin-top: var(--spacing-3);
  border-top: 1px solid var(--color-gray-200);
  font-weight: 700;
}

.dark .summary-total {
  border-color: var(--color-gray-300);
}

.summary-total span:last-child {
  color: var(--color-primary);
  font-size: 1.125rem;
}

/* Enhanced Transport Styles for New Integration */

/* Transport Loading States */
.transport-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  color: var(--color-gray-600);
}

.dark .transport-loading {
  color: var(--color-gray-500);
}

.transport-loading i {
  font-size: 2rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-4);
}

.transport-loading h3 {
  margin-bottom: var(--spacing-2);
  color: var(--color-gray-800);
}

.dark .transport-loading h3 {
  color: var(--color-gray-700);
}

/* Transport Section Headers */
.transport-section {
  margin-bottom: var(--spacing-8);
}

.transport-section-header {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--color-gray-200);
}

.dark .transport-section-header {
  border-color: var(--color-gray-300);
}

.transport-section-header h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.dark .transport-section-header h4 {
  color: var(--color-gray-700);
}

.transport-section-header p {
  color: var(--color-gray-600);
  margin: 0;
}

.dark .transport-section-header p {
  color: var(--color-gray-500);
}

/* Enhanced Transport Cards */
.transport-card.primary {
  border: 2px solid var(--color-primary);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.15);
}

.transport-card.alternative {
  border: 1px solid var(--color-gray-200);
  opacity: 0.9;
}

.dark .transport-card.alternative {
  border-color: var(--color-gray-300);
}

.transport-card.alternative:hover {
  opacity: 1;
  border-color: var(--color-primary);
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.primary {
  background-color: var(--color-primary);
  color: white;
}

.status-badge.alternative {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

.dark .status-badge.alternative {
  background-color: var(--color-gray-300);
  color: var(--color-gray-600);
}

/* Transport Route Styling */
.route-cities {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--color-gray-800);
}

.dark .route-cities {
  color: var(--color-gray-700);
}

.route-cities i {
  color: var(--color-primary);
}

.route-stations {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: var(--color-gray-600);
}

.dark .route-stations {
  color: var(--color-gray-500);
}

.route-locations {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.from-location,
.to-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: 0.9rem;
  color: var(--color-gray-700);
}

.dark .from-location,
.dark .to-location {
  color: var(--color-gray-600);
}

.from-location i,
.to-location i {
  color: var(--color-primary);
  font-size: 0.8rem;
}

/* Transport Details */
.transport-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-top: var(--spacing-3);
}

.transport-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-700);
}

.dark .transport-time {
  color: var(--color-gray-600);
}

.transport-time i {
  color: var(--color-primary);
}

.duration {
  color: var(--color-gray-500);
  font-size: 0.9rem;
}

.transport-class {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-700);
}

.dark .transport-class {
  color: var(--color-gray-600);
}

.transport-class i {
  color: var(--color-accent);
}

/* Transport Price Styling */
.transport-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
}

.price-amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-primary);
}

.price-currency {
  font-size: 0.8rem;
  color: var(--color-gray-500);
}

/* Transport Actions */
.transport-actions {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.dark .transport-actions {
  border-color: var(--color-gray-300);
  background-color: var(--color-gray-100);
}

.select-transport-btn {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--color-primary);
  background-color: transparent;
  color: var(--color-primary);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.select-transport-btn:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-1px);
}

.select-transport-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Transport Alternatives */
.transport-alternatives {
  margin-top: var(--spacing-4);
}

.transport-alternatives h5 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-3);
}

.dark .transport-alternatives h5 {
  color: var(--color-gray-600);
}

.toggle-alternatives {
  background: none;
  border: none;
  color: var(--color-primary);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  transition: all var(--transition-fast);
}

.toggle-alternatives:hover {
  color: var(--color-primary-dark);
  transform: translateX(2px);
}

.toggle-alternatives i {
  transition: transform var(--transition-fast);
}

/* Transport Sequence */
.transport-sequence {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px dashed var(--color-gray-200);
}

.dark .transport-sequence {
  border-color: var(--color-gray-300);
}

.transport-sequence:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Transport Summary Enhanced */
.transport-summary {
  background-color: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  margin-top: var(--spacing-8);
}

.dark .transport-summary {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
  color: var(--color-gray-800);
}

.summary-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-4);
}

.dark .summary-header h4 {
  color: var(--color-gray-700);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.summary-stat {
  text-align: center;
  padding: var(--spacing-4);
  background-color: var(--color-gray-50);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-gray-200);
}

.dark .summary-stat {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-300);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dark .stat-label {
  color: var(--color-gray-500);
}

.summary-breakdown {
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--spacing-4);
}

.dark .summary-breakdown {
  border-color: var(--color-gray-300);
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2) 0;
  color: var(--color-gray-700);
}

.dark .breakdown-item {
  color: var(--color-gray-600);
}

.breakdown-item:not(:last-child) {
  border-bottom: 1px solid var(--color-gray-100);
}

.dark .breakdown-item:not(:last-child) {
  border-color: var(--color-gray-200);
}

/* Error States */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  color: var(--color-gray-600);
}

.dark .error-state {
  color: var(--color-gray-500);
}

.error-state i {
  font-size: 2.5rem;
  color: var(--color-danger);
  margin-bottom: var(--spacing-4);
}

.error-state h3 {
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
}

.dark .error-state h3 {
  color: var(--color-gray-700);
}

.retry-transport-btn,
.retry-transport-day-btn {
  margin-top: var(--spacing-4);
  padding: var(--spacing-3) var(--spacing-6);
  border: 1px solid var(--color-primary);
  background-color: transparent;
  color: var(--color-primary);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.retry-transport-btn:hover,
.retry-transport-day-btn:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-1px);
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  color: var(--color-gray-600);
}

.dark .empty-state {
  color: var(--color-gray-500);
}

.empty-state i {
  font-size: 2.5rem;
  color: var(--color-gray-400);
  margin-bottom: var(--spacing-4);
}

.dark .empty-state i {
  color: var(--color-gray-500);
}

.empty-state h3 {
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
}

.dark .empty-state h3 {
  color: var(--color-gray-700);
}

/* Show More Button */
.show-more-alternatives {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px dashed var(--color-gray-300);
  background-color: transparent;
  color: var(--color-gray-600);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-top: var(--spacing-3);
}

.dark .show-more-alternatives {
  border-color: var(--color-gray-400);
  color: var(--color-gray-500);
}

.show-more-alternatives:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

/* Timeline styles for transport integration */
.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-lg);
  background-color: white;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.timeline-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.timeline-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.timeline-icon.transport {
  background-color: var(--color-accent);
}

.timeline-icon.hotel {
  background-color: var(--color-primary);
}

.timeline-icon.meal {
  background-color: var(--color-warning);
}

.timeline-icon.activity {
  background-color: var(--color-success);
}

.timeline-details {
  flex: 1;
}

.timeline-time {
  font-size: 0.9rem;
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-1);
  font-weight: 500;
}

.timeline-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-1);
}

.timeline-subtitle {
  font-size: 0.9rem;
  color: var(--color-gray-600);
  line-height: 1.4;
}

.timeline-distance {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: 0.85rem;
  color: var(--color-gray-500);
  margin-top: var(--spacing-1);
}

.timeline-distance i {
  color: var(--color-primary);
}