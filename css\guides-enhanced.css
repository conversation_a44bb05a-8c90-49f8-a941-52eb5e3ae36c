/* Enhanced Guides page styles - Beautiful invitation-style design */

/* Hero section enhancements */
.hero-section {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url('https://images.unsplash.com/photo-1493246507139-91e8fad9978e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.4) 0%, rgba(15, 23, 42, 0.7) 100%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

.hero-description {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1.2s ease-out;
}

/* Guide cards as beautiful invitations */
.guide-card {
  background-color: var(--color-card-bg);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s ease;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
  border: 1px solid rgba(249, 115, 22, 0.1);
}

.dark-mode .guide-card {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(249, 115, 22, 0.2);
}

.guide-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #f97316, #fb923c);
  z-index: 1;
}

.guide-card::after {
  content: '';
  position: absolute;
  top: 6px;
  right: 20px;
  width: 30px;
  height: 30px;
  background-color: var(--color-card-bg);
  border-radius: 50%;
  box-shadow: inset 0 0 0 1px rgba(249, 115, 22, 0.3);
  z-index: 1;
  transition: all 0.4s ease;
}

.dark-mode .guide-card::after {
  background-color: #1f2937;
  box-shadow: inset 0 0 0 1px rgba(249, 115, 22, 0.5);
}

.guide-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark-mode .guide-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

.guide-card:hover::after {
  transform: scale(1.2);
  box-shadow: inset 0 0 0 1px rgba(249, 115, 22, 0.6);
}

.guide-card-inner {
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.guide-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.guide-profile-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #fff;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.4s ease;
  position: relative;
}

.dark-mode .guide-profile-image {
  border-color: #374151;
}

.guide-card:hover .guide-profile-image {
  transform: scale(1.05);
  border-color: #f97316;
}

.guide-info {
  margin-left: 1.25rem;
  flex: 1;
}

.guide-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.guide-specialization {
  color: #f97316;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.guide-languages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.guide-language {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  background-color: rgba(249, 115, 22, 0.1);
  color: #f97316;
  transition: all 0.3s ease;
}

.dark-mode .guide-language {
  background-color: rgba(249, 115, 22, 0.2);
}

.guide-card:hover .guide-language {
  background-color: rgba(249, 115, 22, 0.2);
}

.dark-mode .guide-card:hover .guide-language {
  background-color: rgba(249, 115, 22, 0.3);
}

.guide-description {
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  transition: color 0.3s ease;
}

.guide-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px dashed rgba(249, 115, 22, 0.3);
}

.guide-stats {
  display: flex;
  gap: 1rem;
}

.guide-stat {
  display: flex;
  align-items: center;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.guide-stat i {
  color: #f97316;
  margin-right: 0.5rem;
}

.guide-action {
  background-color: #f97316;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3), 0 2px 4px -1px rgba(249, 115, 22, 0.1);
}

.guide-action:hover {
  background-color: #ea580c;
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(249, 115, 22, 0.3), 0 4px 6px -2px rgba(249, 115, 22, 0.1);
}

.guide-action:active {
  transform: translateY(0);
}

.guide-action i {
  margin-left: 0.5rem;
}

/* Rating badge */
.rating-badge {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: linear-gradient(135deg, #f97316, #fb923c);
  color: white;
  border-radius: 9999px;
  padding: 0.5rem 0.75rem;
  font-weight: 700;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3), 0 2px 4px -1px rgba(249, 115, 22, 0.1);
  transition: all 0.3s ease;
}

.guide-card:hover .rating-badge {
  transform: scale(1.1);
}

.rating-badge i {
  color: #fff;
  margin-right: 0.25rem;
}

/* Filter buttons */
.filter-container {
  position: relative;
  z-index: 10;
  margin-top: -2rem;
  margin-bottom: 2rem;
  background-color: var(--color-card-bg);
  border-radius: 9999px;
  padding: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  display: inline-flex;
  transition: background-color 0.3s ease;
}

.dark-mode .filter-container {
  background-color: #1f2937;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  background-color: transparent;
  color: var(--color-text-secondary);
}

.filter-btn:hover {
  color: var(--color-text);
  transform: translateY(-2px);
}

.filter-btn.active {
  background-color: #f97316 !important;
  color: white !important;
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3), 0 2px 4px -1px rgba(249, 115, 22, 0.1);
}

/* Enhanced modal */
.guide-modal {
  background-color: var(--color-card-bg);
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s ease;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
}

.dark-mode .guide-modal {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.guide-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, #f97316, #fb923c);
  z-index: 1;
}

.modal-close-btn {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.modal-header {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3)), url('https://images.unsplash.com/photo-1493246507139-91e8fad9978e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  height: 200px;
  position: relative;
  display: flex;
  align-items: flex-end;
  padding: 2rem;
}

.modal-profile {
  display: flex;
  align-items: flex-end;
}

.modal-profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  margin-bottom: -60px;
  position: relative;
  z-index: 2;
}

.modal-profile-info {
  margin-left: 1.5rem;
  color: white;
}

.modal-profile-name {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-profile-title {
  font-size: 1.125rem;
  color: #fb923c;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.modal-content {
  padding: 4rem 2rem 2rem;
}

.modal-section {
  margin-bottom: 2rem;
}

.modal-section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.modal-section-title i {
  color: #f97316;
  margin-right: 0.75rem;
}

.modal-about {
  color: var(--color-text-secondary);
  line-height: 1.7;
}

.modal-specialties {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.modal-specialty {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: rgba(249, 115, 22, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.dark-mode .modal-specialty {
  background-color: rgba(249, 115, 22, 0.15);
}

.modal-specialty:hover {
  background-color: rgba(249, 115, 22, 0.15);
  transform: translateY(-2px);
}

.dark-mode .modal-specialty:hover {
  background-color: rgba(249, 115, 22, 0.2);
}

.modal-specialty i {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f97316;
  color: white;
  border-radius: 50%;
  margin-right: 1rem;
}

.modal-specialty-name {
  font-weight: 600;
  color: var(--color-text);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.modal-action-btn {
  flex: 1;
  padding: 1rem;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.modal-action-primary {
  background-color: #f97316;
  color: white;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3), 0 2px 4px -1px rgba(249, 115, 22, 0.1);
}

.modal-action-primary:hover {
  background-color: #ea580c;
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(249, 115, 22, 0.3), 0 4px 6px -2px rgba(249, 115, 22, 0.1);
}

.modal-action-secondary {
  background-color: transparent;
  color: #f97316;
  border: 2px solid #f97316;
}

.modal-action-secondary:hover {
  background-color: rgba(249, 115, 22, 0.1);
  transform: translateY(-2px);
}

.modal-action-btn i {
  margin-right: 0.5rem;
}

/* CTA section */
.cta-section {
  background: linear-gradient(135deg, #f97316, #ea580c);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cta-description {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.cta-btn {
  background-color: white;
  color: #f97316;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.cta-btn:active {
  transform: translateY(0);
}

.cta-btn i {
  margin-right: 0.5rem;
}

.cta-account-btn {
  background-color: transparent;
  color: white;
  font-weight: 600;
  padding: 0.875rem 1.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
  border: 2px solid white;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cta-account-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.cta-account-btn:active {
  transform: translateY(0);
}

.cta-account-btn img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.75rem;
  border: 2px solid white;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sequential animation delay for guide cards */
.guide-card:nth-child(1) {
  animation-delay: 0.1s;
}

.guide-card:nth-child(2) {
  animation-delay: 0.2s;
}

.guide-card:nth-child(3) {
  animation-delay: 0.3s;
}

.guide-card:nth-child(4) {
  animation-delay: 0.4s;
}

.guide-card:nth-child(5) {
  animation-delay: 0.5s;
}

.guide-card:nth-child(6) {
  animation-delay: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-specialties {
    grid-template-columns: 1fr;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-profile-image {
    width: 100px;
    height: 100px;
    margin-bottom: -50px;
  }
  
  .modal-header {
    height: 160px;
  }
  
  .filter-container {
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
    margin-top: -1.5rem;
    width: 100%;
    justify-content: flex-start;
  }
  
  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    white-space: nowrap;
  }
  
  .guide-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .guide-profile-image {
    margin-bottom: 1rem;
  }
  
  .guide-info {
    margin-left: 0;
  }
  
  .guide-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .guide-action {
    width: 100%;
  }
}

/* Fix for auth state inconsistency */
.auth-state-visible {
  display: flex !important;
}

.auth-state-hidden {
  display: none !important;
}

/* Dark mode adjustments */
.dark-mode .guide-name {
  color: white;
}

.dark-mode .guide-description {
  color: #d1d5db;
}

.dark-mode .guide-stat {
  color: #9ca3af;
}

.dark-mode .modal-section-title {
  color: white;
}

.dark-mode .modal-about {
  color: #d1d5db;
}

.dark-mode .modal-specialty-name {
  color: white;
}

/* Loading state */
.guides-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.guides-loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(249, 115, 22, 0.1);
  border-radius: 50%;
  border-top-color: #f97316;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Invitation seal effect */
.invitation-seal {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 60px;
  height: 60px;
  background-color: #f97316;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 0.875rem;
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3), 0 2px 4px -1px rgba(249, 115, 22, 0.1);
  z-index: 10;
  transform: rotate(15deg);
}

.invitation-seal::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px dashed #f97316;
  border-radius: 50%;
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
