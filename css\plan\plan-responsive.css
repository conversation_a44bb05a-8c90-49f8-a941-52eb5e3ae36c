/* Animation Classes */
.slide-in-right {
  animation: slideInRight var(--transition-normal) forwards;
}

.slide-in-left {
  animation: slideInLeft var(--transition-normal) forwards;
}

.fade-in {
  animation: fadeIn var(--transition-normal) forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Mobile-First Responsive Styles */

/* Extra Small Devices (phones, 320px and up) */
@media (min-width: 320px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .budget-categories {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .weather-days {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .weather-day {
    min-width: 100%;
    max-width: 100%;
  }

  .day-selector {
    padding: var(--spacing-3) 0;
    gap: var(--spacing-2);
  }

  .day-button {
    min-width: 65px;
    height: 65px;
    flex-shrink: 0;
  }

  .timeline-event {
    padding-left: var(--spacing-5);
  }

  .timeline-icon {
    width: 40px;
    height: 40px;
    left: -20px;
  }

  .timeline-time {
    left: 30px;
    font-size: 0.75rem;
    padding: var(--spacing-1) var(--spacing-2);
  }

  .timeline::before {
    left: 0;
  }
}

/* Small Devices (landscape phones, 480px and up) */
@media (min-width: 480px) {
  .budget-categories {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .weather-days {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .weather-day {
    min-width: calc(50% - var(--spacing-2));
    max-width: calc(50% - var(--spacing-2));
  }

  .day-button {
    min-width: 70px;
    height: 70px;
  }
}

/* Medium Small Devices (tablets, 640px and up) */
@media (min-width: 640px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .budget-categories {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
  }

  .weather-day {
    min-width: calc(33.333% - var(--spacing-3));
    max-width: calc(33.333% - var(--spacing-3));
  }

  .recommendations-grid, .videos-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .day-button {
    min-width: 80px;
    height: 80px;
  }

  .timeline-icon {
    width: 48px;
    height: 48px;
    left: -24px;
  }

  .timeline-time {
    left: 35px;
    font-size: 0.875rem;
  }

  .timeline::before {
    left: 24px;
  }

  .timeline-event {
    padding-left: var(--spacing-8);
  }
}

@media (min-width: 768px) {
  .main-nav {
    display: flex;
    gap: var(--spacing-6);
  }

  .itinerary-details {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .itinerary-name {
    width: auto;
    font-size: 2.25rem;
  }

  .itinerary-actions {
    flex-wrap: nowrap;
  }

  .overview-grid {
    grid-template-columns: 2fr 1fr;
  }

  .day-detail-grid {
    grid-template-columns: 2fr 1fr;
  }

  .lodging-details, .meals-summary, .transport-summary {
    grid-template-columns: 2fr 1fr;
  }

  .lodging-meta {
    grid-template-columns: repeat(4, 1fr);
  }

  .amenities-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .meal-content {
    flex-direction: row;
  }

  .meal-image {
    width: 40%;
    min-width: 200px;
    height: auto;
  }

  .meal-details {
    width: 60%;
  }

  .transport-card {
    grid-template-columns: 60px 1fr;
  }

  .transport-info {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .action-left {
    flex-direction: row;
    align-items: center;
  }

  .recommendations-grid, .videos-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .timeline-card, .meal-card, .transport-card, .recommendation-card, .video-card {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  }

  .timeline-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
  }

  .amenity-item, .tip-item {
    transition: all var(--transition-fast);
  }

  .amenity-item:hover, .tip-item:hover {
    background-color: var(--color-gray-100);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .dark .amenity-item:hover, .dark .tip-item:hover {
    background-color: var(--color-gray-200);
  }

  .meal-card {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  }

  .meal-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
  }

  .meal-image {
    overflow: hidden;
    height: 180px;
  }

  .meal-image img {
    transition: transform var(--transition-normal);
  }

  .meal-card:hover .meal-image img {
    transform: scale(1.05);
  }

  .transport-card {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  }

  .transport-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
  }

  .transport-info {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Enhanced Mobile Optimizations */
@media (max-width: 767px) {
  .overview-section {
    padding: var(--spacing-6) 0;
  }

  .overview-section::before {
    display: none; /* Remove pattern on mobile for better performance */
  }

  .overview-grid {
    gap: var(--spacing-6);
  }

  .budget-summary {
    max-height: none; /* Remove fixed height on mobile */
    overflow-y: visible;
  }

  .budget-summary .card, .weather-summary .card {
    padding: var(--spacing-4);
  }

  .meal-card, .transport-card {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-4);
  }

  .meal-card:hover, .transport-card:hover {
    transform: translateY(-4px) scale(1.01); /* Reduced scale for mobile */
  }

  .transport-icon {
    width: 100%;
    height: 50px;
  }

  .meal-image {
    width: 100%;
    height: 160px; /* Slightly reduced for mobile */
  }

  .meal-details {
    padding: var(--spacing-3);
  }

  .transport-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .transport-info {
    grid-template-columns: 1fr;
  }

  .day-detail-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .day-summary {
    position: static; /* Remove sticky on mobile */
    margin-top: var(--spacing-6);
  }

  .timeline-card {
    padding: var(--spacing-4);
    margin-top: var(--spacing-4);
  }

  .timeline-card:hover {
    transform: translateY(-4px) scale(1.01); /* Reduced scale for mobile */
  }
}

@media (min-width: 1280px) {
  .container {
    padding: 0 var(--spacing-8);
  }
}

/* Enhanced Touch and Scroll Optimizations */
@media (max-width: 480px) {
  /* Optimize for very small screens */
  .container {
    padding: 0 var(--spacing-2);
  }

  .overview-section {
    padding: var(--spacing-4) 0;
  }

  .budget-summary .card, .weather-summary .card {
    padding: var(--spacing-3);
    border-radius: var(--border-radius-lg);
  }

  .budget-summary .card h2, .weather-summary .card h2 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-4);
  }

  .budget-categories {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .budget-category {
    padding: var(--spacing-3);
  }

  .weather-days {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .weather-day {
    min-width: 100%;
    padding: var(--spacing-3);
  }

  .day-selector {
    padding: var(--spacing-2) 0;
    margin-bottom: var(--spacing-4);
  }

  .day-button {
    min-width: 60px;
    height: 60px;
    padding: var(--spacing-1);
  }

  .day-button .day-number {
    font-size: 1rem;
  }

  .day-button .day-date {
    font-size: 0.7rem;
  }

  .day-title h2 {
    font-size: 1.25rem;
  }

  .day-title p {
    font-size: 0.875rem;
  }

  .timeline-card {
    padding: var(--spacing-3);
    border-radius: var(--border-radius-lg);
  }

  .timeline-header h4 {
    font-size: 1rem;
  }

  .meal-card {
    border-radius: var(--border-radius-lg);
  }

  .meal-time {
    padding: var(--spacing-3);
    font-size: 0.8rem;
  }

  .meal-details {
    padding: var(--spacing-3);
  }

  .meal-details h3 {
    font-size: 1.125rem;
  }

  .meal-card-actions {
    padding: var(--spacing-3);
  }

  .meal-card-actions .btn {
    padding: var(--spacing-3);
    font-size: 0.875rem;
  }

  .summary-box {
    padding: var(--spacing-4);
    border-radius: var(--border-radius-lg);
  }

  .summary-box h3 {
    font-size: 1.125rem;
  }
}

/* Scroll Optimization Styles */
.day-selector {
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
}

.day-selector::-webkit-scrollbar {
  display: none;
}

.day-button {
  scroll-snap-align: start;
}

/* Enhanced Touch Targets for Mobile */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px; /* iOS recommended touch target */
    padding: var(--spacing-3) var(--spacing-4);
  }

  .day-button {
    min-width: 70px;
    min-height: 70px;
  }

  .meal-card-actions .btn {
    min-height: 48px;
    padding: var(--spacing-3) var(--spacing-4);
  }

  .timeline-card {
    padding: var(--spacing-5);
  }

  /* Reduce hover effects on touch devices */
  .card:hover,
  .budget-category:hover,
  .weather-day:hover,
  .timeline-card:hover,
  .meal-card:hover {
    transform: none;
  }

  /* Add active states for touch feedback */
  .card:active,
  .budget-category:active,
  .weather-day:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}