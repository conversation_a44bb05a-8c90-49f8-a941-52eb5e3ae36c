/**
 * Account Page Main Script - Clean initialization
 * Uses the consolidated AccountController for all functionality
 */

import accountController from './accountController.js';
import { initializeFiltersAndToggles } from './filters.js';
import { initAuth, getCurrentUser } from '../../core/auth.js';

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    console.log('DOM content loaded, initializing account page');

    // Initialize authentication first
    await initAuth();

    // Get current user (authentication already verified by immediate guard)
    const user = await getCurrentUser();
    console.log('User authenticated:', user?.id);

    // Initialize the consolidated account controller
    await accountController.initialize();

    // Make accountController globally available for debugging
    window.accountController = accountController;

    // Set up global event listeners (logout, mobile menu, etc.)
    setupGlobalEventListeners();

    // Initialize filters and view toggles for itineraries/notifications
    setTimeout(() => {
      initializeFiltersAndToggles();
    }, 500);

    console.log('Account page initialized successfully');
  } catch (error) {
    console.error('Error initializing account page:', error);
    showErrorMessage('Failed to load account data. Please refresh the page.');
  }
});

/**
 * Set up global event listeners (non-account specific)
 */
function setupGlobalEventListeners() {
  // Mobile menu toggle
  const menuToggle = document.querySelector('.mobile-menu-toggle');
  const sidebar = document.querySelector('.sidebar-nav');

  if (menuToggle && sidebar) {
    menuToggle.addEventListener('click', () => {
      sidebar.classList.toggle('active');

      // Add/remove overlay
      if (sidebar.classList.contains('active')) {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        overlay.addEventListener('click', () => {
          sidebar.classList.remove('active');
          document.body.removeChild(overlay);
        });
        document.body.appendChild(overlay);
      } else {
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
          document.body.removeChild(overlay);
        }
      }
    });
  }

  // Password visibility toggle
  const togglePasswordButtons = document.querySelectorAll('.toggle-password');
  togglePasswordButtons.forEach(button => {
    button.addEventListener('click', () => {
      const input = button.parentElement.querySelector('input');
      const icon = button.querySelector('i');

      if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  });

  // Sign out functionality
  const logoutButton = document.querySelector('.logout-button');
  if (logoutButton) {
    logoutButton.addEventListener('click', async (e) => {
      e.preventDefault();

      if (confirm('Are you sure you want to sign out?')) {
        try {
          console.log('Starting logout process...');
          const { signOut } = await import('../../core/auth.js');
          const result = await signOut();

          console.log('Logout result:', result);
          if (result.success) {
            console.log('Logout successful, redirecting to signin...');
            window.location.href = './signin.html';
          } else {
            console.error('Logout failed:', result.error);
            alert('Failed to sign out. Please try again.');
          }
        } catch (error) {
          console.error('Error during sign out:', error);
          alert('An error occurred while signing out. Please try again.');
        }
      }
    });
  }
}

/**
 * Show error message to user
 * @param {string} message Error message to display
 */
function showErrorMessage(message) {
  // Create error toast
  const errorDiv = document.createElement('div');
  errorDiv.className = 'toast-message error';
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ef4444;
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 400px;
  `;
  errorDiv.innerHTML = `
    <div style="display: flex; align-items: center; gap: 0.5rem;">
      <i class="fas fa-exclamation-triangle"></i>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(errorDiv);

  // Remove after 5 seconds
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
}

