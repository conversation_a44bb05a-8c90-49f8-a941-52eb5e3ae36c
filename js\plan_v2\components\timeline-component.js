/**
 * Timeline Component - Phase 2 Implementation
 * 
 * This component will handle the timeline view that combines all daily activities
 * (accommodation, meals, activities, transport) into a chronological timeline.
 */

/**
 * Timeline Component Class
 * 
 * Responsibilities:
 * - Render chronological timeline of daily activities
 * - Merge data from accommodation, meals, activities, transport
 * - Handle timeline item interactions
 * - Show time-based navigation
 * - Manage timeline animations
 */
export class TimelineComponent {
  constructor(container, stateManager) {
    this.container = container;
    this.stateManager = stateManager;
    this.elements = {};
    this.unsubscribers = [];
    this.currentDay = 1;
    this.timelineData = [];
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    console.log('⏰ TimelineComponent: Initializing...');
    
    this.cacheElements();
    this.setupEventListeners();
    this.subscribeToState();
    
    console.log('⏰ TimelineComponent: Initialized');
  }

  /**
   * Cache DOM elements
   */
  cacheElements() {
    this.elements = {
      timelineContainer: this.container.querySelector('.timeline-container'),
      timelineHeader: this.container.querySelector('.timeline-header'),
      timeline: this.container.querySelector('.timeline'),
      timelineItems: this.container.querySelectorAll('.timeline-item')
    };
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Timeline item clicks
    this.elements.timelineItems.forEach(item => {
      item.addEventListener('click', () => {
        const time = item.querySelector('.timeline-time').textContent;
        const category = item.querySelector('.timeline-category').textContent;
        this.handleTimelineItemClick(time, category);
      });
    });

    // Timeline item hover effects
    this.elements.timelineItems.forEach(item => {
      item.addEventListener('mouseenter', () => {
        this.highlightTimelineItem(item);
      });
      
      item.addEventListener('mouseleave', () => {
        this.unhighlightTimelineItem(item);
      });
    });
  }

  /**
   * Subscribe to state changes
   */
  subscribeToState() {
    // Subscribe to selected day changes
    const dayUnsubscribe = this.stateManager.subscribe('navigation.selectedDay', (day) => {
      this.currentDay = day;
      this.loadTimelineData(day);
    });
    
    this.unsubscribers.push(dayUnsubscribe);

    // Subscribe to day data changes
    const dataUnsubscribe = this.stateManager.subscribe('data.dayData', (dayData) => {
      this.renderTimeline(dayData);
    });
    
    this.unsubscribers.push(dataUnsubscribe);
  }

  /**
   * Load timeline data for specific day
   * @param {number} day - Day number
   */
  async loadTimelineData(day) {
    console.log(`⏰ TimelineComponent: Loading timeline data for day ${day}`);
    
    // Phase 2: Load and merge data from all sources
    // const dayData = await this.dataManager.fetchDayData(day);
    // const timelineData = this.mergeTimelineData(dayData);
    // this.timelineData = this.sortTimelineData(timelineData);
    
    // For Phase 1, use placeholder data
    this.updateTimelineHeader(day);
  }

  /**
   * Merge data from different sources into timeline format
   * @param {Object} dayData - Day data containing all activities
   * @returns {Array} Merged timeline data
   */
  mergeTimelineData(dayData) {
    const timelineItems = [];
    
    // Add accommodation events
    if (dayData.accommodation) {
      if (dayData.accommodation.checkOut) {
        timelineItems.push({
          time: dayData.accommodation.checkOut.time,
          type: 'accommodation',
          icon: 'fas fa-bed',
          title: 'Hotel Check-out',
          description: dayData.accommodation.checkOut.hotel,
          category: 'Accommodation'
        });
      }
      
      if (dayData.accommodation.checkIn) {
        timelineItems.push({
          time: dayData.accommodation.checkIn.time,
          type: 'accommodation',
          icon: 'fas fa-bed',
          title: 'Hotel Check-in',
          description: dayData.accommodation.checkIn.hotel,
          category: 'Accommodation'
        });
      }
    }

    // Add transport events
    if (dayData.transport) {
      dayData.transport.forEach(transport => {
        timelineItems.push({
          time: transport.departureTime,
          type: 'transport',
          icon: 'fas fa-car',
          title: `Travel to ${transport.destination}`,
          description: `${transport.type} • ${transport.duration} • ${transport.distance}`,
          category: 'Transport'
        });
      });
    }

    // Add meal events
    if (dayData.meals) {
      dayData.meals.forEach(meal => {
        timelineItems.push({
          time: meal.time,
          type: 'meal',
          icon: 'fas fa-utensils',
          title: `${meal.type} at ${meal.restaurant}`,
          description: meal.description,
          category: 'Meal'
        });
      });
    }

    // Add activity events
    if (dayData.activities) {
      dayData.activities.forEach(activity => {
        timelineItems.push({
          time: activity.startTime,
          type: 'activity',
          icon: 'fas fa-map-marker-alt',
          title: activity.name,
          description: activity.description,
          category: 'Activity'
        });
      });
    }

    return timelineItems;
  }

  /**
   * Sort timeline data by time
   * @param {Array} timelineData - Unsorted timeline data
   * @returns {Array} Sorted timeline data
   */
  sortTimelineData(timelineData) {
    return timelineData.sort((a, b) => {
      const timeA = this.parseTime(a.time);
      const timeB = this.parseTime(b.time);
      return timeA - timeB;
    });
  }

  /**
   * Parse time string to minutes for sorting
   * @param {string} timeStr - Time string (e.g., "09:30")
   * @returns {number} Minutes since midnight
   */
  parseTime(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Render timeline
   * @param {Object} dayData - Day data from state
   */
  renderTimeline(dayData) {
    if (!dayData) return;
    
    const currentDayData = dayData.get ? dayData.get(this.currentDay) : dayData[this.currentDay];
    if (!currentDayData) return;
    
    console.log(`⏰ TimelineComponent: Rendering timeline for day ${this.currentDay}`);
    
    // Phase 2: Generate timeline items dynamically
    // const timelineData = this.mergeTimelineData(currentDayData);
    // this.renderTimelineItems(timelineData);
    
    // For Phase 1, the HTML already contains static timeline items
    this.animateTimelineItems();
  }

  /**
   * Render timeline items dynamically
   * @param {Array} timelineData - Timeline data to render
   */
  renderTimelineItems(timelineData) {
    if (!this.elements.timeline) return;
    
    // Clear existing items
    this.elements.timeline.innerHTML = '';
    
    // Create timeline items
    timelineData.forEach((item, index) => {
      const timelineItem = this.createTimelineItem(item, index);
      this.elements.timeline.appendChild(timelineItem);
    });
  }

  /**
   * Create a timeline item element
   * @param {Object} item - Timeline item data
   * @param {number} index - Item index for animation delay
   * @returns {HTMLElement} Timeline item element
   */
  createTimelineItem(item, index) {
    const timelineItem = document.createElement('div');
    timelineItem.className = 'timeline-item';
    timelineItem.style.animationDelay = `${index * 0.1}s`;
    
    timelineItem.innerHTML = `
      <div class="timeline-time">${item.time}</div>
      <div class="timeline-icon ${item.type}">
        <i class="${item.icon}"></i>
      </div>
      <div class="timeline-content">
        <h4>${item.title}</h4>
        <p>${item.description}</p>
        <span class="timeline-category ${item.type}">${item.category}</span>
      </div>
    `;
    
    // Add click handler
    timelineItem.addEventListener('click', () => {
      this.handleTimelineItemClick(item.time, item.category);
    });
    
    return timelineItem;
  }

  /**
   * Update timeline header
   * @param {number} day - Day number
   */
  updateTimelineHeader(day) {
    if (!this.elements.timelineHeader) return;
    
    const titleEl = this.elements.timelineHeader.querySelector('h3');
    const subtitleEl = this.elements.timelineHeader.querySelector('p');
    
    if (titleEl) {
      titleEl.textContent = 'Complete Day Timeline';
    }
    
    if (subtitleEl) {
      subtitleEl.textContent = `All activities, meals, and transport for Day ${day}`;
    }
  }

  /**
   * Animate timeline items
   */
  animateTimelineItems() {
    // Reset animations
    this.elements.timelineItems.forEach(item => {
      item.style.opacity = '0';
      item.style.transform = 'translateX(-20px)';
    });
    
    // Animate items with staggered delay
    this.elements.timelineItems.forEach((item, index) => {
      setTimeout(() => {
        item.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
        item.style.opacity = '1';
        item.style.transform = 'translateX(0)';
      }, index * 100);
    });
  }

  /**
   * Handle timeline item click
   * @param {string} time - Item time
   * @param {string} category - Item category
   */
  handleTimelineItemClick(time, category) {
    console.log(`⏰ TimelineComponent: Timeline item clicked: ${time} - ${category}`);
    
    // Phase 2: Navigate to specific tab or show details
    switch (category.toLowerCase()) {
      case 'accommodation':
        this.stateManager.setState('navigation.activeTab', 'accommodations');
        break;
      case 'meal':
        this.stateManager.setState('navigation.activeTab', 'restaurants');
        break;
      case 'activity':
        this.stateManager.setState('navigation.activeTab', 'activities');
        break;
      case 'transport':
        this.stateManager.setState('navigation.activeTab', 'transport');
        break;
    }
  }

  /**
   * Highlight timeline item
   * @param {HTMLElement} item - Timeline item element
   */
  highlightTimelineItem(item) {
    item.style.transform = 'translateY(-2px)';
    item.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
  }

  /**
   * Remove highlight from timeline item
   * @param {HTMLElement} item - Timeline item element
   */
  unhighlightTimelineItem(item) {
    item.style.transform = 'translateY(0)';
    item.style.boxShadow = '';
  }

  /**
   * Filter timeline by category
   * @param {string} category - Category to filter by
   */
  filterTimeline(category) {
    console.log(`⏰ TimelineComponent: Filtering timeline by: ${category}`);
    
    this.elements.timelineItems.forEach(item => {
      const itemCategory = item.querySelector('.timeline-category').textContent.toLowerCase();
      
      if (category === 'all' || itemCategory === category.toLowerCase()) {
        item.style.display = 'grid';
      } else {
        item.style.display = 'none';
      }
    });
  }

  /**
   * Show all timeline items
   */
  showAllTimelineItems() {
    this.filterTimeline('all');
  }

  /**
   * Get timeline data for export
   * @returns {Array} Timeline data
   */
  getTimelineData() {
    return this.timelineData;
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Unsubscribe from state changes
    this.unsubscribers.forEach(unsubscribe => unsubscribe());
    this.unsubscribers = [];
    
    console.log('⏰ TimelineComponent: Destroyed');
  }
}
