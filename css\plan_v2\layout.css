/* ===== LAYOUT STYLES ===== */

/* ===== HEADER ===== */

.header {
  background: var(--color-bg);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 990;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.logo i {
  font-size: var(--font-size-2xl);
}

.main-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  color: var(--gray-700);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.nav-link:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

.theme-toggle {
  background: none;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-2);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  background: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-900);
}

/* ===== TRIP STATUS BANNER ===== */

.trip-status-banner {
  background: var(--info-light);
  border-bottom: 1px solid var(--info-color);
  padding: var(--spacing-3) 0;
  animation: slideDown 0.3s ease-out;
}

.trip-status-banner.warning {
  background: var(--warning-light);
  border-color: var(--warning-color);
}

.trip-status-banner.success {
  background: var(--success-light);
  border-color: var(--success-color);
}

.status-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.status-icon {
  font-size: var(--font-size-lg);
  color: var(--info-color);
}

.trip-status-banner.warning .status-icon {
  color: var(--warning-color);
}

.trip-status-banner.success .status-icon {
  color: var(--success-color);
}

.status-text h4 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-1);
  color: var(--gray-900);
}

.status-text p {
  font-size: var(--font-size-sm);
  margin-bottom: 0;
  color: var(--gray-700);
}

.status-close {
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--spacing-1);
  margin-left: auto;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.status-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--gray-700);
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ===== TRIP HEADER ===== */

.trip-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: var(--color-text-inverse);
  padding: var(--spacing-8) 0;
}

.trip-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-6);
}

.trip-info {
  flex: 1;
}

.trip-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin-bottom: var(--spacing-3);
  border: none;
  background: none;
  outline: none;
  cursor: text;
}

.trip-title:focus {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-2);
  margin: calc(-1 * var(--spacing-2));
}

.trip-title[data-placeholder]:empty::before {
  content: attr(data-placeholder);
  color: rgba(255, 255, 255, 0.7);
}

.trip-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.trip-dates {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: rgba(255, 255, 255, 0.9);
  font-weight: var(--font-weight-medium);
}

.trip-dates i {
  opacity: 0.8;
}

.status-badge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-badge.status-active {
  background: var(--success-color);
  border-color: var(--success-color);
}

.status-badge.status-future {
  background: var(--warning-color);
  border-color: var(--warning-color);
}

.status-badge.status-past {
  background: var(--gray-500);
  border-color: var(--gray-500);
}

.trip-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* ===== OVERVIEW SECTION ===== */

.overview-section {
  padding: var(--spacing-8) 0;
  background: var(--color-bg-alt);
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

.budget-overview {
  order: 1;
}

.weather-overview {
  order: 2;
}

@media (min-width: 1024px) {
  .overview-grid {
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-8);
  }

  .budget-overview {
    order: 0;
  }

  .weather-overview {
    order: 0;
  }
}

/* ===== DAY NAVIGATION SECTION ===== */

.day-navigation-section {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-6) 0;
}

.day-navigation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
}

.navigation-controls {
  display: flex;
  gap: var(--spacing-2);
}

.nav-btn {
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-900);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.current-day-info {
  text-align: center;
}

.day-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.day-subtitle {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.day-actions {
  display: flex;
  gap: var(--spacing-2);
}

.day-selector {
  display: flex;
  gap: var(--spacing-3);
  overflow-x: auto;
  padding: var(--spacing-2) 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.day-selector::-webkit-scrollbar {
  display: none;
}

.day-card {
  background: var(--white);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  min-width: 80px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: var(--shadow-sm);
}

.day-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-3px);
  box-shadow: var(--shadow-card-hover);
}

.day-card.active {
  border-color: var(--color-primary);
  background: var(--primary-light);
  box-shadow: var(--shadow-md);
}

.day-number {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.day-date {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--spacing-2);
}

.day-status {
  display: flex;
  justify-content: center;
}

.day-status i {
  font-size: var(--font-size-sm);
}

.day-status .fa-check-circle {
  color: var(--success-color);
}

.day-status .fa-clock {
  color: var(--warning-color);
}

.day-status .fa-circle {
  color: var(--gray-300);
}

/* ===== DAY CONTENT SECTION ===== */

.day-content-section {
  background: var(--color-bg);
  padding: var(--spacing-8) 0;
  min-height: 60vh;
}

.day-tabs {
  display: flex;
  background: var(--white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-card);
  overflow-x: auto;
}

.day-tab {
  background: none;
  border: none;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-xl);
  color: var(--color-text-muted);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  white-space: nowrap;
  flex: 1;
  justify-content: center;
}

.day-tab:hover {
  background: var(--color-bg-accent);
  color: var(--color-text);
  transform: translateY(-1px);
}

.day-tab.active {
  background: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.tab-content {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.tab-pane {
  display: none;
  padding: var(--spacing-6);
}

.tab-pane.active {
  display: block;
}

/* ===== MAP SECTION ===== */

.map-section {
  background: var(--color-bg-alt);
  padding: var(--spacing-8) 0;
  border-top: 1px solid var(--color-border);
  border-bottom: 1px solid var(--color-border);
}

.map-container {
  background: var(--color-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.map-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-border);
  text-align: center;
}

.map-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.map-header h2 i {
  color: var(--color-primary);
}

.map-header p {
  color: var(--color-text-muted);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.map-wrapper {
  display: grid;
  grid-template-columns: 1fr 250px;
  gap: 0;
}

.trip-map {
  height: 500px;
  background: var(--color-bg-accent);
  position: relative;
}

.map-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, var(--color-bg-accent) 0%, var(--color-bg-alt) 100%);
}

.map-placeholder-content {
  text-align: center;
  color: var(--color-text-muted);
}

.map-placeholder-content i {
  font-size: 3rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-4);
  opacity: 0.7;
}

.map-placeholder-content h3 {
  font-size: var(--font-size-xl);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.map-placeholder-content p {
  color: var(--color-text-muted);
  margin-bottom: 0;
}

.map-legend {
  background: var(--color-bg);
  border-left: 1px solid var(--color-border);
  padding: var(--spacing-6);
}

.map-legend h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-4);
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.legend-marker {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-sm);
}

.legend-marker.accommodation {
  background: var(--color-primary);
}

.legend-marker.restaurant {
  background: var(--accent-color);
}

.legend-marker.activity {
  background: var(--success-color);
}

.legend-marker.route {
  background: var(--info-color);
}

.legend-item span {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

/* Responsive map */
@media (max-width: 767px) {
  .map-section {
    padding: var(--spacing-6) 0;
  }

  .map-container {
    border-radius: var(--radius-xl);
  }

  .map-header {
    padding: var(--spacing-4);
  }

  .map-header h2 {
    font-size: var(--font-size-xl);
  }

  .map-wrapper {
    grid-template-columns: 1fr;
  }

  .map-legend {
    border-left: none;
    border-top: 1px solid var(--color-border);
    padding: var(--spacing-4);
  }

  .legend-items {
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    justify-content: center;
  }

  .trip-map {
    height: 350px;
  }

  .map-placeholder-content h3 {
    font-size: var(--font-size-lg);
  }
}


