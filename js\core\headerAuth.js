/**
 * Header Authentication Manager
 * Handles authentication state for both main header and account page header
 */

import { supabase } from '../supabaseClient.js';
import { getCurrentUser, getUserProfile } from './auth.js';

class HeaderAuthManager {
  constructor() {
    this.initialized = false;
    this.currentUser = null;
    this.userProfile = null;
  }

  /**
   * Initialize header authentication
   */
  async initialize() {
    if (this.initialized) return;

    try {
      console.log('Initializing Header Auth Manager...');

      // Use initial auth state if available to prevent delay
      if (window.initialAuthState) {
        console.log('Using initial auth state to prevent FOUC');
        this.currentUser = window.initialAuthState.session?.user || null;

        // Load profile if user exists
        if (this.currentUser) {
          try {
            this.userProfile = await getUserProfile(this.currentUser.id);
          } catch (error) {
            console.warn('Failed to load user profile:', error);
            this.userProfile = null;
          }
        }
      } else {
        // Fallback to loading user data normally
        await this.loadUserData();
      }

      // Update header UI immediately
      this.updateHeaderUI();

      // Listen for auth state changes
      this.setupAuthStateListener();

      this.initialized = true;
      console.log('Header Auth Manager initialized');
    } catch (error) {
      console.error('Error initializing Header Auth Manager:', error);
    }
  }

  /**
   * Load current user data
   */
  async loadUserData() {
    try {
      this.currentUser = await getCurrentUser();
      
      if (this.currentUser) {
        try {
          this.userProfile = await getUserProfile(this.currentUser.id);
        } catch (error) {
          console.warn('Failed to load user profile:', error);
          this.userProfile = null;
        }
      } else {
        this.userProfile = null;
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      this.currentUser = null;
      this.userProfile = null;
    }
  }

  /**
   * Update header UI based on authentication state
   */
  updateHeaderUI() {
    const isAccountPage = window.location.pathname.includes('/account.html');
    
    if (isAccountPage) {
      this.updateAccountPageHeader();
    } else {
      this.updateMainHeader();
    }
  }

  /**
   * Update account page header
   */
  updateAccountPageHeader() {
    console.log('Updating account page header...');
    
    if (this.currentUser) {
      // Update profile dropdown
      const profileDropdown = document.querySelector('.profile-dropdown');
      const profileImage = document.querySelector('.profile-image');
      
      if (profileDropdown && profileImage) {
        // Show profile dropdown
        profileDropdown.style.display = 'block';
        
        // Set avatar image
        if (this.userProfile?.avatar_url) {
          profileImage.src = this.userProfile.avatar_url;
        } else {
          profileImage.src = 'https://via.placeholder.com/40x40/e5e7eb/6b7280?text=U';
        }
        
        // Set alt text
        profileImage.alt = this.userProfile?.name || this.currentUser.email || 'Profile';
      }
      
      // Update sidebar profile info
      this.updateSidebarProfile();
    } else {
      // User not authenticated - redirect to signin
      console.log('User not authenticated on account page, redirecting...');
      window.location.href = './signin.html';
    }
  }

  /**
   * Update sidebar profile information
   */
  updateSidebarProfile() {
    const profilePhoto = document.querySelector('.profile-photo');
    const profileName = document.querySelector('.profile-name');
    const profileEmail = document.querySelector('.profile-email span');
    
    if (profilePhoto) {
      if (this.userProfile?.avatar_url) {
        profilePhoto.src = this.userProfile.avatar_url;
      } else {
        profilePhoto.src = 'https://via.placeholder.com/100x100/e5e7eb/6b7280?text=U';
      }
    }
    
    if (profileName) {
      profileName.textContent = this.userProfile?.name || this.currentUser?.email || 'User';
    }
    
    if (profileEmail) {
      profileEmail.textContent = this.currentUser?.email || '';
    }
  }

  /**
   * Update main header (for other pages)
   */
  updateMainHeader() {
    console.log('Updating main header...');

    const authPlaceholder = document.querySelector('#auth-placeholder');
    const mobileAuthPlaceholder = document.querySelector('#mobile-auth-placeholder');
    const mobileMenuAuthPlaceholder = document.querySelector('#mobile-menu-auth-placeholder');

    if (this.currentUser) {
      // User is authenticated - show profile dropdown
      this.createDesktopProfileDropdown(authPlaceholder);
      this.createMobileProfileButton(mobileAuthPlaceholder);
      this.createMobileMenuProfile(mobileMenuAuthPlaceholder);
    } else {
      // User not authenticated - show signin buttons (they're already there, just ensure correct paths)
      this.updateSigninButtons();
    }
  }

  /**
   * Get avatar URL with fallback and cache-busting
   */
  getAvatarUrl(avatarUrl) {
    if (avatarUrl) {
      // Add cache-busting parameter if URL doesn't already have query params
      const separator = avatarUrl.includes('?') ? '&' : '?';
      return `${avatarUrl}${separator}t=${Date.now()}`;
    }
    return 'https://via.placeholder.com/32x32/e5e7eb/6b7280?text=U';
  }

  /**
   * Update signin button paths based on current location
   */
  updateSigninButtons() {
    const isInPagesDir = window.location.pathname.includes('/pages/');
    const signinPath = isInPagesDir ? '../pages/signin.html' : './pages/signin.html';

    const authPlaceholder = document.querySelector('#auth-placeholder');
    const mobileAuthPlaceholder = document.querySelector('#mobile-auth-placeholder');
    const mobileMenuAuthPlaceholder = document.querySelector('#mobile-menu-auth-placeholder');

    const signinBtn = document.querySelector('#signin-btn');
    const mobileSigninBtn = document.querySelector('#mobile-signin-btn');
    const mobileSigninLink = document.querySelector('#mobile-signin-link');

    if (signinBtn) signinBtn.href = signinPath;
    if (mobileSigninBtn) mobileSigninBtn.href = signinPath;
    if (mobileSigninLink) mobileSigninLink.href = signinPath;

    // Ensure containers are visible for unauthenticated users
    if (authPlaceholder) authPlaceholder.style.opacity = '1';
    if (mobileAuthPlaceholder) mobileAuthPlaceholder.style.opacity = '1';
    if (mobileMenuAuthPlaceholder) mobileMenuAuthPlaceholder.style.opacity = '1';
  }

  /**
   * Create desktop profile dropdown
   */
  createDesktopProfileDropdown(container) {
    if (!container) return;

    const isInPagesDir = window.location.pathname.includes('/pages/');
    const accountPath = isInPagesDir ? '../pages/account.html' : './pages/account.html';

    container.innerHTML = `
      <div class="relative">
        <button class="profile-button flex items-center space-x-2 px-4 py-2 rounded-full border-2 border-transparent text-[var(--color-text)] hover:border-[var(--color-primary)] transition-all duration-200">
          <img src="${this.getAvatarUrl(this.userProfile?.avatar_url)}"
               alt="${this.userProfile?.name || 'Profile'}"
               class="w-8 h-8 rounded-full object-cover">
          <span class="profile-name">${this.userProfile?.name || this.currentUser?.email || 'User'}</span>
          <i class="fas fa-chevron-down text-sm"></i>
        </button>
        <div class="profile-menu absolute right-0 mt-2 w-48 bg-[var(--color-bg)] border border-[var(--color-border)] rounded-lg shadow-lg z-50 hidden">
          <a href="${accountPath}" class="block px-4 py-2 text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] transition-colors duration-200">
            <i class="fas fa-user mr-2"></i> Account
          </a>
          <button class="signout-btn block w-full text-left px-4 py-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200">
            <i class="fas fa-sign-out-alt mr-2"></i> Sign Out
          </button>
        </div>
      </div>
    `;

    // Ensure container is visible after content replacement
    container.style.opacity = '1';
    this.setupProfileDropdownEvents(container);
  }

  /**
   * Create mobile profile button
   */
  createMobileProfileButton(container) {
    if (!container) return;

    const isInPagesDir = window.location.pathname.includes('/pages/');
    const accountPath = isInPagesDir ? '../pages/account.html' : './pages/account.html';

    container.innerHTML = `
      <a href="${accountPath}" class="text-[var(--color-text)] hover:text-[var(--color-primary)] transition-colors duration-200">
        <img src="${this.getAvatarUrl(this.userProfile?.avatar_url)}"
             alt="${this.userProfile?.name || 'Profile'}"
             class="w-6 h-6 rounded-full object-cover">
      </a>
    `;

    // Ensure container is visible after content replacement
    container.style.opacity = '1';
  }

  /**
   * Create mobile menu profile section
   */
  createMobileMenuProfile(container) {
    if (!container) return;

    const isInPagesDir = window.location.pathname.includes('/pages/');
    const accountPath = isInPagesDir ? '../pages/account.html' : './pages/account.html';

    container.innerHTML = `
      <div class="flex items-center space-x-3 px-4 py-3 border-2 border-[var(--color-border)] rounded-full">
        <img src="${this.getAvatarUrl(this.userProfile?.avatar_url)}"
             alt="${this.userProfile?.name || 'Profile'}"
             class="w-8 h-8 rounded-full object-cover">
        <div class="flex-1 text-left">
          <div class="text-[var(--color-text)] font-medium">${this.userProfile?.name || this.currentUser?.email || 'User'}</div>
          <div class="text-[var(--color-text-secondary)] text-sm">${this.currentUser?.email || ''}</div>
        </div>
      </div>
      <a href="${accountPath}" class="block w-full text-center px-4 py-2 border-2 border-[var(--color-border)] rounded-full text-[var(--color-text)] hover:border-[var(--color-primary)] transition-all duration-200 mt-2">
        <i class="fas fa-user mr-2"></i> Account
      </a>
      <button class="mobile-signout-btn block w-full text-center px-4 py-2 border-2 border-red-500 rounded-full text-red-500 hover:bg-red-500 hover:text-white transition-all duration-200 mt-2">
        <i class="fas fa-sign-out-alt mr-2"></i> Sign Out
      </button>
    `;

    // Ensure container is visible after content replacement
    container.style.opacity = '1';

    // Add signout event listener
    const signoutBtn = container.querySelector('.mobile-signout-btn');
    if (signoutBtn) {
      signoutBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        await this.handleSignOut();
      });
    }
  }

  /**
   * Setup profile dropdown events
   */
  setupProfileDropdownEvents(container) {
    const button = container.querySelector('.profile-button');
    const menu = container.querySelector('.profile-menu');
    const signoutBtn = container.querySelector('.signout-btn');

    if (button && menu) {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        menu.classList.toggle('hidden');
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!container.contains(e.target)) {
          menu.classList.add('hidden');
        }
      });
    }

    if (signoutBtn) {
      signoutBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        await this.handleSignOut();
      });
    }
  }

  /**
   * Handle sign out
   */
  async handleSignOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Redirect to signin page
      const isInPagesDir = window.location.pathname.includes('/pages/');
      const signinPath = isInPagesDir ? '../pages/signin.html' : './pages/signin.html';
      window.location.href = signinPath;
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  /**
   * Setup auth state listener
   */
  setupAuthStateListener() {
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);
      
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
        await this.loadUserData();
        this.updateHeaderUI();
      }
    });
  }
}

// Create and export singleton instance
const headerAuthManager = new HeaderAuthManager();
export default headerAuthManager;
