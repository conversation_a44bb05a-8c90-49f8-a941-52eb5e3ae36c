/* Lottie Animation Styling */
#lottie-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background-color: #1a1a2e; /* Darker blue background */
  transform: rotate(90deg) scale(1.8); /* Larger scale to fill the space better */
  transform-origin: center center;
  overflow: hidden;
  filter: contrast(1.05) saturate(1.1); /* Enhance colors */
}

/* Create a subtle pattern overlay for added texture */
.auth-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 2;
  opacity: 0.3;
  pointer-events: none;
}

/* Remove background image and overlay from auth-image */
.auth-image {
  background-image: none !important;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 100px rgba(0,0,0,0.5);
}

/* Remove the orange gradient overlay completely */
.auth-image::after {
  content: none;
}

/* Make sure text is still visible */
.auth-image-content {
  z-index: 3;
}

/* Add animated glow to the container edge */
@keyframes glow {
  0% { box-shadow: inset 0 0 30px rgba(234, 88, 12, 0.2); }
  50% { box-shadow: inset 0 0 50px rgba(234, 88, 12, 0.4); }
  100% { box-shadow: inset 0 0 30px rgba(234, 88, 12, 0.2); }
}

.auth-image {
  animation: glow 5s infinite;
}

/* Media queries for responsive design */
@media (max-width: 992px) {
  #lottie-container {
    min-height: 200px;
    transform: rotate(90deg) scale(1.4); /* Adjust scale for smaller screens */
  }
}

@media (max-width: 640px) {
  #lottie-container {
    transform: rotate(90deg) scale(1.2); /* Further adjust for mobile */
  }
} 