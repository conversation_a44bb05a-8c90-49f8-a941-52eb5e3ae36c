/* Index page specific styling */

/* Hero section styles */
#hero {
  background-color: #2a2a2a;
}

/* Custom background for trip wizard */
.bg-cream-50 {
  background-color: #FFF8F2;
}

/* Testimonial carousel */
.testimonial-container {
  position: relative;
  min-height: 300px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 1) 100%);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-radius: 1rem;
  overflow: hidden;
}

.dark .testimonial-container {
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(45, 55, 72, 1) 100%);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.testimonial-slide {
  opacity: 0;
  transform: translateX(50px) scale(0.95);
  position: absolute;
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  width: 100%;
  padding: 2rem;
}

.testimonial-slide.active {
  opacity: 1;
  transform: translateX(0) scale(1);
  position: relative;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e2e8f0;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  border: none;
  padding: 0;
  margin: 0;
}

.dark .carousel-dot {
  background-color: #4a5568;
}

.carousel-dot.bg-orange-600 {
  background-color: #F26522;
  transform: scale(1.3);
}

.dark .carousel-dot.bg-orange-600 {
  background-color: #ff7733;
}

/* Testimonial card styling */
.testimonial-card {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .testimonial-card {
  background-color: #2d3748;
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.dark .testimonial-card:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark .testimonial-avatar {
  border-color: #2d3748;
}

.testimonial-card:hover .testimonial-avatar {
  transform: scale(1.1);
}

.testimonial-stars {
  color: #F26522;
  display: flex;
  gap: 2px;
}

.testimonial-quote {
  position: relative;
  padding-left: 1.5rem;
  font-style: italic;
}

.testimonial-quote::before {
  content: '\201C';
  font-size: 3rem;
  font-family: Georgia, serif;
  color: #F26522;
  opacity: 0.3;
  position: absolute;
  left: 0;
  top: -1.5rem;
}

.dark .testimonial-quote::before {
  color: #ff7733;
}

/* Navigation arrows styling */
#testimonial-prev:hover, #testimonial-next:hover {
  transform: translateY(-50%) scale(1.1);
}

#testimonial-prev {
  transform: translateY(-50%) translateX(-4px);
}

#testimonial-prev:hover {
  transform: translateY(-50%) translateX(-4px) scale(1.1);
}

#testimonial-next {
  transform: translateY(-50%) translateX(4px);
}

#testimonial-next:hover {
  transform: translateY(-50%) translateX(4px) scale(1.1);
}

@media (min-width: 768px) {
  #testimonial-prev {
    transform: translateY(-50%) translateX(-12px);
  }

  #testimonial-prev:hover {
    transform: translateY(-50%) translateX(-12px) scale(1.1);
  }

  #testimonial-next {
    transform: translateY(-50%) translateX(12px);
  }

  #testimonial-next:hover {
    transform: translateY(-50%) translateX(12px) scale(1.1);
  }
}

/* Destination card hover effects */
.destination-card:hover img {
  transform: scale(1.05);
}

.destination-card img {
  transition: transform 0.3s ease;
}

/* Enhanced destination cards */
.tsafira-card {
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.tsafira-card:hover {
  transform: translateY(-8px) scale(1.01);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.tsafira-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: -1;
}

.tsafira-card:hover::after {
  opacity: 1;
}

.tsafira-card .card-image {
  overflow: hidden;
  position: relative;
}

.tsafira-card .card-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 60%, rgba(0, 0, 0, 0.6) 100%);
  z-index: 1;
  opacity: 0.7;
  transition: opacity 0.4s ease;
}

.tsafira-card:hover .card-image::before {
  opacity: 0.9;
}

.tsafira-card .card-image img {
  transition: transform 0.7s cubic-bezier(0.165, 0.84, 0.44, 1);
  transform-origin: center;
}

.tsafira-card:hover .card-image img {
  transform: scale(1.12);
}

.tsafira-card .card-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: translateZ(0);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.tsafira-card:hover .card-badge {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.tsafira-card .card-favorite {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0.7;
  transform: translateZ(0);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.tsafira-card:hover .card-favorite {
  opacity: 1;
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.9);
}

.tsafira-card .card-favorite.active {
  color: #F26522;
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.9);
  animation: heartBeat 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.3);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.tsafira-card .card-content {
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 1) 100%);
}

.dark .tsafira-card .card-content {
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(45, 55, 72, 1) 100%);
}

.tsafira-card:hover .card-content {
  transform: translateY(-5px);
}

.tsafira-card .destination-features {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.tsafira-card .feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(242, 101, 34, 0.1);
  color: #F26522;
  transition: all 0.3s ease;
}

.tsafira-card:hover .feature-icon {
  transform: scale(1.1);
  background-color: rgba(242, 101, 34, 0.2);
}

.tsafira-card .popular-badge {
  position: absolute;
  top: 1rem;
  right: 4rem;
  z-index: 10;
  background: linear-gradient(135deg, #F26522 0%, #ff7e3f 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 0.75rem;
  box-shadow: 0 4px 10px rgba(242, 101, 34, 0.3);
  transform: translateZ(0);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tsafira-card:hover .popular-badge {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 20px rgba(242, 101, 34, 0.4);
}

/* Destinations hero section */
.destinations-hero {
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url('https://images.unsplash.com/photo-1489749798305-4fea3ae63d43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
}

.destinations-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(242, 101, 34, 0.2) 0%, transparent 70%);
  z-index: 1;
  animation: pulse 8s infinite alternate;
}

.destinations-hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, transparent 40%, transparent 60%, rgba(0, 0, 0, 0.7) 100%);
  z-index: 1;
}

.destinations-hero .container {
  position: relative;
  z-index: 2;
}

@keyframes pulse {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Search and filter animations */
.search-input {
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  z-index: 1;
}

.search-input:focus {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(242, 101, 34, 0.2);
}

.search-container {
  position: relative;
}

.search-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(242, 101, 34, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  z-index: 0;
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: 9999px;
  pointer-events: none;
}

.search-container:focus-within::before {
  opacity: 1;
  animation: searchPulse 2s infinite alternate;
}

@keyframes searchPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.95);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

.filter-tag {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: 1px solid transparent;
}

.filter-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(242, 101, 34, 0.1) 0%, rgba(242, 101, 34, 0.05) 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 9999px;
}

.filter-tag:hover {
  transform: translateY(-3px);
  border-color: rgba(242, 101, 34, 0.3);
  box-shadow: 0 6px 15px rgba(242, 101, 34, 0.1);
}

.filter-tag:hover::before {
  opacity: 1;
}

.filter-tag.active {
  background: linear-gradient(135deg, #F26522 0%, #ff7e3f 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 6px 15px rgba(242, 101, 34, 0.2);
  transform: translateY(-3px) scale(1.05);
}

.filter-tag.active i {
  animation: iconPop 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes iconPop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.filter-tag .filter-checkbox:checked + span {
  font-weight: 500;
}

.filter-section {
  position: relative;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  transform: translateZ(0);
}

.filter-section:hover {
  transform: translateY(-3px);
}

.filter-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: -1;
  border-radius: 1rem;
}

.filter-section:hover::after {
  opacity: 1;
}

/* Enhanced Skeleton loading animation */
@keyframes skeletonPulse {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeletonPulse 1.5s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.dark .skeleton {
  background: linear-gradient(90deg, #2d3748 25%, #374151 50%, #2d3748 75%);
  background-size: 200px 100%;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-card {
  transition: all 0.3s ease;
  animation: skeletonCardPulse 2s ease-in-out infinite;
  transform: translateZ(0);
}

@keyframes skeletonCardPulse {
  0% {
    transform: translateY(0);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: translateY(0);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
}

.skeleton::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: skeletonShimmer 2s infinite;
}

.dark .skeleton::after {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
}

@keyframes skeletonShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Map view toggle */
.view-toggle {
  transition: all 0.3s ease;
}

.view-toggle button {
  transition: all 0.2s ease;
}

.view-toggle button.active {
  background-color: #F26522;
  color: white;
}

/* Popular searches */
.popular-search-tag {
  transition: all 0.2s ease;
}

.popular-search-tag:hover {
  background-color: #F26522;
  color: white;
  transform: translateY(-2px);
}

/* Recently viewed section */
.recently-viewed {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.recently-viewed::-webkit-scrollbar {
  display: none;
}

.recently-viewed-card {
  min-width: 200px;
  transition: all 0.3s ease;
}

.recently-viewed-card:hover {
  transform: translateY(-5px);
}

/* Bottom sheet for mobile filters */
.mobile-filter-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.mobile-filter-sheet.open {
  transform: translateY(0);
}

.mobile-filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.mobile-filter-overlay.open {
  opacity: 1;
  pointer-events: auto;
}

/* Experience category cards */
.experience-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.experience-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* CTA section overlay */
#cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 100%);
  z-index: 1;
}

/* Newsletter form styles */
.newsletter-input {
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.newsletter-input:focus {
  border-color: #F26522;
  box-shadow: 0 0 0 3px rgba(242, 101, 34, 0.2);
  outline: none;
}

.subscribe-btn {
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.subscribe-btn:hover {
  background-color: #e05a1c;
}

.subscribe-btn:active {
  transform: translateY(1px);
}

/* Featured itinerary card hover effects */
.itinerary-card {
  transition: transform 0.3s ease;
}

.itinerary-card:hover {
  transform: translateY(-5px);
}

/* Benefits section icon pulse effect */
@keyframes iconPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.benefit-icon:hover {
  animation: iconPulse 1.5s ease infinite;
}

/* Mobile menu animation */
#mobile-menu {
  transition: opacity 0.3s ease;
}

#mobile-menu.hidden {
  opacity: 0;
  pointer-events: none;
}

#mobile-menu:not(.hidden) {
  opacity: 1;
}

/* Filter section animations */
@keyframes rippleEffect {
  0% {
    transform: scale(0);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.pulse-highlight {
  animation: pulseHighlight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pulseHighlight {
  0% {
    box-shadow: 0 0 0 0 rgba(242, 101, 34, 0.2);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(242, 101, 34, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(242, 101, 34, 0);
  }
}

/* Mobile filter button animations */
.animate-bounce-subtle {
  animation: bounceSlight 3s infinite;
}

@keyframes bounceSlight {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.animate-pulse-slow {
  animation: pulseSlow 3s infinite;
}

@keyframes pulseSlow {
  0% {
    opacity: 0.2;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.95);
  }
}

/* City Pass Cards Styling */
.city-pass-card {
  position: relative;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, box-shadow;
}

.city-pass-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.city-pass-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0.75rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: -1;
}

.city-pass-card:hover::after {
  opacity: 1;
}

.city-pass-card .backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Animated gradient border effect */
.city-pass-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  z-index: -1;
  background: linear-gradient(90deg, #F26522, #ff7e3f, #F26522);
  border-radius: 0.85rem;
  opacity: 0;
  transition: opacity 0.5s ease;
  background-size: 200% 200%;
  animation: gradientBorder 3s ease infinite;
}

.city-pass-card:hover::before {
  opacity: 1;
}

@keyframes gradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Benefit cards animation */
#city-pass .flex-col {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid transparent;
}

#city-pass .flex-col:hover {
  border-color: rgba(242, 101, 34, 0.2);
  box-shadow: 0 10px 30px rgba(242, 101, 34, 0.1);
}

#city-pass .w-14 {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#city-pass .flex-col:hover .w-14 {
  transform: scale(1.1);
  background-color: rgba(242, 101, 34, 0.2);
}

/* CTA button animation */
#city-pass .inline-flex.items-center.px-8 {
  position: relative;
  overflow: hidden;
}

#city-pass .inline-flex.items-center.px-8::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

#city-pass .inline-flex.items-center.px-8:hover::after {
  left: 100%;
}