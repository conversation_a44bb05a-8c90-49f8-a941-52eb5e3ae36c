/**
 * Trips Page Main Entry Point
 */

import { getCurrentUser } from '../../core/auth.js';
import tripsController from './tripsController.js';

// Authentication check
async function checkAuth() {
    try {
        const user = await getCurrentUser();
        if (!user) {
            console.log('User not authenticated, redirecting to login');
            window.location.href = '../login.html';
            return false;
        }
        console.log('User authenticated:', user.email);
        return true;
    } catch (error) {
        console.error('Authentication check failed:', error);
        window.location.href = '../login.html';
        return false;
    }
}

// Initialize the trips page
async function initializeTripsPage() {
    try {
        // Show loading screen
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }

        // Check authentication
        const isAuthenticated = await checkAuth();
        if (!isAuthenticated) {
            return;
        }

        // Initialize the trips controller
        await tripsController.initialize();

        // Hide loading screen
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }

    } catch (error) {
        console.error('Error initializing trips page:', error);
        
        // Hide loading screen
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }

        // Show error state
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');
        if (errorState && errorMessage) {
            errorMessage.textContent = error.message || 'Failed to load trips page';
            errorState.style.display = 'block';
        }
    }
}

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTripsPage);
} else {
    initializeTripsPage();
}

// Export for debugging
window.tripsController = tripsController;
