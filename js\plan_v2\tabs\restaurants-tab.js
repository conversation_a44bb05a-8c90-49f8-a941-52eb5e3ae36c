/**
 * Restaurants Tab Module
 *
 * Handles rendering and functionality for the restaurants tab using the known
 * consistent data structure from get_trip_plan_json.
 */

/**
 * Generate star rating HTML
 * @param {number} rating - Rating value (0-5)
 * @returns {string} HTML string for star rating
 */
function generateStarRating(rating) {
  const validRating = Math.max(0, Math.min(5, parseFloat(rating) || 0));
  const fullStars = Math.floor(validRating);
  const hasHalfStar = validRating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  let stars = '';
  for (let i = 0; i < fullStars; i++) {
    stars += '<i class="fas fa-star"></i>';
  }
  if (hasHalfStar) {
    stars += '<i class="fas fa-star-half-alt"></i>';
  }
  for (let i = 0; i < emptyStars; i++) {
    stars += '<i class="far fa-star"></i>';
  }

  return stars;
}

/**
 * Get meal type icon
 * @param {string} mealType - Type of meal (breakfast, lunch, dinner)
 * @returns {string} Font Awesome icon class
 */
function getMealTypeIcon(mealType) {
  const iconMap = {
    'breakfast': 'fa-coffee',
    'lunch': 'fa-hamburger',
    'dinner': 'fa-wine-glass-alt',
    'brunch': 'fa-coffee',
    'snack': 'fa-cookie-bite'
  };

  return iconMap[mealType?.toLowerCase()] || 'fa-utensils';
}

/**
 * Format time for display
 * @param {string} time - Time in HH:MM:SS format
 * @returns {string} Formatted time
 */
function formatTime(time) {
  if (!time) return 'Time TBD';

  try {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  } catch (error) {
    return time;
  }
}

/**
 * Sort meals by time chronologically
 * @param {Array} meals - Array of meal objects
 * @returns {Array} Sorted meals array
 */
function sortMealsByTime(meals) {
  return meals.sort((a, b) => {
    const timeA = a.time || '00:00:00';
    const timeB = b.time || '00:00:00';
    return timeA.localeCompare(timeB);
  });
}

/**
 * Check if restaurant has a valid menu
 * @param {Object} restaurant - Restaurant object
 * @returns {boolean} True if restaurant has menu items
 */
function hasValidMenu(restaurant) {
  return restaurant.menu &&
         Array.isArray(restaurant.menu) &&
         restaurant.menu.length > 0 &&
         restaurant.menu.some(section =>
           section.items &&
           Array.isArray(section.items) &&
           section.items.length > 0
         );
}

/**
 * Render restaurants tab using the known consistent data structure
 * @param {Object} dayData - Day data from trip plan
 * @param {Object} AppState - Global application state
 * @returns {Promise<void>}
 */
async function renderRestaurantsTab(dayData, AppState) {
  console.log('🍽️ Rendering restaurants tab for day', dayData?.day_number || 'unknown');
  console.log('🍽️ Day data structure:', dayData);

  const restaurantsTab = document.getElementById('restaurants-tab');
  if (!restaurantsTab) {
    console.error('❌ Restaurants tab element not found in DOM');
    return;
  }

  try {
    // Check for meals data in the known location
    const meals = dayData.meals;

    if (!meals || !Array.isArray(meals) || meals.length === 0) {
      console.log('⚠️ No meals data found in dayData.meals');
      console.log('🔍 Available day data fields:', Object.keys(dayData || {}));

      restaurantsTab.innerHTML = `
        <div class="no-data-message">
          <i class="fas fa-utensils"></i>
          <h3>No restaurant data</h3>
          <p>No meal information available for this day.</p>
          <div class="debug-info" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
            <strong>Debug Info:</strong> Available fields: ${Object.keys(dayData || {}).join(', ')}
          </div>
        </div>
      `;
      return;
    }

    console.log(`✅ Found ${meals.length} meals for the day`);

    // Sort meals by time chronologically
    const sortedMeals = sortMealsByTime(meals);

    // Generate restaurant cards for each meal
    const restaurantCards = sortedMeals.map((meal, index) => {
      try {
        const restaurant = meal.restaurant;

        if (!restaurant) {
          console.warn(`⚠️ No restaurant data for meal ${index + 1}`);
          return '';
        }

        // Extract data directly from the known structure
        const name = restaurant.name || 'Restaurant';
        const cuisine = restaurant.cuisine || 'International';
        const rating = restaurant.rating?.score || 0;
        const reviewsCount = restaurant.rating?.reviews_count || 0;
        const location = restaurant.location?.address || 'Location not specified';
        const description = restaurant.description || 'Delicious local cuisine in a great atmosphere.';
        const primaryImage = restaurant.images?.[0]?.url || 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400';
        const estimatedCost = meal.estimated_cost || 0;
        const mealTime = formatTime(meal.time);
        const mealType = meal.type || 'meal';
        const mealIcon = getMealTypeIcon(mealType);

        // Format pricing
        let priceDisplay = 'Price not available';
        if (estimatedCost > 0) {
          priceDisplay = `$${estimatedCost.toFixed(2)}/person`;
        } else if (restaurant.price_range) {
          const currency = restaurant.price_range.currency === 'USD' ? '$' : restaurant.price_range.currency + ' ';
          const minPrice = restaurant.price_range.min?.toFixed(2) || '0';
          const maxPrice = restaurant.price_range.max?.toFixed(2) || '0';
          priceDisplay = `${currency}${minPrice} - ${currency}${maxPrice}`;
        }

        // Generate star rating HTML
        const starRating = generateStarRating(rating);

        // Check if menu should be displayed
        const showMenuButton = hasValidMenu(restaurant);

        console.log(`📊 Processing meal: ${name} (${mealType} at ${mealTime})`);

        return `
          <div class="restaurant-card" data-meal-index="${index}" data-restaurant-id="${restaurant.id || ''}">
            <div class="restaurant-image">
              <img src="${primaryImage}"
                   alt="${name}"
                   onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400';"
                   loading="lazy">
              <div class="meal-badge">
                <i class="fas ${mealIcon}"></i>
                ${mealType}
              </div>
              <div class="meal-time">${mealTime}</div>
            </div>
            <div class="restaurant-content">
              <div class="restaurant-header">
                <h4>${name}</h4>
                <div class="restaurant-rating">
                  <div class="stars">
                    ${starRating}
                  </div>
                  <span class="rating-score">${rating}/5 (${reviewsCount})</span>
                </div>
              </div>

              <div class="restaurant-details">
                <div class="detail-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>${location}</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-utensils"></i>
                  <span>${cuisine}</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-dollar-sign"></i>
                  <span>${priceDisplay}</span>
                </div>
                ${estimatedCost > 0 ? `
                <div class="detail-item">
                  <i class="fas fa-receipt"></i>
                  <span>Est. cost: $${estimatedCost.toFixed(2)}</span>
                </div>
                ` : ''}
              </div>

              <p class="restaurant-description">
                ${description}
              </p>

              <div class="restaurant-actions">
                ${showMenuButton ? `
                <button class="btn btn-outline btn-sm" onclick="showRestaurantMenu('${index}')">
                  <i class="fas fa-list"></i>
                  View Menu
                </button>
                ` : ''}
                <button class="btn btn-outline btn-sm" onclick="getDirections('${restaurant.location?.coordinates?.lat || ''}', '${restaurant.location?.coordinates?.lng || ''}')">
                  <i class="fas fa-directions"></i>
                  Directions
                </button>
              </div>
            </div>
          </div>
        `;
      } catch (cardError) {
        console.error(`❌ Error processing restaurant card ${index}:`, cardError);
        return `
          <div class="restaurant-card error">
            <div class="restaurant-content">
              <h4>Restaurant ${index + 1}</h4>
              <p>Error loading restaurant information</p>
            </div>
          </div>
        `;
      }
    }).filter(card => card.length > 0); // Remove empty cards

    // Render all restaurant cards
    restaurantsTab.innerHTML = `
      <div class="restaurants-container">
        <div class="restaurants-header">
          <h3>Today's Dining</h3>
          <p>${restaurantCards.length} meal${restaurantCards.length !== 1 ? 's' : ''} planned</p>
        </div>
        <div class="restaurants-grid">
          ${restaurantCards.join('')}
        </div>
      </div>
    `;

    // Store meals data for menu modal
    window.currentMealsData = sortedMeals;

    console.log(`✅ Successfully rendered ${restaurantCards.length} restaurant cards`);

  } catch (error) {
    console.error('❌ Error rendering restaurants tab:', error);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Day data that caused error:', dayData);

    restaurantsTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading restaurants</h3>
        <p>Unable to load restaurant information. Please try again.</p>
        <div class="error-details" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
          <strong>Error:</strong> ${error.message}<br>
          <strong>Day:</strong> ${dayData?.day_number || 'unknown'}<br>
          <strong>Available fields:</strong> ${Object.keys(dayData || {}).join(', ')}
        </div>
      </div>
    `;
  }
}

/**
 * Show restaurant menu in a modal
 * @param {string} mealIndex - Index of the meal in the current meals data
 */
function showRestaurantMenu(mealIndex) {
  console.log('🍽️ Showing menu for meal index:', mealIndex);

  if (!window.currentMealsData || !window.currentMealsData[mealIndex]) {
    console.error('❌ No meal data found for index:', mealIndex);
    return;
  }

  const meal = window.currentMealsData[mealIndex];
  const restaurant = meal.restaurant;

  if (!hasValidMenu(restaurant)) {
    console.warn('⚠️ No valid menu found for restaurant:', restaurant.name);
    return;
  }

  // Create menu modal HTML
  const menuSections = restaurant.menu.map(section => {
    if (!section.items || section.items.length === 0) return '';

    // Sort items by price_numeric if available
    const sortedItems = section.items.sort((a, b) => {
      const priceA = a.price_numeric || 0;
      const priceB = b.price_numeric || 0;
      return priceA - priceB;
    });

    const itemsHtml = sortedItems.map(item => `
      <div class="menu-item">
        <div class="menu-item-header">
          <span class="menu-item-name">${item.item_name || 'Menu Item'}</span>
          <span class="menu-item-price">${item.price || 'Price on request'}</span>
        </div>
        ${item.description ? `<p class="menu-item-description">${item.description}</p>` : ''}
      </div>
    `).join('');

    return `
      <div class="menu-section">
        <h4 class="menu-section-title">${section.section_name || 'Menu Items'}</h4>
        <div class="menu-items">
          ${itemsHtml}
        </div>
      </div>
    `;
  }).filter(section => section.length > 0).join('');

  // Create and show modal
  const modalHtml = `
    <div class="menu-modal-overlay" onclick="closeRestaurantMenu()">
      <div class="menu-modal" onclick="event.stopPropagation()">
        <div class="menu-modal-header">
          <h3>${restaurant.name} - Menu</h3>
          <button class="menu-modal-close" onclick="closeRestaurantMenu()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="menu-modal-content">
          ${menuSections}
        </div>
      </div>
    </div>
  `;

  // Add modal to DOM
  const existingModal = document.querySelector('.menu-modal-overlay');
  if (existingModal) {
    existingModal.remove();
  }

  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Add escape key listener
  document.addEventListener('keydown', handleMenuModalEscape);
}

/**
 * Close restaurant menu modal
 */
function closeRestaurantMenu() {
  const modal = document.querySelector('.menu-modal-overlay');
  if (modal) {
    modal.remove();
  }

  // Remove escape key listener
  document.removeEventListener('keydown', handleMenuModalEscape);
}

/**
 * Handle escape key for menu modal
 * @param {KeyboardEvent} event - Keyboard event
 */
function handleMenuModalEscape(event) {
  if (event.key === 'Escape') {
    closeRestaurantMenu();
  }
}

/**
 * Get directions to restaurant
 * @param {string} lat - Latitude
 * @param {string} lng - Longitude
 */
function getDirections(lat, lng) {
  console.log('🗺️ Getting directions to:', lat, lng);

  if (!lat || !lng) {
    alert('Location coordinates not available for this restaurant.');
    return;
  }

  // Open Google Maps with directions
  const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
  window.open(url, '_blank');
}

// Make functions available globally for onclick handlers
if (typeof window !== 'undefined') {
  window.showRestaurantMenu = showRestaurantMenu;
  window.closeRestaurantMenu = closeRestaurantMenu;
  window.getDirections = getDirections;
}

export default renderRestaurantsTab;
