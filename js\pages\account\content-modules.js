/**
 * Content Modules
 * Contains ItinerariesManager and GuideDashboardManager functionality
 */

/**
 * Itineraries Manager
 * Handles the itineraries section functionality
 */
export class ItinerariesManager {
  constructor() {
    this.itinerariesSection = document.getElementById('itineraries');
    this.itinerariesGrid = this.itinerariesSection.querySelector('.itinerary-grid');
    this.itineraries = [];
  }

  /**
   * Initialize the itineraries manager
   * @param {Array} itineraries - Array of itinerary objects
   */
  init(itineraries) {
    this.itineraries = itineraries || [];
    this.renderItineraries();
    this.setupEventListeners();
  }

  /**
   * Render the itineraries in the grid
   */
  renderItineraries() {
    if (!this.itinerariesGrid) return;

    // Clear existing itineraries
    this.itinerariesGrid.innerHTML = '';

    if (this.itineraries.length === 0) {
      this.renderEmptyState();
      return;
    }

    // Add each itinerary card
    this.itineraries.forEach(itinerary => {
      const itineraryCard = this.createItineraryCard(itinerary);
      this.itinerariesGrid.appendChild(itineraryCard);
    });
  }

  /**
   * Create an itinerary card element
   * @param {Object} itinerary - Itinerary data object
   * @returns {HTMLElement} The created card element
   */
  createItineraryCard(itinerary) {
    const card = document.createElement('div');
    card.className = 'itinerary-card';
    card.dataset.id = itinerary.id;

    // Get the status class
    let statusClass = 'status-pending';
    if (itinerary.status === 'confirmed') {
      statusClass = 'status-accepted';
    } else if (itinerary.status === 'cancelled') {
      statusClass = 'status-declined';
    }

    // Format the status text
    const statusText = itinerary.status.charAt(0).toUpperCase() + itinerary.status.slice(1);

    // Format dates
    let formattedDates = '';
    if (itinerary.startDate && itinerary.endDate) {
      // Format dates in a readable format
      const startDate = new Date(itinerary.startDate);
      const endDate = new Date(itinerary.endDate);

      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      formattedDates = `${startDate.toLocaleDateString(undefined, options)} - ${endDate.toLocaleDateString(undefined, options)}`;
    } else if (itinerary.dates) {
      // Use dates if already formatted
      formattedDates = itinerary.dates;
    }

    card.innerHTML = `
      <div class="itinerary-image">
        <img src="${itinerary.image}" alt="${itinerary.title}">
      </div>
      <div class="itinerary-details">
        <div class="itinerary-header">
          <h3>${itinerary.title}</h3>
          <span class="status-badge ${statusClass}">${statusText}</span>
        </div>
        <p class="itinerary-dates">
          <i class="far fa-calendar"></i>
          ${formattedDates}
        </p>
        <div class="itinerary-actions">
          <button class="action-button view-button">
            <i class="far fa-eye"></i> View
          </button>
          <button class="action-button edit-button">
            <i class="far fa-edit"></i> Edit
          </button>
          <button class="action-button delete-button">
            <i class="far fa-trash-alt"></i> Delete
          </button>
        </div>
      </div>
    `;

    return card;
  }

  /**
   * Render an empty state when no itineraries exist
   */
  renderEmptyState() {
    const emptyState = document.createElement('div');
    emptyState.className = 'empty-state';
    emptyState.innerHTML = `
      <div class="empty-state-icon">
        <i class="fas fa-route"></i>
      </div>
      <h3>No Itineraries Yet</h3>
      <p>Start planning your next adventure!</p>
      <button class="primary-button">
        <i class="fas fa-plus"></i>
        Create New Trip
      </button>
    `;

    this.itinerariesGrid.appendChild(emptyState);

    // Add click handler for the button
    const createButton = emptyState.querySelector('.primary-button');
    createButton.addEventListener('click', this.handleCreateTrip.bind(this));
  }

  /**
   * Set up event listeners for itinerary interactions
   */
  setupEventListeners() {
    // Create trip button in the header
    const createTripButton = this.itinerariesSection.querySelector('.primary-button');
    if (createTripButton) {
      createTripButton.addEventListener('click', this.handleCreateTrip.bind(this));
    }

    // Delegate event handling for card buttons
    this.itinerariesGrid.addEventListener('click', (event) => {
      const target = event.target.closest('button');
      if (!target) return;

      const card = target.closest('.itinerary-card');
      if (!card) return;

      const itineraryId = card.dataset.id;

      if (target.classList.contains('view-button')) {
        this.handleViewItinerary(itineraryId);
      } else if (target.classList.contains('edit-button')) {
        this.handleEditItinerary(itineraryId);
      } else if (target.classList.contains('delete-button')) {
        this.handleDeleteItinerary(itineraryId, card);
      }
    });
  }

  /**
   * Handle creating a new trip
   */
  handleCreateTrip() {
    // In a real app, this would redirect to the trip creation wizard
    console.log('Create new trip clicked');
    window.location.href = '/wizard.html';
  }

  /**
   * Handle viewing an itinerary
   * @param {string} itineraryId - ID of the itinerary to view
   */
  handleViewItinerary(itineraryId) {
    // In a real app, this would redirect to the itinerary view page
    console.log('View itinerary clicked:', itineraryId);
    window.location.href = `/itinerary.html?id=${itineraryId}`;
  }

  /**
   * Handle editing an itinerary
   * @param {string} itineraryId - ID of the itinerary to edit
   */
  handleEditItinerary(itineraryId) {
    // In a real app, this would redirect to the itinerary edit page
    console.log('Edit itinerary clicked:', itineraryId);
    window.location.href = `/wizard.html?edit=${itineraryId}`;
  }

  /**
   * Handle deleting an itinerary
   * @param {string} itineraryId - ID of the itinerary to delete
   * @param {HTMLElement} cardElement - The card element to remove
   */
  handleDeleteItinerary(itineraryId, cardElement) {
    if (confirm('Are you sure you want to delete this itinerary?')) {
      // In a real app, this would send a delete request to the server
      console.log('Delete itinerary clicked:', itineraryId);

      // Remove from UI
      cardElement.classList.add('card-fade-out');
      setTimeout(() => {
        cardElement.remove();

        // Check if we need to show empty state
        if (this.itinerariesGrid.children.length === 0) {
          this.renderEmptyState();
        }
      }, 300);

      // Remove from local data
      this.itineraries = this.itineraries.filter(item => item.id !== itineraryId);
    }
  }
}



// Export default object with all classes
export default {
  ItinerariesManager,
  GuideDashboardManager
};