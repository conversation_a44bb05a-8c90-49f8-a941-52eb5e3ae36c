<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Morocco Adventure | Tsafira Travel Planner</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Modular CSS Architecture -->
  <link rel="stylesheet" href="../css/plan_v2/base.css">
  <link rel="stylesheet" href="../css/plan_v2/layout.css">
  <link rel="stylesheet" href="../css/plan_v2/components.css">
  <link rel="stylesheet" href="../css/plan_v2/budget.css">
  <link rel="stylesheet" href="../css/plan_v2/weather.css">
  <link rel="stylesheet" href="../css/plan_v2/timeline.css">
  <link rel="stylesheet" href="../css/plan_v2/responsive.css">
</head>
<body>
  <!-- Loading Overlay -->
  <div id="loading-overlay" class="loading-overlay hidden">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h3>Loading your adventure...</h3>
      <p>Preparing your personalized itinerary</p>
    </div>
  </div>

  <!-- Header -->
  <header class="header">
    <div class="container">
      <div class="header-content">
        <div class="logo">
          <a href="/">
            <i class="fas fa-compass"></i>
            <span>Tsafira</span>
          </a>
        </div>
        <nav class="main-nav">
          <a href="../pages/account.html" class="nav-link">
            <i class="fas fa-user"></i>
            <span>My Trips</span>
          </a>
          <a href="../pages/support.html" class="nav-link">
            <i class="fas fa-headset"></i>
            <span>Support</span>
          </a>
          <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme">
            <i class="fas fa-moon"></i>
          </button>
        </nav>
      </div>
    </div>
  </header>

  <!-- Trip Status Banner -->
  <div id="trip-status-banner" class="trip-status-banner hidden">
    <div class="container">
      <div class="status-content">
        <i class="status-icon"></i>
        <div class="status-text">
          <h4 class="status-title"></h4>
          <p class="status-message"></p>
        </div>
        <button class="status-close" aria-label="Close status banner">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Trip Header -->
  <section class="trip-header">
    <div class="container">
      <div class="trip-header-content">
        <div class="trip-info">
          <h1 class="trip-title" contenteditable="true" data-placeholder="Trip Name">
            Morocco Adventure
          </h1>
          <div class="trip-meta">
            <div class="trip-dates">
              <i class="fas fa-calendar-alt"></i>
              <span>March 15 - March 22, 2025 • 8 Days</span>
            </div>
            <div class="trip-status">
              <span class="status-badge status-active">Active Trip</span>
            </div>
          </div>
        </div>
        <div class="trip-actions">
          <button class="btn btn-outline-white">
            <i class="fas fa-share-alt"></i>
            <span>Share</span>
          </button>
          <button class="btn btn-outline-white">
            <i class="fas fa-download"></i>
            <span>Export PDF</span>
          </button>
          <button class="btn btn-white">
            <i class="fas fa-bookmark"></i>
            <span>Save Changes</span>
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Overview Section -->
  <section class="overview-section">
    <div class="container">
      <div class="overview-grid">
        <!-- Budget Overview -->
        <div class="budget-overview">
          <div class="card">
            <div class="card-header">
              <h2>
                <i class="fas fa-wallet"></i>
                Trip Budget
              </h2>
              <div class="budget-status">
                <span class="budget-health good">Healthy</span>
              </div>
            </div>
            
            <div class="budget-summary">
              <div class="budget-total">
                <div class="amount">$3,800</div>
                <div class="label">Total Budget</div>
              </div>
              <div class="budget-spent">
                <div class="amount">$2,450</div>
                <div class="label">Spent to Date</div>
              </div>
              <div class="budget-remaining">
                <div class="amount">$1,350</div>
                <div class="label">Remaining</div>
              </div>
            </div>

            <div class="budget-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 64%"></div>
              </div>
              <div class="progress-labels">
                <span>64% Used</span>
                <span>36% Remaining</span>
              </div>
            </div>

            <div class="budget-categories">
              <div class="category-item">
                <div class="category-info">
                  <i class="fas fa-bed category-icon"></i>
                  <span class="category-name">Accommodation</span>
                </div>
                <div class="category-amounts">
                  <span class="spent">$1,200</span>
                  <span class="total">/ $1,500</span>
                </div>
                <div class="category-progress">
                  <div class="progress-bar small">
                    <div class="progress-fill" style="width: 80%"></div>
                  </div>
                </div>
              </div>
              
              <div class="category-item">
                <div class="category-info">
                  <i class="fas fa-utensils category-icon"></i>
                  <span class="category-name">Meals</span>
                </div>
                <div class="category-amounts">
                  <span class="spent">$680</span>
                  <span class="total">/ $800</span>
                </div>
                <div class="category-progress">
                  <div class="progress-bar small">
                    <div class="progress-fill" style="width: 85%"></div>
                  </div>
                </div>
              </div>
              
              <div class="category-item">
                <div class="category-info">
                  <i class="fas fa-map-marker-alt category-icon"></i>
                  <span class="category-name">Activities</span>
                </div>
                <div class="category-amounts">
                  <span class="spent">$420</span>
                  <span class="total">/ $900</span>
                </div>
                <div class="category-progress">
                  <div class="progress-bar small">
                    <div class="progress-fill" style="width: 47%"></div>
                  </div>
                </div>
              </div>
              
              <div class="category-item">
                <div class="category-info">
                  <i class="fas fa-car category-icon"></i>
                  <span class="category-name">Transport</span>
                </div>
                <div class="category-amounts">
                  <span class="spent">$150</span>
                  <span class="total">/ $600</span>
                </div>
                <div class="category-progress">
                  <div class="progress-bar small">
                    <div class="progress-fill" style="width: 25%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Weather Overview -->
        <div class="weather-overview">
          <div class="card">
            <div class="card-header">
              <h2>
                <i class="fas fa-cloud-sun"></i>
                Weather Forecast
              </h2>
              <div class="weather-location">
                <i class="fas fa-map-marker-alt"></i>
                <span>Marrakech, Morocco</span>
              </div>
            </div>
            
            <div class="current-weather">
              <div class="weather-main">
                <div class="weather-icon">
                  <i class="fas fa-sun"></i>
                </div>
                <div class="weather-temp">
                  <span class="temp">25°C</span>
                  <span class="condition">Sunny</span>
                </div>
              </div>
              <div class="weather-details">
                <div class="detail-item">
                  <i class="fas fa-droplet"></i>
                  <span>30%</span>
                  <small>Humidity</small>
                </div>
                <div class="detail-item">
                  <i class="fas fa-wind"></i>
                  <span>12 km/h</span>
                  <small>Wind</small>
                </div>
                <div class="detail-item">
                  <i class="fas fa-sunrise"></i>
                  <span>6:45 AM</span>
                  <small>Sunrise</small>
                </div>
                <div class="detail-item">
                  <i class="fas fa-sunset"></i>
                  <span>7:20 PM</span>
                  <small>Sunset</small>
                </div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <div class="day-label">Tomorrow</div>
                <div class="day-icon">
                  <i class="fas fa-cloud-sun"></i>
                </div>
                <div class="day-temp">
                  <span class="high">28°</span>
                  <span class="low">18°</span>
                </div>
                <div class="day-condition">Partly Cloudy</div>
              </div>
              
              <div class="forecast-day">
                <div class="day-label">Day 3</div>
                <div class="day-icon">
                  <i class="fas fa-sun"></i>
                </div>
                <div class="day-temp">
                  <span class="high">30°</span>
                  <span class="low">20°</span>
                </div>
                <div class="day-condition">Sunny</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Map Section -->
  <section class="map-section">
    <div class="container">
      <div class="map-container">
        <div class="map-header">
          <h2>
            <i class="fas fa-map-marked-alt"></i>
            Trip Itinerary Map
          </h2>
          <p>Explore your complete journey with locations and routes</p>
        </div>

        <div class="map-wrapper">
          <div id="trip-map" class="trip-map">
            <!-- Folium map will be inserted here -->
            <div class="map-placeholder">
              <div class="map-placeholder-content">
                <i class="fas fa-map"></i>
                <h3>Interactive Map Loading...</h3>
                <p>Your trip locations and routes will appear here</p>
              </div>
            </div>
          </div>

          <div class="map-legend">
            <h4>Map Legend</h4>
            <div class="legend-items">
              <div class="legend-item">
                <div class="legend-marker accommodation">
                  <i class="fas fa-bed"></i>
                </div>
                <span>Accommodations</span>
              </div>
              <div class="legend-item">
                <div class="legend-marker restaurant">
                  <i class="fas fa-utensils"></i>
                </div>
                <span>Restaurants</span>
              </div>
              <div class="legend-item">
                <div class="legend-marker activity">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <span>Activities</span>
              </div>
              <div class="legend-item">
                <div class="legend-marker route">
                  <i class="fas fa-route"></i>
                </div>
                <span>Travel Routes</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Day Navigation Section -->
  <section class="day-navigation-section">
    <div class="container">
      <div class="day-navigation-header">
        <div class="navigation-controls">
          <button id="prev-day" class="nav-btn" aria-label="Previous day">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button id="next-day" class="nav-btn" aria-label="Next day">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <div class="current-day-info">
          <h2 class="day-title">Day 3 - March 17, 2025</h2>
          <p class="day-subtitle">Exploring Marrakech</p>
        </div>

        <div class="day-actions">
          <button class="btn btn-outline btn-sm">
            <i class="fas fa-edit"></i>
            <span>Edit Day</span>
          </button>
        </div>
      </div>

      <!-- Day Selector Cards -->
      <div class="day-selector">
        <div class="day-card" data-day="1">
          <div class="day-number">1</div>
          <div class="day-date">Mar 15</div>
          <div class="day-status">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>

        <div class="day-card" data-day="2">
          <div class="day-number">2</div>
          <div class="day-date">Mar 16</div>
          <div class="day-status">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>

        <div class="day-card active" data-day="3">
          <div class="day-number">3</div>
          <div class="day-date">Mar 17</div>
          <div class="day-status">
            <i class="fas fa-clock"></i>
          </div>
        </div>

        <div class="day-card" data-day="4">
          <div class="day-number">4</div>
          <div class="day-date">Mar 18</div>
          <div class="day-status">
            <i class="fas fa-circle"></i>
          </div>
        </div>

        <div class="day-card" data-day="5">
          <div class="day-number">5</div>
          <div class="day-date">Mar 19</div>
          <div class="day-status">
            <i class="fas fa-circle"></i>
          </div>
        </div>

        <div class="day-card" data-day="6">
          <div class="day-number">6</div>
          <div class="day-date">Mar 20</div>
          <div class="day-status">
            <i class="fas fa-circle"></i>
          </div>
        </div>

        <div class="day-card" data-day="7">
          <div class="day-number">7</div>
          <div class="day-date">Mar 21</div>
          <div class="day-status">
            <i class="fas fa-circle"></i>
          </div>
        </div>

        <div class="day-card" data-day="8">
          <div class="day-number">8</div>
          <div class="day-date">Mar 22</div>
          <div class="day-status">
            <i class="fas fa-circle"></i>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Day Content Section -->
  <section class="day-content-section">
    <div class="container">
      <!-- Day Content Tabs -->
      <div class="day-tabs">
        <button class="day-tab active" data-tab="timeline">
          <i class="fas fa-clock"></i>
          <span>Timeline</span>
        </button>
        <button class="day-tab" data-tab="accommodations">
          <i class="fas fa-bed"></i>
          <span>Accommodations</span>
        </button>
        <button class="day-tab" data-tab="restaurants">
          <i class="fas fa-utensils"></i>
          <span>Restaurants</span>
        </button>
        <button class="day-tab" data-tab="activities">
          <i class="fas fa-map-marker-alt"></i>
          <span>Activities</span>
        </button>
        <button class="day-tab" data-tab="transport">
          <i class="fas fa-car"></i>
          <span>Transport</span>
        </button>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <!-- Timeline Tab -->
        <div id="timeline-tab" class="tab-pane active">
          <div class="timeline-container">
            <div class="timeline-header">
              <h3>Complete Day Timeline</h3>
              <p>All activities, meals, and transport for Day 3</p>
            </div>

            <div class="timeline">
              <div class="timeline-item">
                <div class="timeline-time">08:00</div>
                <div class="timeline-icon accommodation">
                  <i class="fas fa-bed"></i>
                </div>
                <div class="timeline-content">
                  <h4>Hotel Check-out</h4>
                  <p>La Mamounia Hotel</p>
                  <span class="timeline-category">Accommodation</span>
                </div>
              </div>

              <div class="timeline-item">
                <div class="timeline-time">09:00</div>
                <div class="timeline-icon transport">
                  <i class="fas fa-car"></i>
                </div>
                <div class="timeline-content">
                  <h4>Travel to Essaouira</h4>
                  <p>Car rental • 2.5 hours • 180 km</p>
                  <span class="timeline-category">Transport</span>
                </div>
              </div>

              <div class="timeline-item">
                <div class="timeline-time">12:30</div>
                <div class="timeline-icon meal">
                  <i class="fas fa-utensils"></i>
                </div>
                <div class="timeline-content">
                  <h4>Lunch at Taros Café</h4>
                  <p>Moroccan cuisine with ocean view</p>
                  <span class="timeline-category">Meal</span>
                </div>
              </div>

              <div class="timeline-item">
                <div class="timeline-time">15:00</div>
                <div class="timeline-icon accommodation">
                  <i class="fas fa-bed"></i>
                </div>
                <div class="timeline-content">
                  <h4>Hotel Check-in</h4>
                  <p>Riad Mimouna</p>
                  <span class="timeline-category">Accommodation</span>
                </div>
              </div>

              <div class="timeline-item">
                <div class="timeline-time">16:30</div>
                <div class="timeline-icon activity">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="timeline-content">
                  <h4>Essaouira Medina Walking Tour</h4>
                  <p>Explore the UNESCO World Heritage site</p>
                  <span class="timeline-category">Activity</span>
                </div>
              </div>

              <div class="timeline-item">
                <div class="timeline-time">19:30</div>
                <div class="timeline-icon meal">
                  <i class="fas fa-utensils"></i>
                </div>
                <div class="timeline-content">
                  <h4>Dinner at Ocean Vagabond</h4>
                  <p>Fresh seafood by the harbor</p>
                  <span class="timeline-category">Meal</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Accommodations Tab -->
        <div id="accommodations-tab" class="tab-pane">
          <div class="accommodation-card">
            <div class="accommodation-image">
              <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800" alt="Riad Mimouna">
              <div class="accommodation-badge">Primary Choice</div>
            </div>
            <div class="accommodation-content">
              <div class="accommodation-header">
                <h3>Riad Mimouna</h3>
                <div class="accommodation-rating">
                  <div class="stars">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star-half-alt"></i>
                  </div>
                  <span class="rating-score">4.5</span>
                </div>
              </div>

              <div class="accommodation-details">
                <div class="detail-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>Essaouira Medina, Morocco</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-bed"></i>
                  <span>Deluxe Room with Ocean View</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>2 Guests</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-calendar"></i>
                  <span>1 Night</span>
                </div>
              </div>

              <div class="accommodation-amenities">
                <div class="amenity-tag">
                  <i class="fas fa-wifi"></i>
                  <span>Free WiFi</span>
                </div>
                <div class="amenity-tag">
                  <i class="fas fa-swimming-pool"></i>
                  <span>Pool</span>
                </div>
                <div class="amenity-tag">
                  <i class="fas fa-utensils"></i>
                  <span>Restaurant</span>
                </div>
                <div class="amenity-tag">
                  <i class="fas fa-spa"></i>
                  <span>Spa</span>
                </div>
              </div>

              <div class="accommodation-pricing">
                <div class="price-info">
                  <span class="price">$180</span>
                  <span class="period">/ night</span>
                </div>
                <div class="accommodation-actions">
                  <button class="btn btn-outline">View Details</button>
                  <button class="btn btn-primary">Change Hotel</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Restaurants Tab -->
        <div id="restaurants-tab" class="tab-pane">
          <div class="restaurants-grid">
            <div class="restaurant-card">
              <div class="restaurant-image">
                <img src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400" alt="Taros Café">
                <div class="meal-badge">Lunch</div>
              </div>
              <div class="restaurant-content">
                <div class="restaurant-header">
                  <h4>Taros Café</h4>
                  <div class="restaurant-time">12:30 PM</div>
                </div>
                <div class="restaurant-details">
                  <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Place Moulay Hassan, Essaouira</span>
                  </div>
                  <div class="detail-item">
                    <i class="fas fa-utensils"></i>
                    <span>Moroccan, Mediterranean</span>
                  </div>
                  <div class="detail-item">
                    <i class="fas fa-star"></i>
                    <span>4.2/5 (324 reviews)</span>
                  </div>
                </div>
                <p class="restaurant-description">
                  Rooftop restaurant with stunning ocean views, serving traditional Moroccan dishes and fresh seafood.
                </p>
                <div class="restaurant-pricing">
                  <span class="price">$35/person</span>
                  <div class="restaurant-actions">
                    <button class="btn btn-outline btn-sm">View Menu</button>
                    <button class="btn btn-outline btn-sm">Directions</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="restaurant-card">
              <div class="restaurant-image">
                <img src="https://images.unsplash.com/photo-1559339352-11d035aa65de?w=400" alt="Ocean Vagabond">
                <div class="meal-badge">Dinner</div>
              </div>
              <div class="restaurant-content">
                <div class="restaurant-header">
                  <h4>Ocean Vagabond</h4>
                  <div class="restaurant-time">7:30 PM</div>
                </div>
                <div class="restaurant-details">
                  <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Port of Essaouira</span>
                  </div>
                  <div class="detail-item">
                    <i class="fas fa-utensils"></i>
                    <span>Seafood, International</span>
                  </div>
                  <div class="detail-item">
                    <i class="fas fa-star"></i>
                    <span>4.6/5 (189 reviews)</span>
                  </div>
                </div>
                <p class="restaurant-description">
                  Waterfront dining with the freshest catch of the day, perfect for watching the sunset over the Atlantic.
                </p>
                <div class="restaurant-pricing">
                  <span class="price">$45/person</span>
                  <div class="restaurant-actions">
                    <button class="btn btn-outline btn-sm">View Menu</button>
                    <button class="btn btn-outline btn-sm">Directions</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Activities Tab -->
        <div id="activities-tab" class="tab-pane">
          <div class="activity-card">
            <div class="activity-content">
              <div class="activity-header">
                <h3>Essaouira Medina Walking Tour</h3>
                <div class="activity-time">4:30 PM - 7:30 PM</div>
                <div class="activity-duration">3 hours</div>
              </div>

              <div class="activity-details">
                <div class="detail-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>Essaouira Medina, Morocco</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>Small Group (max 12 people)</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-language"></i>
                  <span>English, French, Arabic</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-star"></i>
                  <span>4.8/5 (156 reviews)</span>
                </div>
              </div>

              <div class="activity-description">
                <p>Explore the UNESCO World Heritage site of Essaouira's medina with a local guide. Discover the Portuguese and French colonial architecture, visit traditional workshops, and learn about the city's rich history as a trading port.</p>

                <div class="activity-highlights">
                  <h4>Tour Highlights:</h4>
                  <ul>
                    <li>Skala de la Ville fortifications</li>
                    <li>Traditional woodworking workshops</li>
                    <li>Spice and jewelry souks</li>
                    <li>Moulay Hassan Square</li>
                    <li>Port and fishing harbor</li>
                  </ul>
                </div>
              </div>

              <div class="activity-pricing">
                <div class="price-info">
                  <span class="price">$25</span>
                  <span class="period">/ person</span>
                </div>
                <div class="activity-actions">
                  <button class="btn btn-outline">View Details</button>
                  <button class="btn btn-primary">Modify Booking</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Transport Tab -->
        <div id="transport-tab" class="tab-pane">
          <div class="transport-card">
            <div class="transport-header">
              <div class="transport-icon">
                <i class="fas fa-car"></i>
              </div>
              <div class="transport-info">
                <h3>Car Rental - Marrakech to Essaouira</h3>
                <p class="transport-route">Marrakech → Essaouira</p>
              </div>
              <div class="transport-status">
                <span class="status-badge confirmed">Confirmed</span>
              </div>
            </div>

            <div class="transport-details">
              <div class="transport-timeline">
                <div class="transport-point">
                  <div class="point-time">9:00 AM</div>
                  <div class="point-location">
                    <strong>Pickup: La Mamounia Hotel</strong>
                    <p>Avenue Bab Jdid, Marrakech</p>
                  </div>
                </div>

                <div class="transport-journey">
                  <div class="journey-line"></div>
                  <div class="journey-info">
                    <span class="duration">2h 30min</span>
                    <span class="distance">180 km</span>
                  </div>
                </div>

                <div class="transport-point">
                  <div class="point-time">11:30 AM</div>
                  <div class="point-location">
                    <strong>Arrival: Essaouira</strong>
                    <p>Riad Mimouna, Essaouira Medina</p>
                  </div>
                </div>
              </div>

              <div class="transport-summary">
                <div class="transport-info">
                  <h4>Transport Details</h4>
                  <div class="transport-specs">
                    <div class="spec-item">
                      <i class="fas fa-car"></i>
                      <span>Car Rental</span>
                    </div>
                    <div class="spec-item">
                      <i class="fas fa-route"></i>
                      <span>180 km journey</span>
                    </div>
                    <div class="spec-item">
                      <i class="fas fa-clock"></i>
                      <span>2h 30min duration</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="transport-pricing">
                <div class="price-breakdown">
                  <div class="price-item">
                    <span>Daily Rate:</span>
                    <span>$20.00</span>
                  </div>
                  <div class="price-item">
                    <span>Insurance:</span>
                    <span>Included</span>
                  </div>
                  <div class="price-item total">
                    <span>Total Cost:</span>
                    <strong>$20.00</strong>
                  </div>
                </div>

                <div class="transport-actions">
                  <button class="btn btn-outline">Contact Rental</button>
                  <button class="btn btn-outline">View Route</button>
                  <button class="btn btn-primary">Modify Booking</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- JavaScript Modules -->
  <!-- Enhanced loading system with proper error handling and debugging -->
  <script type="module">
    // Enhanced Plan V2 Loading System
    (async function() {
      console.log('🚀 Plan V2: Enhanced loading system starting...');

      // Step 1: Ensure DOM is ready
      function waitForDOM() {
        return new Promise((resolve) => {
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', resolve);
          } else {
            resolve();
          }
        });
      }

      // Step 2: Show loading state immediately
      function showInitialLoading() {
        document.body.style.visibility = 'visible';
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
          loadingOverlay.classList.remove('hidden');
          console.log('✅ Loading overlay displayed');
        } else {
          console.warn('⚠️ Loading overlay element not found');
        }
      }

      // Step 3: Enhanced error display
      function showEnhancedError(error, context = 'initialization') {
        console.error(`❌ Plan V2: ${context} error:`, error);

        document.body.style.visibility = 'visible';

        // Hide loading overlay if it exists
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
          loadingOverlay.classList.add('hidden');
        }

        // Create enhanced error display
        const errorContainer = document.createElement('div');
        errorContainer.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.9);
          color: white;
          padding: 40px;
          font-family: Arial, sans-serif;
          z-index: 10000;
          overflow-y: auto;
        `;

        errorContainer.innerHTML = `
          <div style="max-width: 800px; margin: 0 auto;">
            <h1 style="color: #ff6b6b; margin-bottom: 20px;">
              🚨 Plan V2 Loading Error
            </h1>
            <div style="background: #2d3748; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #ffd93d; margin-top: 0;">Error Details:</h3>
              <p><strong>Context:</strong> ${context}</p>
              <p><strong>Message:</strong> ${error.message}</p>
              <p><strong>Type:</strong> ${error.name || 'Unknown'}</p>
            </div>

            <div style="background: #2d3748; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #ffd93d; margin-top: 0;">Stack Trace:</h3>
              <pre style="background: #1a202c; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; line-height: 1.4;">${error.stack || 'No stack trace available'}</pre>
            </div>

            <div style="background: #2d3748; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #ffd93d; margin-top: 0;">Debugging Information:</h3>
              <p><strong>URL:</strong> ${window.location.href}</p>
              <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
              <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            </div>

            <div style="text-align: center;">
              <button onclick="window.location.reload()" style="
                padding: 12px 24px;
                margin: 10px;
                background: #4299e1;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
              ">🔄 Reload Page</button>

              <button onclick="window.location.href='../index.html'" style="
                padding: 12px 24px;
                margin: 10px;
                background: #48bb78;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
              ">🏠 Go Home</button>

              <button onclick="this.parentElement.parentElement.parentElement.style.display='none'" style="
                padding: 12px 24px;
                margin: 10px;
                background: #ed8936;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
              ">❌ Close Error</button>
            </div>
          </div>
        `;

        document.body.appendChild(errorContainer);
      }

      try {
        // Wait for DOM to be ready
        await waitForDOM();
        console.log('✅ DOM is ready');

        // Show loading state
        showInitialLoading();

        // Check for trip ID in URL
        const urlParams = new URLSearchParams(window.location.search);
        const tripId = urlParams.get('id');

        if (!tripId) {
          throw new Error('Trip ID is required. Please add ?id=123 to the URL (e.g., plan_v2.html?id=6)');
        }

        console.log(`✅ Trip ID found: ${tripId}`);

        // Test module imports before loading main application
        console.log('📦 Testing critical module imports...');

        try {
          const { supabase } = await import('../js/supabaseClient.js');
          if (!supabase) {
            throw new Error('Supabase client not available');
          }
          console.log('✅ Supabase client imported successfully');
        } catch (importError) {
          throw new Error(`Failed to import Supabase client: ${importError.message}`);
        }

        try {
          const { DataManager } = await import('../js/plan_v2/data-manager.js');
          if (!DataManager) {
            throw new Error('DataManager class not available');
          }
          console.log('✅ DataManager imported successfully');
        } catch (importError) {
          throw new Error(`Failed to import DataManager: ${importError.message}`);
        }

        // Load the main application
        console.log('🚀 Loading main application...');
        await import('../js/plan_v2/main.js');
        console.log('✅ Plan V2: Main application loaded successfully');

      } catch (error) {
        showEnhancedError(error, 'module loading');
      }
    })();
  </script>
</body>
</html>
