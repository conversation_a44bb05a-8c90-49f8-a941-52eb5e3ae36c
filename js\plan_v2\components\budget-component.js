/**
 * Budget Component - Phase 2 Implementation
 * 
 * This component will handle all budget-related UI updates and interactions.
 * It will replace the scattered budget rendering code with a clean, reusable component.
 */

/**
 * Budget Component Class
 * 
 * Responsibilities:
 * - Render budget overview
 * - Update budget progress bars
 * - Handle budget category interactions
 * - Show budget alerts and warnings
 * - Format currency values
 */
export class BudgetComponent {
  constructor(container, stateManager) {
    this.container = container;
    this.stateManager = stateManager;
    this.elements = {};
    this.unsubscribers = [];
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    console.log('💰 BudgetComponent: Initializing...');
    
    this.cacheElements();
    this.setupEventListeners();
    this.subscribeToState();
    
    console.log('💰 BudgetComponent: Initialized');
  }

  /**
   * Cache DOM elements
   */
  cacheElements() {
    this.elements = {
      budgetHealth: this.container.querySelector('.budget-health'),
      totalAmount: this.container.querySelector('.budget-total .amount'),
      spentAmount: this.container.querySelector('.budget-spent .amount'),
      remainingAmount: this.container.querySelector('.budget-remaining .amount'),
      progressFill: this.container.querySelector('.progress-fill'),
      progressLabels: this.container.querySelectorAll('.progress-labels span'),
      categoryItems: this.container.querySelectorAll('.category-item')
    };
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Category item interactions
    this.elements.categoryItems.forEach(item => {
      item.addEventListener('click', () => {
        const categoryName = item.querySelector('.category-name').textContent;
        this.handleCategoryClick(categoryName);
      });
    });

    // Budget health tooltip
    if (this.elements.budgetHealth) {
      this.elements.budgetHealth.addEventListener('mouseenter', this.showBudgetTooltip.bind(this));
      this.elements.budgetHealth.addEventListener('mouseleave', this.hideBudgetTooltip.bind(this));
    }
  }

  /**
   * Subscribe to state changes
   */
  subscribeToState() {
    // Subscribe to budget data changes
    const unsubscribe = this.stateManager.subscribe('data.budget', (budgetData) => {
      this.render(budgetData);
    });

    this.unsubscribers.push(unsubscribe);
  }

  /**
   * Load budget data directly from trip data (for direct usage without state manager)
   * @param {Object} tripData - Complete trip data from get_trip_plan_json
   */
  loadFromTripData(tripData) {
    if (!tripData || !tripData.trip || !tripData.trip.budget) {
      console.warn('💰 BudgetComponent: No budget data available in trip data');
      return;
    }

    const budgetData = this.transformBudgetData(tripData.trip.budget);
    this.render(budgetData);
  }

  /**
   * Transform database budget data to component format
   * @param {Object} dbBudgetData - Budget data from database
   * @returns {Object} Transformed budget data
   */
  transformBudgetData(dbBudgetData) {
    return {
      total: dbBudgetData.total_allocated || 0,
      spent: dbBudgetData.spent_to_date || 0,
      remaining: dbBudgetData.remaining || 0,
      currency: dbBudgetData.currency || 'USD',
      categories: (dbBudgetData.expenses_by_category || []).map(category => ({
        name: category.name,
        spent: category.spent || 0,
        total: category.allocated || 0,
        remaining: category.remaining || 0
      }))
    };
  }

  /**
   * Render budget data
   * @param {Object} budgetData - Budget data from state
   */
  render(budgetData) {
    if (!budgetData) return;
    
    console.log('💰 BudgetComponent: Rendering budget data');
    
    this.renderBudgetSummary(budgetData);
    this.renderBudgetProgress(budgetData);
    this.renderBudgetCategories(budgetData.categories);
    this.renderBudgetHealth(budgetData);
  }

  /**
   * Render budget summary
   * @param {Object} budgetData - Budget data
   */
  renderBudgetSummary(budgetData) {
    const currency = budgetData.currency || 'USD';

    if (this.elements.totalAmount) {
      this.elements.totalAmount.textContent = this.formatCurrency(budgetData.total, currency);
    }

    if (this.elements.spentAmount) {
      this.elements.spentAmount.textContent = this.formatCurrency(budgetData.spent, currency);
    }

    if (this.elements.remainingAmount) {
      this.elements.remainingAmount.textContent = this.formatCurrency(budgetData.remaining, currency);
    }
  }

  /**
   * Render budget progress bar
   * @param {Object} budgetData - Budget data
   */
  renderBudgetProgress(budgetData) {
    const percentage = (budgetData.spent / budgetData.total) * 100;
    
    if (this.elements.progressFill) {
      this.elements.progressFill.style.width = `${Math.min(percentage, 100)}%`;
    }
    
    if (this.elements.progressLabels.length >= 2) {
      this.elements.progressLabels[0].textContent = `${Math.round(percentage)}% Used`;
      this.elements.progressLabels[1].textContent = `${Math.round(100 - percentage)}% Remaining`;
    }
  }

  /**
   * Render budget categories
   * @param {Array} categories - Budget categories
   */
  renderBudgetCategories(categories) {
    if (!categories || !Array.isArray(categories)) {
      console.warn('💰 BudgetComponent: No categories data available');
      return;
    }

    this.elements.categoryItems.forEach((item, index) => {
      if (categories[index]) {
        const category = categories[index];
        const percentage = category.total > 0 ? (category.spent / category.total) * 100 : 0;

        // Update category name if needed
        const categoryNameEl = item.querySelector('.category-name');
        if (categoryNameEl && category.name) {
          categoryNameEl.textContent = category.name;
        }

        // Update amounts
        const spentEl = item.querySelector('.spent');
        const totalEl = item.querySelector('.total');

        if (spentEl) spentEl.textContent = this.formatCurrency(category.spent);
        if (totalEl) totalEl.textContent = `/ ${this.formatCurrency(category.total)}`;

        // Update progress bar
        const progressFill = item.querySelector('.progress-fill');
        if (progressFill) {
          progressFill.style.width = `${Math.min(percentage, 100)}%`;

          // Add warning class if over budget
          if (percentage > 100) {
            progressFill.classList.add('over-budget');
          } else {
            progressFill.classList.remove('over-budget');
          }
        }
      }
    });
  }

  /**
   * Render budget health indicator
   * @param {Object} budgetData - Budget data
   */
  renderBudgetHealth(budgetData) {
    if (!this.elements.budgetHealth) return;
    
    const percentage = (budgetData.spent / budgetData.total) * 100;
    
    // Remove existing health classes
    this.elements.budgetHealth.classList.remove('good', 'warning', 'danger');
    
    // Add appropriate health class
    if (percentage < 70) {
      this.elements.budgetHealth.classList.add('good');
      this.elements.budgetHealth.textContent = 'Healthy';
    } else if (percentage < 90) {
      this.elements.budgetHealth.classList.add('warning');
      this.elements.budgetHealth.textContent = 'Watch Spending';
    } else {
      this.elements.budgetHealth.classList.add('danger');
      this.elements.budgetHealth.textContent = 'Over Budget';
    }
  }

  /**
   * Handle category click
   * @param {string} categoryName - Category name
   */
  handleCategoryClick(categoryName) {
    console.log(`💰 BudgetComponent: Category clicked: ${categoryName}`);
    
    // Phase 2: Implement category detail view
    // this.showCategoryDetails(categoryName);
  }

  /**
   * Show budget tooltip
   */
  showBudgetTooltip() {
    // Phase 2: Implement budget breakdown tooltip
    console.log('💰 BudgetComponent: Showing budget tooltip');
  }

  /**
   * Hide budget tooltip
   */
  hideBudgetTooltip() {
    // Phase 2: Hide budget breakdown tooltip
    console.log('💰 BudgetComponent: Hiding budget tooltip');
  }

  /**
   * Format currency value
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code (default: USD)
   * @returns {string} Formatted currency string
   */
  formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Show budget alert
   * @param {string} type - Alert type (warning, error, success)
   * @param {string} message - Alert message
   */
  showBudgetAlert(type, message) {
    // Phase 2: Implement budget alerts
    console.log(`💰 BudgetComponent: ${type} alert: ${message}`);
  }

  /**
   * Update budget data
   * @param {Object} newBudgetData - New budget data
   */
  updateBudget(newBudgetData) {
    this.stateManager.setState('data.budget', newBudgetData);
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Unsubscribe from state changes
    this.unsubscribers.forEach(unsubscribe => unsubscribe());
    this.unsubscribers = [];
    
    console.log('💰 BudgetComponent: Destroyed');
  }
}
