/**
 * Clean Transport UI Rendering Module
 * 
 * This module handles all transport-related UI rendering with proper data structure handling.
 * It works with the unified TransportSystem to display transport data consistently.
 * 
 * Features:
 * - Renders daily transport segments
 * - Displays inter-city transport routes
 * - Shows transport costs and timing
 * - Handles error states gracefully
 * - Provides consistent UI components
 */

/**
 * Transport icon mapping for different modes
 * @param {string} mode - Transport mode
 * @returns {string} Font Awesome icon class
 */
export function getTransportIcon(mode) {
  const iconMap = {
    'car_rental': 'car',
    'car': 'car',
    'bus': 'bus',
    'train': 'train',
    'plane': 'plane',
    'walking': 'walking',
    'taxi': 'taxi',
    'bike': 'bicycle'
  };

  return iconMap[mode?.toLowerCase()] || 'car';
}

/**
 * Render transport data for a specific day
 * @param {Object} transportData - Transport data from TransportSystem
 * @param {number} dayNumber - Day number to render
 * @param {string} containerId - Container element ID (default: 'transport-tab')
 * @returns {void}
 */
export function renderTransportDay(transportData, dayNumber, containerId = 'transport-tab') {
  console.log(`🚗 Rendering transport for day ${dayNumber}`);

  const container = document.getElementById(containerId);
  if (!container) {
    console.error(`❌ Container element '${containerId}' not found`);
    return;
  }

  try {
    // Find day data
    const dayData = transportData.dailyTransport?.find(day => day.dayNumber === dayNumber);
    const interCityData = transportData.interCityTransport?.find(route => route.fromDay === dayNumber);

    // Clear container
    container.innerHTML = '';

    // Create header
    const header = createTransportHeader(dayNumber, dayData, interCityData);
    container.appendChild(header);

    // Render daily transport segments
    if (dayData && dayData.segments.length > 0) {
      const dailySection = createDailyTransportSection(dayData);
      container.appendChild(dailySection);
    }

    // Render inter-city transport if applicable
    if (interCityData) {
      const interCitySection = createInterCityTransportSection(interCityData);
      container.appendChild(interCitySection);
    }

    // Show no data message if no transport
    if ((!dayData || dayData.segments.length === 0) && !interCityData) {
      const noDataMessage = createNoDataMessage();
      container.appendChild(noDataMessage);
    }

  } catch (error) {
    console.error('❌ Error rendering transport day:', error);
    container.innerHTML = createErrorMessage('Error loading transport data', error.message);
  }
}

/**
 * Create transport header with summary information
 * @param {number} dayNumber - Day number
 * @param {Object} dayData - Daily transport data
 * @param {Object} interCityData - Inter-city transport data
 * @returns {HTMLElement} Header element
 */
function createTransportHeader(dayNumber, dayData, interCityData) {
  const header = document.createElement('div');
  header.className = 'transport-header';

  const totalSegments = (dayData?.segments.length || 0) + (interCityData ? 1 : 0);
  const totalCost = (dayData?.totalCostUSD || 0) + (interCityData?.costUSD || 0);

  header.innerHTML = `
    <div class="transport-title">
      <h3>
        <i class="fas fa-route"></i>
        Day ${dayNumber} Transport
      </h3>
      <p>Daily movement and travel arrangements</p>
    </div>
    <div class="transport-summary">
      <div class="summary-item">
        <span class="summary-label">Segments</span>
        <span class="summary-value">${totalSegments}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">Total Cost</span>
        <span class="summary-value">$${totalCost.toFixed(2)}</span>
      </div>
    </div>
  `;

  return header;
}

/**
 * Create daily transport section
 * @param {Object} dayData - Daily transport data
 * @returns {HTMLElement} Daily transport section
 */
function createDailyTransportSection(dayData) {
  const section = document.createElement('div');
  section.className = 'daily-transport-section';

  section.innerHTML = `
    <div class="section-header">
      <h4>
        <i class="fas fa-map-marked-alt"></i>
        Daily Movement
      </h4>
      <p>${dayData.segments.length} segments • $${dayData.totalCostUSD.toFixed(2)} total</p>
    </div>
  `;

  const segmentsContainer = document.createElement('div');
  segmentsContainer.className = 'transport-segments';

  for (const segment of dayData.segments) {
    const segmentCard = createTransportSegmentCard(segment);
    segmentsContainer.appendChild(segmentCard);
  }

  section.appendChild(segmentsContainer);
  return section;
}

/**
 * Create transport segment card with alternative options
 * @param {Object} segment - Transport segment data
 * @returns {HTMLElement} Segment card element
 */
function createTransportSegmentCard(segment) {
  const card = document.createElement('div');
  card.className = 'transport-card';

  const durationDisplay = segment.duration < 60
    ? `${Math.round(segment.duration)} min`
    : `${Math.floor(segment.duration / 60)}h ${Math.round(segment.duration % 60)}min`;

  // Create route header
  const routeHeader = `
    <div class="transport-route">
      <div class="route-sequence">
        <span class="sequence-number">${segment.sequence}</span>
      </div>
      <div class="route-details">
        <h5>${segment.description}</h5>
        <div class="route-path">
          <span class="from">${segment.from}</span>
          <i class="fas fa-arrow-right"></i>
          <span class="to">${segment.to}</span>
        </div>
      </div>
      <div class="route-stats">
        <span class="distance">${segment.distance.toFixed(1)} km</span>
        <span class="duration">${durationDisplay}</span>
      </div>
    </div>
  `;

  // Create primary option
  const primaryOption = segment.primaryOption || {
    mode: segment.mode,
    costUSD: segment.costUSD,
    costMAD: segment.costMAD,
    reasoning: segment.reasoning
  };

  const primaryOptionHtml = createTransportOptionHtml(primaryOption, true);

  // Create alternative options
  let alternativeOptionsHtml = '';
  if (segment.alternativeOptions && segment.alternativeOptions.length > 0) {
    alternativeOptionsHtml = segment.alternativeOptions
      .map(option => createTransportOptionHtml(option, false))
      .join('');
  }

  card.innerHTML = routeHeader + primaryOptionHtml + alternativeOptionsHtml;
  return card;
}

/**
 * Create transport option HTML
 * @param {Object} option - Transport option data
 * @param {boolean} isPrimary - Whether this is the primary option
 * @returns {string} Option HTML
 */
function createTransportOptionHtml(option, isPrimary = false) {
  const icon = getTransportIcon(option.mode);
  const optionClass = isPrimary ? 'transport-option primary' : 'transport-option alternative';
  const badge = isPrimary ? '<span class="option-badge">Recommended</span>' : '<span class="option-badge alternative">Alternative</span>';

  return `
    <div class="${optionClass}">
      <div class="option-header">
        <div class="transport-mode">
          <i class="fas fa-${icon}"></i>
          <span class="mode-name">${formatModeName(option.mode)}</span>
          ${badge}
        </div>
        <div class="transport-cost">
          <span class="price">$${option.costUSD.toFixed(2)}</span>
          <span class="price-mad">${option.costMAD.toFixed(0)} MAD</span>
        </div>
      </div>
      <div class="option-reasoning">
        <p>${option.reasoning}</p>
      </div>
    </div>
  `;
}

/**
 * Create inter-city transport section with train stations and alternatives
 * @param {Object} interCityData - Inter-city transport data
 * @returns {HTMLElement} Inter-city transport section
 */
function createInterCityTransportSection(interCityData) {
  const section = document.createElement('div');
  section.className = 'inter-city-transport-section';

  const durationDisplay = interCityData.duration < 60
    ? `${Math.round(interCityData.duration)} min`
    : `${Math.floor(interCityData.duration / 60)}h ${Math.round(interCityData.duration % 60)}min`;

  // Create route header
  const routeHeader = `
    <div class="section-header">
      <h4>
        <i class="fas fa-map-signs"></i>
        Inter-City Travel
      </h4>
      <p>${interCityData.description}</p>
    </div>

    <div class="inter-city-card">
      <div class="inter-city-route">
        <div class="city-info">
          <h5>${interCityData.fromCity} → ${interCityData.toCity}</h5>
          <div class="route-stats">
            <span class="distance">${interCityData.distance.toFixed(0)} km</span>
            <span class="duration">${durationDisplay}</span>
          </div>
        </div>
      </div>
  `;

  // Create primary option
  const primaryOption = interCityData.primaryOption || {
    mode: interCityData.mode,
    costUSD: interCityData.costUSD,
    costMAD: interCityData.costMAD,
    reasoning: interCityData.reasoning
  };

  const primaryOptionHtml = createTransportOptionHtml(primaryOption, true);

  // Create alternative options
  let alternativeOptionsHtml = '';
  if (interCityData.alternativeOptions && interCityData.alternativeOptions.length > 0) {
    alternativeOptionsHtml = interCityData.alternativeOptions
      .map(option => createTransportOptionHtml(option, false))
      .join('');
  }

  // Create train stations section if available
  let trainStationsHtml = '';
  if (interCityData.trainStations) {
    trainStationsHtml = createTrainStationsSection(interCityData.trainStations, interCityData.fromCity, interCityData.toCity);
  }

  section.innerHTML = routeHeader + primaryOptionHtml + alternativeOptionsHtml + trainStationsHtml + '</div>';
  return section;
}

/**
 * Create train stations section
 * @param {Object} trainStations - Train stations data with from and to arrays
 * @param {string} fromCity - Source city name
 * @param {string} toCity - Destination city name
 * @returns {string} Train stations HTML
 */
function createTrainStationsSection(trainStations, fromCity, toCity) {
  if (!trainStations.from || !trainStations.to ||
      trainStations.from.length === 0 || trainStations.to.length === 0) {
    return '';
  }

  const fromStationsHtml = trainStations.from.map(station => `
    <div class="train-station">
      <div class="station-name">${station.name}</div>
      <div class="station-address">${station.address}</div>
    </div>
  `).join('');

  const toStationsHtml = trainStations.to.map(station => `
    <div class="train-station">
      <div class="station-name">${station.name}</div>
      <div class="station-address">${station.address}</div>
    </div>
  `).join('');

  return `
    <div class="train-stations-section">
      <h6><i class="fas fa-train"></i> Available Train Stations</h6>
      <div class="stations-container">
        <div class="stations-group">
          <h7>Departure from ${fromCity}</h7>
          <div class="stations-list">
            ${fromStationsHtml}
          </div>
        </div>
        <div class="stations-group">
          <h7>Arrival in ${toCity}</h7>
          <div class="stations-list">
            ${toStationsHtml}
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Format transport mode name for display
 * @param {string} mode - Transport mode
 * @returns {string} Formatted mode name
 */
function formatModeName(mode) {
  const modeNames = {
    'car_rental': 'Car Rental',
    'car': 'Car',
    'bus': 'Bus',
    'train': 'Train',
    'plane': 'Flight',
    'walking': 'Walking',
    'taxi': 'Taxi',
    'bike': 'Bicycle'
  };

  return modeNames[mode?.toLowerCase()] || 'Transport';
}

/**
 * Create no data message
 * @returns {string} No data message HTML
 */
function createNoDataMessage() {
  const message = document.createElement('div');
  message.className = 'no-data-message';
  message.innerHTML = `
    <div class="no-data-content">
      <i class="fas fa-car"></i>
      <h3>No transport data</h3>
      <p>No transport information available for this day.</p>
    </div>
  `;
  return message;
}

/**
 * Create error message
 * @param {string} title - Error title
 * @param {string} message - Error message
 * @returns {string} Error message HTML
 */
function createErrorMessage(title, message) {
  return `
    <div class="error-message">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>${title}</h3>
        <p>${message}</p>
      </div>
    </div>
  `;
}

/**
 * Render transport calculation status
 * @param {string} status - Status ('calculating', 'success', 'error')
 * @param {string} message - Status message
 * @param {string} containerId - Container element ID
 * @returns {void}
 */
export function renderTransportStatus(status, message, containerId = 'transport-tab') {
  const container = document.getElementById(containerId);
  if (!container) return;

  const statusClass = status === 'calculating' ? 'loading-message' : 
                     status === 'success' ? 'success-message' : 'error-message';
  
  const icon = status === 'calculating' ? 'fa-spinner fa-spin' :
               status === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

  container.innerHTML = `
    <div class="${statusClass}">
      <div class="status-content">
        <i class="fas ${icon}"></i>
        <h3>${status === 'calculating' ? 'Calculating Transport...' : 
              status === 'success' ? 'Transport Calculated' : 'Calculation Error'}</h3>
        <p>${message}</p>
      </div>
    </div>
  `;
}
