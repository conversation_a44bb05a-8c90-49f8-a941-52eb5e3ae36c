/* ===== BUDGET COMPONENT STYLES ===== */

.budget-overview {
  grid-column: 1;
}

.budget-status {
  display: flex;
  align-items: center;
}

.budget-health {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.budget-health.good {
  background: var(--success-light);
  color: var(--success-color);
}

.budget-health.warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.budget-health.danger {
  background: var(--error-light);
  color: var(--error-color);
}

.budget-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-4);
}

.budget-total,
.budget-spent,
.budget-remaining {
  text-align: center;
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.budget-total:hover,
.budget-spent:hover,
.budget-remaining:hover {
  background: var(--white);
  box-shadow: var(--shadow-sm);
}

.budget-total .amount {
  color: var(--primary-color);
}

.budget-spent .amount {
  color: var(--warning-color);
}

.budget-remaining .amount {
  color: var(--success-color);
}

.amount {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: var(--spacing-1);
}

.label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

.budget-progress {
  padding: 0 var(--spacing-6) var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.budget-categories {
  padding: 0 var(--spacing-6) var(--spacing-6);
}

.category-item {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-3);
  align-items: center;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-100);
}

.category-item:last-child {
  border-bottom: none;
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.category-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
}

.category-name {
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
}

.category-amounts {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
}

.category-amounts .spent {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

.category-amounts .total {
  color: var(--gray-500);
}

.category-progress {
  grid-column: 1 / -1;
  margin-top: var(--spacing-2);
}

/* Category-specific icon colors */
.category-item:nth-child(1) .category-icon {
  color: var(--primary-color);
}

.category-item:nth-child(2) .category-icon {
  color: var(--accent-color);
}

.category-item:nth-child(3) .category-icon {
  color: var(--success-color);
}

.category-item:nth-child(4) .category-icon {
  color: var(--info-color);
}

/* Budget status indicators */
.budget-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
}

.budget-indicator.over-budget {
  background: var(--error-light);
  color: var(--error-color);
}

.budget-indicator.near-budget {
  background: var(--warning-light);
  color: var(--warning-color);
}

.budget-indicator.under-budget {
  background: var(--success-light);
  color: var(--success-color);
}

/* Animated progress bars for budget categories */
.category-progress .progress-fill {
  animation: fillProgress 1s ease-out;
}

@keyframes fillProgress {
  from {
    width: 0;
  }
}

/* Budget breakdown tooltip */
.budget-tooltip {
  position: relative;
  cursor: help;
}

.budget-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-900);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-fast);
  z-index: var(--z-tooltip);
}

.budget-tooltip:hover::after {
  opacity: 1;
}

/* Budget alerts */
.budget-alert {
  background: var(--warning-light);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin: var(--spacing-4) var(--spacing-6) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.budget-alert.error {
  background: var(--error-light);
  border-color: var(--error-color);
}

.budget-alert.success {
  background: var(--success-light);
  border-color: var(--success-color);
}

.budget-alert-icon {
  color: var(--warning-color);
  font-size: var(--font-size-lg);
}

.budget-alert.error .budget-alert-icon {
  color: var(--error-color);
}

.budget-alert.success .budget-alert-icon {
  color: var(--success-color);
}

.budget-alert-text {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  margin: 0;
}

/* Responsive budget layout */
@media (max-width: 767px) {
  .budget-summary {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
  }

  .budget-total,
  .budget-spent,
  .budget-remaining {
    padding: var(--spacing-3);
  }

  .amount {
    font-size: var(--font-size-xl);
  }

  .category-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    padding: var(--spacing-4) 0;
  }

  .category-amounts {
    justify-self: start;
    font-size: var(--font-size-base);
  }

  .category-progress {
    margin-top: var(--spacing-2);
  }

  .budget-categories {
    padding: 0 var(--spacing-4) var(--spacing-4);
  }
}

/* Budget comparison charts */
.budget-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-top: var(--spacing-4);
}

.comparison-item {
  text-align: center;
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
}

.comparison-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  display: block;
  margin-bottom: var(--spacing-1);
}

.comparison-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.comparison-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-top: var(--spacing-1);
}

.comparison-change.positive {
  color: var(--success-color);
}

.comparison-change.negative {
  color: var(--error-color);
}

.comparison-change.neutral {
  color: var(--gray-500);
}
