/**
 * Authentication module for Tsafira
 * Handles user authentication state and UI updates using Supabase Auth
 */

import { supabase } from '../supabaseClient.js';

/**
 * Check if user is authenticated
 * @returns {boolean} Authentication status
 */
export async function isAuthenticated() {
  const { data: { session } } = await supabase.auth.getSession();
  return !!session;
}

/**
 * Sign in user with email and password
 * @param {string} email User email
 * @param {string} password User password
 * @returns {Object} Authentication result
 */
export async function signInWithEmail(email, password) {
  try {
    console.log('Attempting to sign in with email:', email);

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Supabase auth error:', error);
      throw error;
    }

    console.log('Authentication successful:', data.user.id);

    // Try to get user profile data, but don't block signin if it fails
    let profile = null;
    try {
      profile = await getUserProfile(data.user.id);
      console.log('Profile loaded:', profile);
    } catch (profileError) {
      console.warn('Failed to load profile, but signin was successful:', profileError);
    }

    // Dispatch event to notify other components about auth state change
    document.dispatchEvent(new CustomEvent('authStateChanged', {
      detail: { authenticated: true, user: data.user, profile }
    }));

    // UI updates are now handled by headerAuth.js

    console.log('Signin completed successfully, returning result');
    return { success: true, user: data.user, profile };
  } catch (error) {
    console.error('Sign in error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Sign up user with email and password
 * @param {string} email User email
 * @param {string} password User password
 * @param {string} name User full name
 * @returns {Object} Authentication result
 */
export async function signUpWithEmail(email, password, name) {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name
        },
        emailRedirectTo: `${window.location.origin}/pages/verify-email.html`
      }
    });

    if (error) {
      throw error;
    }

    return { success: true, user: data.user, needsConfirmation: !data.session };
  } catch (error) {
    console.error('Sign up error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Sign out user
 */
export async function signOut() {
  try {
    console.log('Signing out user...');

    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    console.log('Sign out successful');

    // Dispatch event to notify other components about auth state change
    document.dispatchEvent(new CustomEvent('authStateChanged', {
      detail: { authenticated: false }
    }));

    // UI updates are now handled by headerAuth.js

    return { success: true };
  } catch (error) {
    console.error('Sign out error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get current user data
 * @returns {Object|null} User data or null if not authenticated
 */
export async function getCurrentUser() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const profile = await getUserProfile(user.id);
      return { ...user, profile };
    }
    return null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Get user profile from database
 * @param {string} userId User ID
 * @returns {Object|null} User profile or null
 */
export async function getUserProfile(userId) {
  try {
    console.log('Fetching profile for user:', userId);

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Profile fetch timeout')), 10000);
    });

    const fetchPromise = supabase
      .from('profile')
      .select('*')
      .eq('id', userId)
      .single();

    const { data, error } = await Promise.race([fetchPromise, timeoutPromise]);

    if (error) {
      console.log('Profile fetch error:', error);
      // If profile doesn't exist, create one
      if (error.code === 'PGRST116' || error.message?.includes('No rows returned')) {
        console.log('Profile not found, creating new profile for user:', userId);
        return await createUserProfile(userId);
      }
      console.error('Error fetching user profile:', error);
      return null;
    }

    console.log('Profile fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

/**
 * Create user profile in database
 * @param {string} userId User ID
 * @returns {Object|null} Created profile or null
 */
export async function createUserProfile(userId) {
  try {
    console.log('Creating profile for user:', userId);

    // Get user data from auth
    const { data: { user } } = await supabase.auth.getUser();
    if (!user || user.id !== userId) {
      throw new Error('User not authenticated or ID mismatch');
    }

    // Create profile with basic information
    const profileData = {
      id: userId,
      name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
      phone: user.user_metadata?.phone || null,
      role: 'traveler',
      status: 'active'
    };

    console.log('Inserting profile data:', profileData);

    const { data, error } = await supabase
      .from('profile')
      .insert(profileData)
      .select()
      .single();

    if (error) {
      console.error('Profile creation error:', error);
      throw error;
    }

    console.log('Profile created successfully:', data);

    // Try to create user system preferences, but don't fail if it doesn't work
    try {
      await createUserSystemPreferences(userId);
    } catch (prefError) {
      console.warn('Failed to create user preferences, but profile was created:', prefError);
    }

    return data;
  } catch (error) {
    console.error('Error creating user profile:', error);
    return null;
  }
}

/**
 * Create user system preferences
 * @param {string} userId User ID
 * @returns {Object|null} Created preferences or null
 */
export async function createUserSystemPreferences(userId) {
  try {
    const preferencesData = {
      user_id: userId,
      default_currency: 'USD',
      default_language: 'English',
      timezone: 'UTC',
      date_format: 'YYYY-MM-DD',
      email_notifications: true,
      sms_notifications: false,
      push_notifications: true,
      trip_updates: true,
      marketing: false
    };

    const { data, error } = await supabase
      .from('user_system_preference')
      .insert(preferencesData)
      .select()
      .single();

    if (error) {
      console.error('Error creating user preferences:', error);
      return null;
    }

    console.log('Created user system preferences:', data);
    return data;
  } catch (error) {
    console.error('Error creating user system preferences:', error);
    return null;
  }
}

/**
 * Update user profile
 * @param {Object} profileData Profile data to update
 * @returns {Object} Update result
 */
export async function updateUserProfile(profileData) {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('profile')
      .update(profileData)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { success: true, profile: data };
  } catch (error) {
    console.error('Error updating profile:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Sign in with social provider
 * @param {string} provider Social provider (google, facebook, github)
 * @returns {Object} Authentication result
 */
export async function signInWithSocial(provider) {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider,
      options: {
        redirectTo: `${window.location.origin}/index.html`
      }
    });

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error(`${provider} sign in error:`, error);
    return { success: false, error: error.message };
  }
}

/**
 * Reset password
 * @param {string} email User email
 * @returns {Object} Reset result
 */
export async function resetPassword(email) {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/pages/signin.html?reset=true`
    });

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Password reset error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Resend verification email
 * @param {string} email User email
 * @returns {Object} Resend result
 */
export async function resendVerificationEmail(email) {
  try {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email,
      options: {
        emailRedirectTo: `${window.location.origin}/pages/verify-email.html`
      }
    });

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Resend verification error:', error);
    return { success: false, error: error.message };
  }
}

// UI updates are now handled by headerAuth.js to prevent conflicts and FOUC

/**
 * Initialize authentication with Supabase
 * Sets up auth state listeners and updates UI
 */
export async function initAuth() {
  try {
    console.log('Initializing auth...');

    // Listen for auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);

      if (event === 'SIGNED_IN' && session) {
        // Don't load profile during auth state change to avoid blocking
        document.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { authenticated: true, user: session.user, profile: null }
        }));
      } else if (event === 'SIGNED_OUT') {
        document.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { authenticated: false }
        }));
      }

      // UI updates are now handled by headerAuth.js
    });

    // Listen for sign out events from UI
    document.addEventListener('signOut', async () => {
      console.log('Sign out event received');
      await signOut();
    });

    console.log('Auth initialization complete');
  } catch (error) {
    console.error('Error initializing auth:', error);
  }
}

// Export default object with all functions
export default {
  isAuthenticated,
  getCurrentUser,
  signInWithEmail,
  signUpWithEmail,
  signInWithSocial,
  resetPassword,
  resendVerificationEmail,
  signOut,
  getUserProfile,
  createUserProfile,
  createUserSystemPreferences,
  updateUserProfile,
  initAuth
};