<!-- Header with improved mobile design -->
<header id="header" class="fixed w-full bg-[var(--color-bg)] border-b border-[var(--color-border)] z-50">
  <nav class="container mx-auto px-4 py-3 flex items-center justify-between">
    <!-- Logo -->
    <a href="../index.html" class="text-2xl font-bold text-[var(--color-primary)]">Tsa<PERSON>ra</a>

    <!-- Desktop Navigation -->
    <div class="hidden md:flex items-center space-x-6">
      <a href="../index.html" class="text-[var(--color-text)] hover:text-[var(--color-primary)] transition-colors duration-200">Home</a>
      <a href="../pages/destinations.html" class="text-[var(--color-text)] hover:text-[var(--color-primary)] transition-colors duration-200">Destinations</a>
      <a href="../pages/city-pass.html" class="relative group">
        <span class="text-[var(--color-text)] group-hover:text-[var(--color-primary)] transition-colors duration-200">City Pass</span>
        <span class="absolute -top-1 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-1 py-1 rounded-full font-medium group-hover:scale-110 transition-transform duration-200"></span>
        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-[var(--color-primary)] group-hover:w-full transition-all duration-300"></span>
      </a>
      <a href="../pages/how-it-works.html" class="text-[var(--color-text)] hover:text-[var(--color-primary)] transition-colors duration-200">How It Works</a>
      <a href="../pages/contact.html" class="text-[var(--color-text)] hover:text-[var(--color-primary)] transition-colors duration-200">Contact</a>
    </div>

    <!-- Desktop Auth + Theme + CTA -->
    <div class="hidden md:flex items-center space-x-4">
      <!-- Theme Toggle -->
      <button
        id="theme-toggle"
        class="p-2 rounded-full hover:bg-[var(--color-bg-alt)] text-[var(--color-text)] transition-colors duration-200"
        data-theme-toggle
        onclick="toggleTheme(); return false;"
      >
        <i class="fas fa-moon moon-icon"></i>
        <i class="fas fa-sun sun-icon hidden"></i>
      </button>

      <!-- Auth Actions -->
      <div id="nav-actions" class="flex items-center space-x-4">
        <!-- Auth content will be populated by JavaScript - hidden initially to prevent FOUC -->
        <div id="auth-placeholder" style="opacity: 0; transition: opacity 0.2s ease-in-out;">
          <a href="../pages/signin.html" id="signin-btn" class="px-4 py-2 rounded-full border-2 border-transparent text-[var(--color-text)] hover:border-[var(--color-primary)] transition-all duration-200">
            Sign In
          </a>
        </div>
        <div class="flex space-x-3">
          <a href="../pages/city-pass.html" class="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-5 py-2 rounded-l-full hover:shadow-lg hover:shadow-orange-200 dark:hover:shadow-orange-900/30 transition-all duration-300 group">
            <span class="flex items-center">
              <i class="fas fa-ticket-alt mr-2 group-hover:rotate-12 transition-transform duration-300"></i>
              <span>City Pass</span>
            </span>
          </a>
          <a href="../pages/wizard.html" class="bg-[var(--color-primary)] text-[var(--color-text-inverse)] px-5 py-2 rounded-r-full hover:bg-[var(--color-primary-hover)] hover:shadow-lg hover:shadow-primary-200 dark:hover:shadow-primary-900/30 transition-all duration-300">
            Plan Trip
          </a>
        </div>
      </div>
    </div>

    <!-- Mobile CTA + Menu Button -->
    <div class="flex items-center space-x-2 md:hidden">
      <!-- Theme Toggle -->
      <button
        id="theme-toggle-mobile"
        class="p-2 rounded-full hover:bg-[var(--color-bg-alt)] text-[var(--color-text)] transition-colors duration-200"
        data-theme-toggle
        onclick="toggleTheme(); return false;"
      >
        <i class="fas fa-moon moon-icon"></i>
        <i class="fas fa-sun sun-icon hidden"></i>
      </button>

      <div id="mobile-auth-placeholder" style="opacity: 0; transition: opacity 0.2s ease-in-out;">
        <a href="../pages/signin.html" id="mobile-signin-btn" class="text-[var(--color-text)] hover:text-[var(--color-primary)] transition-colors duration-200">
          <i class="fas fa-user"></i>
        </a>
      </div>

      <div class="flex">
        <a href="../pages/city-pass.html" class="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-2 py-1.5 rounded-l-full text-sm transition-all duration-200 flex items-center">
          <i class="fas fa-ticket-alt"></i>
        </a>
        <a href="../pages/wizard.html" class="bg-[var(--color-primary)] text-[var(--color-text-inverse)] px-3 py-1.5 rounded-r-full text-sm hover:bg-[var(--color-primary-hover)] transition-colors duration-200">
          Plan Trip
        </a>
      </div>

      <button
        id="mobile-menu-button"
        class="p-2 rounded-md text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] transition-colors duration-200"
        onclick="openMobileMenu(); return false;"
      >
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>
</header>

<!-- Improved Mobile Menu -->
<div id="mobile-menu" class="fixed inset-0 bg-[var(--color-bg)] z-50 md:hidden" style="display: none;">
  <div class="flex flex-col h-full">
    <!-- Header with close button -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-[var(--color-border)]">
      <a href="../index.html" class="text-2xl font-bold text-[var(--color-primary)]">Tsafira</a>
      <button
        id="close-menu-button"
        class="p-2 rounded-md text-[var(--color-text)] hover:bg-[var(--color-bg-alt)]"
        onclick="closeMobileMenu(); return false;"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Navigation Links -->
    <div class="flex-grow overflow-y-auto">
      <div class="py-2">
        <a href="../index.html" class="block px-6 py-3 text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] hover:text-[var(--color-primary)] border-l-4 border-transparent hover:border-[var(--color-primary)] transition-all duration-200">Home</a>
        <a href="../pages/destinations.html" class="block px-6 py-3 text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] hover:text-[var(--color-primary)] border-l-4 border-transparent hover:border-[var(--color-primary)] transition-all duration-200">Destinations</a>
        <a href="../pages/city-pass.html" class="relative block px-6 py-3 text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] hover:text-[var(--color-primary)] border-l-4 border-transparent hover:border-[var(--color-primary)] transition-all duration-200">
          <div class="flex items-center">
            <span>City Pass</span>
            <span class="ml-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-1 py-1 rounded-full font-medium"></span>
          </div>
        </a>
        <a href="../pages/how-it-works.html" class="block px-6 py-3 text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] hover:text-[var(--color-primary)] border-l-4 border-transparent hover:border-[var(--color-primary)] transition-all duration-200">How It Works</a>
        <a href="../pages/contact.html" class="block px-6 py-3 text-[var(--color-text)] hover:bg-[var(--color-bg-alt)] hover:text-[var(--color-primary)] border-l-4 border-transparent hover:border-[var(--color-primary)] transition-all duration-200">Contact</a>
      </div>
    </div>

    <!-- Auth Actions -->
    <div class="px-6 py-4 border-t border-[var(--color-border)]">
      <div id="mobile-nav-actions" class="space-y-3">
        <!-- Auth content will be populated by JavaScript - hidden initially to prevent FOUC -->
        <div id="mobile-menu-auth-placeholder" style="opacity: 0; transition: opacity 0.2s ease-in-out;">
          <a href="../pages/signin.html" id="mobile-signin-link" class="block w-full text-center px-4 py-2 border-2 border-[var(--color-border)] rounded-full text-[var(--color-text)] hover:border-[var(--color-primary)] transition-all duration-200">
            Sign In
          </a>
        </div>
        <a href="../pages/city-pass.html" class="block w-full text-center bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md">
          <i class="fas fa-ticket-alt mr-2"></i> Explore City Pass
        </a>
        <a href="../pages/wizard.html" class="block w-full text-center bg-[var(--color-primary)] text-[var(--color-text-inverse)] px-4 py-2 rounded-full hover:bg-[var(--color-primary-hover)] transition-colors duration-200">
          Plan Your Trip
        </a>
      </div>
    </div>
  </div>
</div>

<style>
  /* Ensure mobile menu is hidden on page load */
  #mobile-menu {
    display: none !important;
  }

  /* Transition for mobile menu */
  #mobile-menu.visible {
    display: block !important;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  /* City Pass button effects */
  .from-amber-500 {
    --tw-gradient-from: #f59e0b;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(245, 158, 11, 0));
  }

  .to-orange-500 {
    --tw-gradient-to: #f97316;
  }

  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
  }

  /* Shadow effects for hover states */
  .hover\:shadow-lg:hover {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .hover\:shadow-orange-200:hover {
    --tw-shadow-color: rgba(254, 215, 170, 0.5);
    --tw-shadow: var(--tw-shadow-colored);
  }

  .dark .dark\:hover\:shadow-orange-900\/30:hover {
    --tw-shadow-color: rgba(124, 45, 18, 0.3);
    --tw-shadow: var(--tw-shadow-colored);
  }

  /* Transition effects */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .duration-200 {
    transition-duration: 200ms;
  }

  .duration-300 {
    transition-duration: 300ms;
  }

  /* Transform effects */
  .group:hover .group-hover\:rotate-12 {
    --tw-rotate: 12deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .group:hover .group-hover\:scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
</style>

<!-- Immediate Auth Check to Prevent FOUC -->
<script type="module">
  // Import Supabase client for immediate auth check
  import { supabase } from '../js/supabaseClient.js';

  // Immediate authentication check to prevent flash of content
  (async function() {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const isAuthenticated = !!session;

      // Store initial auth state for header auth manager
      window.initialAuthState = { isAuthenticated, session };

      // Show auth placeholders with correct initial state
      const authPlaceholder = document.getElementById('auth-placeholder');
      const mobileAuthPlaceholder = document.getElementById('mobile-auth-placeholder');
      const mobileMenuAuthPlaceholder = document.getElementById('mobile-menu-auth-placeholder');

      if (authPlaceholder) authPlaceholder.style.opacity = '1';
      if (mobileAuthPlaceholder) mobileAuthPlaceholder.style.opacity = '1';
      if (mobileMenuAuthPlaceholder) mobileMenuAuthPlaceholder.style.opacity = '1';

      console.log('Initial auth state set:', isAuthenticated);
    } catch (error) {
      console.warn('Initial auth check failed:', error);
      // Show placeholders anyway to prevent permanent hiding
      const authPlaceholder = document.getElementById('auth-placeholder');
      const mobileAuthPlaceholder = document.getElementById('mobile-auth-placeholder');
      const mobileMenuAuthPlaceholder = document.getElementById('mobile-menu-auth-placeholder');

      if (authPlaceholder) authPlaceholder.style.opacity = '1';
      if (mobileAuthPlaceholder) mobileAuthPlaceholder.style.opacity = '1';
      if (mobileMenuAuthPlaceholder) mobileMenuAuthPlaceholder.style.opacity = '1';
    }
  })();
</script>

<!-- Import UI module (will be loaded by the browser) -->
<script type="module">
  // Dynamically determine the correct path to ui.js
  const isInPagesDir = window.location.pathname.includes('/pages/');
  const uiPath = isInPagesDir ? '../js/ui.js' : './js/ui.js';

  // Import the UI functions
  import { initTheme, updateThemeIcons, closeMobileMenu, openMobileMenu } from '../js/ui.js';

  // Run a quick theme check to make sure UI is correct
  // even if the main UI initialization happens later
  document.addEventListener('DOMContentLoaded', function() {
    const storedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;

    // Set theme class
    if (storedTheme === 'dark') {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }

    // Update toggle button icons
    updateThemeIcons();

    // Ensure mobile menu functions are attached to window
    window.closeMobileMenu = closeMobileMenu;
    window.openMobileMenu = openMobileMenu;

    // Set up mobile menu button handlers
    const closeButton = document.getElementById('close-menu-button');
    if (closeButton) {
      closeButton.onclick = function(e) {
        e.preventDefault();
        closeMobileMenu();
        return false;
      };
    }

    const menuButton = document.getElementById('mobile-menu-button');
    if (menuButton) {
      menuButton.onclick = function(e) {
        e.preventDefault();
        openMobileMenu();
        return false;
      };
    }
  });
</script>