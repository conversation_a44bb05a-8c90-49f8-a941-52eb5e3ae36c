/* Header */
.header {
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 20;
  transition: background-color var(--transition-normal), border-color var(--transition-normal), box-shadow var(--transition-normal);
}

.header.scrolled {
  box-shadow: var(--shadow-md);
}

.dark .header {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-200);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--color-primary);
  transition: transform var(--transition-fast);
}

.logo:hover {
  transform: scale(1.05);
}

.main-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.main-nav a {
  padding: var(--spacing-2);
  color: var(--color-gray-600);
  transition: color var(--transition-fast);
  position: relative;
}

.dark .main-nav a {
  color: var(--color-gray-500);
}

.main-nav a:hover {
  color: var(--color-primary);
}

.main-nav a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal), left var(--transition-normal);
}

.main-nav a:hover::after {
  width: 100%;
  left: 0;
}

#dark-mode-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  transition: all var(--transition-normal);
  margin-left: var(--spacing-2);
}

.dark #dark-mode-toggle {
  background-color: var(--color-gray-200);
  color: var(--color-primary-light);
}

#dark-mode-toggle:hover {
  background-color: var(--color-gray-200);
  transform: rotate(15deg);
}

.dark #dark-mode-toggle:hover {
  background-color: var(--color-gray-300);
}

/* Itinerary Title Section */
.itinerary-title {
  padding: var(--spacing-10) 0;
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.dark .itinerary-title {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-300);
}

.itinerary-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.itinerary-name {
  font-size: 2.25rem;
  font-weight: 700;
  background-color: transparent;
  border: 1px dashed transparent;
  padding: var(--spacing-2);
  border-radius: var(--border-radius);
  width: 100%;
  color: inherit;
  transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-fast);
}

.itinerary-name:hover {
  border-color: var(--color-gray-300);
}

.dark .itinerary-name:hover {
  border-color: var(--color-gray-600);
}

.itinerary-name:focus {
  outline: none;
  border: 1px solid var(--color-primary);
  background-color: var(--color-gray-100);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.dark .itinerary-name:focus {
  background-color: var(--color-gray-200);
  border-color: var(--color-primary-dark);
  box-shadow: 0 0 0 2px var(--color-primary-dark);
}

.itinerary-dates {
  color: var(--color-gray-600);
  margin-top: var(--spacing-2);
  font-size: 1.125rem;
}

.dark .itinerary-dates {
  color: var(--color-gray-500);
}

.itinerary-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

/* Overview Section */
.overview-section {
  padding: var(--spacing-12) 0;
  transition: background-color var(--transition-normal);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.dark .overview-section {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
}

.overview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23f1f5f9" fill-opacity="0.4"><circle cx="30" cy="30" r="1.5"/></g></svg>') repeat;
  opacity: 0.3;
  pointer-events: none;
}

.overview-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-8);
  position: relative;
  z-index: 1;
}

.budget-summary, .weather-summary {
  margin-bottom: var(--spacing-6);
  height: 100%;
}

.budget-summary {
  flex: 1 1 450px;
  max-height: 550px;
  overflow-y: auto;
}

.weather-summary {
  flex: 1 1 350px;
}

.budget-summary .card, .weather-summary .card {
  border-radius: var(--border-radius-xl);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  padding: var(--spacing-6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.budget-summary .card::before, .weather-summary .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.budget-summary .card:hover, .weather-summary .card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .budget-summary .card, .dark .weather-summary .card {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.budget-summary .card h2,
.weather-summary .card h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  background: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.dark .budget-summary .card h2,
.dark .weather-summary .card h2 {
  background: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.budget-summary .card h2::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 1.5rem;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
}

.weather-summary .card h2::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 1.5rem;
  background: linear-gradient(135deg, var(--color-accent) 0%, #1d4ed8 100%);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.budget-details {
  margin-top: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  overflow-y: auto;
  max-height: 300px; /* Limit the height to match weather card */
}

.budget-total {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: var(--color-gray-50);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
  border-left: 4px solid var(--color-primary);
}

.dark .budget-total {
  background-color: var(--color-gray-200);
}

.budget-total-labels {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.budget-total-values {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  text-align: right;
}

.budget-total span:first-child {
  color: var(--color-gray-600);
  font-weight: 600;
}

.dark .budget-total span:first-child {
  color: var(--color-gray-500);
}

.budget-spent {
  color: var(--color-gray-600);
  font-size: 0.875rem;
}

.dark .budget-spent {
  color: var(--color-gray-500);
}

#total-budget {
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

#budget-spent {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--color-secondary);
}

.budget-progress-container {
  margin: var(--spacing-1) 0 var(--spacing-2) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.budget-progress {
  height: 0.5rem;
  background-color: var(--color-gray-700);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  transition: background-color var(--transition-normal);
}

.budget-progress-bar {
  height: 100%;
  width: 0%; /* Will be animated with JS */
  background-color: var(--color-success);
  border-radius: var(--border-radius-full);
  transition: width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* .budget-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    transparent 75%
  );
  background-size: 15px 15px;
  border-radius: 5%;
} */

.budget-progress-bar.danger {
  background: linear-gradient(90deg, #ff4d4d 0%, #ff9966 100%);
}

.budget-progress-bar.warning {
  background: linear-gradient(90deg, #ffbb33 0%, #ffdd99 100%);
}

.budget-progress-bar.good {
  background: linear-gradient(90deg, #33cc66 0%, #66ff99 100%);
}

.budget-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--color-gray-400);
}

.dark .budget-labels {
  color: var(--color-gray-500);
}

#remaining-budget {
  font-weight: 600;
  color: var(--color-success);
}

.budget-categories {
  margin-top: var(--spacing-4);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

.budget-category {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.budget-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .budget-category {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.budget-category:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.budget-category:hover::before {
  opacity: 1;
}

.dark .budget-category:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.25),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.budget-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.budget-category-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: 0.875rem;
  color: var(--color-gray-700);
  font-weight: 600;
}

.budget-category-title i {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
}

.budget-category-percentage {
  font-size: 0.75rem;
  font-weight: 700;
  padding: 2px 8px;
  border-radius: var(--border-radius-full);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.budget-category-percentage.danger {
  color: var(--color-error);
  background: rgba(239, 68, 68, 0.1);
}

.budget-category-percentage.warning {
  color: var(--color-warning);
  background: rgba(245, 158, 11, 0.1);
}

.budget-category-percentage.good {
  color: var(--color-success);
  background: rgba(34, 197, 94, 0.1);
}

.dark .budget-category-title {
  color: var(--color-gray-600);
}

.budget-category-amounts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-3);
}

.budget-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.budget-amount .label {
  color: var(--color-gray-600);
  font-weight: 500;
}

.budget-amount .value {
  font-weight: 600;
  color: var(--color-gray-800);
}

.budget-amount .value.spent {
  color: var(--color-primary);
}

.budget-amount .value.remaining.low {
  color: var(--color-error);
}

.dark .budget-amount .label {
  color: var(--color-gray-500);
}

.dark .budget-amount .value {
  color: var(--color-gray-700);
}

/* Simplified Budget Category Styles */
.budget-category.simplified .budget-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.budget-category.simplified .budget-category-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
}

.budget-category.simplified .budget-category-amount .spent {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--color-primary);
}

.budget-category.simplified .budget-category-amount .total {
  font-size: 0.875rem;
  color: var(--color-gray-600);
  font-weight: 500;
}

.budget-category.simplified .budget-category-amount .total::before {
  content: '/ ';
}

.dark .budget-category.simplified .budget-category-amount .total {
  color: var(--color-gray-400);
}

.category-progress {
  height: 8px;
  background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .category-progress {
  background: linear-gradient(90deg, var(--color-gray-300) 0%, var(--color-gray-200) 100%);
}

.category-progress-bar {
  height: 100%;
  border-radius: var(--border-radius-full);
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: linear-gradient(90deg, var(--color-success) 0%, #16a34a 100%);
  box-shadow: 0 1px 3px rgba(34, 197, 94, 0.3);
}

.category-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.3) 100%
  );
  border-radius: var(--border-radius-full);
}

.category-progress-bar.danger {
  background: linear-gradient(90deg, var(--color-error) 0%, #dc2626 100%);
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3);
}

.category-progress-bar.warning {
  background: linear-gradient(90deg, var(--color-warning) 0%, #d97706 100%);
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
}

.category-progress-bar.good {
  background: linear-gradient(90deg, var(--color-success) 0%, #16a34a 100%);
  box-shadow: 0 1px 3px rgba(34, 197, 94, 0.3);
}

/* Pulse animation for budget bar */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.budget-progress-bar.pulse {
  animation: pulse 0.6s ease-in-out;
}

/* Weather card improvements */
.weather-days {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
  margin-top: var(--spacing-4);
}

.weather-day {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  flex: 1;
  min-width: calc(33.333% - var(--spacing-4));
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* For cases where there are only 2 days */
.weather-days.two-days .weather-day {
  min-width: calc(50% - var(--spacing-2));
}

/* For cases where there is only 1 day */
.weather-days.one-day .weather-day {
  min-width: 100%;
}

.dark .weather-day {
  background-color: var(--budget-category-bg-dark);
}

.weather-day::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-accent) 0%, var(--color-secondary) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.weather-day:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: rgba(59, 130, 246, 0.3);
}

.weather-day:hover::before {
  opacity: 1;
}

.dark .weather-day {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .weather-day:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.25),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
}

.weather-date {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--budget-text-dark-light);
  margin-bottom: 0;
}

.dark .weather-date {
  color: var(--budget-text-light-dark);
}

.weather-location {
  font-size: 0.688rem;
  color: var(--budget-text-muted-light);
}

.weather-day-label {
  font-size: 0.625rem;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--color-accent);
  margin-bottom: 1px;
  letter-spacing: 0.5px;
  display: inline-block;
  padding: 2px 6px;
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: var(--border-radius-full);
}

.dark .weather-day-label {
  color: var(--color-accent-light);
  background-color: rgba(59, 130, 246, 0.2);
}

.weather-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  margin-top: auto;
  padding-top: var(--spacing-1);
}

.weather-info i {
  font-size: 1.25rem;
  color: var(--color-accent);
  margin-right: var(--spacing-1);
}

.dark .weather-info i {
  color: var(--color-accent-light);
}

.weather-temp {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--budget-text-dark-light);
}

.dark .weather-temp {
  color: var(--budget-text-light-dark);
}

.weather-meta {
  display: flex;
  gap: var(--spacing-2);
  font-size: 0.688rem;
  color: var(--budget-text-muted-light);
  margin-top: var(--spacing-1);
  border-top: 1px dashed rgba(0,0,0,0.05);
  padding-top: var(--spacing-1);
}

.dark .weather-meta {
  color: var(--budget-text-muted-dark);
  border-top-color: rgba(255,255,255,0.05);
}

.weather-meta-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.weather-meta-item i {
  font-size: 0.625rem;
  opacity: 0.8;
}

/* Weather Overview improvements */
.weather-overview {
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-3);
  background-color: var(--budget-category-bg-light);
  border-radius: var(--border-radius-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: left;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}

.weather-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-accent);
}

.dark .weather-overview {
  background-color: var(--budget-category-bg-dark);
}

.current-weather {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.weather-icon {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(59, 130, 246, 0.1);
}

.weather-icon i {
  font-size: 2rem;
  color: var(--color-accent);
}

.dark .weather-icon i {
  color: var(--color-accent-light);
}

.weather-temp-container {
  display: flex;
  flex-direction: column;
}

.current-temp {
  font-size: 1.5rem;
  line-height: 1.2;
  font-weight: 600;
  color: var(--budget-text-dark-light);
}

.dark .current-temp {
  color: var(--budget-text-light-dark);
}

.current-condition {
  font-size: 0.875rem;
  color: var(--budget-text-muted-light);
}

.dark .current-condition {
  color: var(--budget-text-muted-dark);
}

.today-label {
  font-size: 0.688rem;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--color-accent);
  letter-spacing: 0.5px;
  margin-bottom: 1px;
  display: inline-block;
  padding: 2px 6px;
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: var(--border-radius-full);
}

.dark .today-label {
  color: var(--color-accent-light);
  background-color: rgba(59, 130, 246, 0.2);
}

/* Weather details */
.weather-details-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.weather-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: 0.75rem;
  color: var(--budget-text-dark-light);
}

.dark .weather-detail {
  color: var(--budget-text-light-dark);
}

.weather-detail i {
  color: var(--color-accent);
  font-size: 0.75rem;
}

.dark .weather-detail i {
  color: var(--color-accent-light);
}

/* Show percentages in budget cards */
.budget-percentage {
  position: absolute;
  right: var(--spacing-2);
  top: var(--spacing-2);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--budget-text-muted-light);
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.dark .budget-percentage {
  color: var(--budget-text-muted-dark);
  background-color: rgba(0, 0, 0, 0.2);
}

.budget-percentage.high {
  color: var(--budget-danger);
}

.budget-percentage.medium {
  color: var(--budget-warning);
}

.budget-percentage.low {
  color: var(--budget-accent);
}

/* Enhanced Responsive Styles for Overview Grid */
@media (max-width: 1200px) {
  .budget-summary, .weather-summary {
    flex: 1 1 400px;
  }

  .budget-summary {
    max-height: 600px; /* Increase height for medium screens */
  }
}

@media (max-width: 992px) {
  .budget-summary, .weather-summary {
    flex: 1 1 100%;
  }

  .weather-summary {
    margin-top: 0;
  }

  .budget-summary {
    max-height: none; /* Remove height restriction on tablets */
    overflow-y: visible;
  }

  .overview-grid {
    gap: var(--spacing-6);
  }
}

@media (max-width: 768px) {
  .overview-section {
    padding: var(--spacing-6) 0;
  }

  .overview-section::before {
    opacity: 0.1; /* Reduce pattern opacity on mobile */
  }

  .budget-summary .card, .weather-summary .card {
    padding: var(--spacing-5);
    border-radius: var(--border-radius-lg);
  }

  .budget-summary .card h2, .weather-summary .card h2 {
    font-size: 1.375rem;
    margin-bottom: var(--spacing-5);
  }

  .budget-categories {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
    margin-top: var(--spacing-4);
  }

  .budget-category {
    padding: var(--spacing-4);
  }

  .budget-category-header {
    margin-bottom: var(--spacing-3);
  }

  .budget-category-title {
    font-size: 0.9rem;
  }

  .budget-category-title i {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .weather-days {
    flex-direction: column;
    gap: var(--spacing-4);
    margin-top: var(--spacing-4);
  }

  .weather-day {
    min-width: 100%;
    max-width: 100%;
    padding: var(--spacing-4);
  }

  .weather-overview {
    padding: var(--spacing-4);
  }

  .weather-icon {
    width: 60px;
    height: 60px;
  }

  .weather-icon i {
    font-size: 2.25rem;
  }

  .current-temp {
    font-size: 1.75rem;
  }
}

@media (max-width: 576px) {
  .overview-section {
    padding: var(--spacing-4) 0;
  }

  .overview-section::before {
    display: none; /* Remove pattern completely on small screens */
  }

  .budget-summary .card, .weather-summary .card {
    padding: var(--spacing-4);
    border-radius: var(--border-radius-lg);
  }

  .budget-summary .card h2, .weather-summary .card h2 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-4);
  }

  .budget-categories {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .budget-category {
    padding: var(--spacing-3);
  }

  .budget-category-title {
    font-size: 0.85rem;
  }

  .budget-category-title i {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .budget-amount {
    font-size: 0.75rem;
  }

  .category-progress {
    height: 6px;
  }

  .weather-overview {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
  }

  .current-weather {
    width: 100%;
    justify-content: space-between;
  }

  .weather-details-container {
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    gap: var(--spacing-4);
  }

  .weather-detail {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-1);
    text-align: center;
  }

  .weather-day {
    padding: var(--spacing-3);
  }

  .weather-icon {
    width: 50px;
    height: 50px;
  }

  .weather-icon i {
    font-size: 1.75rem;
  }

  .current-temp {
    font-size: 1.5rem;
  }

  .current-condition {
    font-size: 0.8rem;
  }
}