<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Tsafira | My Account</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="../css/account.css">
  <link rel="stylesheet" href="../css/calendar-events.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <meta name="description" content="Manage your Tsafira account, view itineraries, and access your travel information.">
  <meta name="theme-color" content="#ff7e33">

  <!-- Hide body initially to prevent flash -->
  <style>
    body {
      visibility: hidden;
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <div class="header-left">
          <button class="mobile-menu-toggle focus-visible" aria-label="Toggle menu">
            <i class="fas fa-bars"></i>
          </button>
          <a href="/" class="logo">Tsafira</a>
        </div>
        <nav class="header-nav">
          <a href="/" class="nav-link focus-visible">
            <i class="fas fa-home"></i>
            <span>Home</span>
          </a>
          <a href="./destinations.html" class="nav-link focus-visible">
            <i class="fa-solid fa-location-dot"></i>
            <span>Destinations</span>
          </a>
          <button class="theme-toggle nav-link focus-visible" aria-label="Toggle dark/light mode">
            <i class="fas fa-sun"></i>
            <i class="fas fa-moon"></i>
            <span class="visually-hidden">Toggle dark mode</span>
          </button>
          <div class="profile-dropdown">
            <img src="https://via.placeholder.com/40x40/e5e7eb/6b7280?text=U"
                 alt="Profile"
                 class="profile-image"/>
          </div>
        </nav>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="app-container">
    <!-- Sidebar -->
    <aside class="sidebar-nav">
      <!-- Profile Summary - Will be populated from userData.json -->
      <div class="sidebar-profile">
        <div class="profile-photo-container">
          <img src=""
               alt="Profile"
               class="profile-photo"/>
          <div class="photo-upload-overlay">
            <i class="fas fa-camera"></i>
            Change Photo
          </div>
        </div>
        <h2 class="profile-name"></h2>
        <p class="profile-email">
          <i class="fas fa-envelope"></i>
          <span></span>
        </p>
      </div>

      <!-- Navigation -->
      <nav class="sidebar-menu">
        <div class="nav-item active" data-section="profile">
          <i class="fas fa-user"></i>
          <span>Profile & Settings</span>
        </div>
        <div class="nav-item" onclick="window.location.href='./trips.html'" style="cursor: pointer;">
          <i class="fas fa-route"></i>
          <span>My Trips</span>
        </div>
        <div class="nav-item" data-section="notifications">
          <i class="fas fa-bell"></i>
          <span>Notifications</span>
          <span class="notification-badge">3</span>
        </div>
        <div class="nav-item" data-section="saved">
          <i class="fas fa-bookmark"></i>
          <span>Saved Places</span>
        </div>
        <div class="nav-item" data-section="help">
          <i class="fas fa-question-circle"></i>
          <span>Help & Support</span>
        </div>
      </nav>

      <!-- Logout -->
      <div class="sidebar-footer">
        <div class="nav-item logout-button">
          <i class="fas fa-sign-out-alt"></i>
          <span>Log Out</span>
        </div>
      </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content">
      <div class="content-container">
        <!-- Profile & Settings -->
        <section id="profile" class="content-section active slide-in-up">
          <div class="section-header">
            <h1>Profile & Settings</h1>
            <button class="primary-button focus-visible">
              <i class="fas fa-save"></i>
              Save Changes
            </button>
          </div>



          <!-- Personal Information -->
          <div class="card">
            <h2 class="card-title"><i class="fas fa-user-circle"></i> Personal Information</h2>
            <p class="card-subtitle">Update your basic profile information</p>

            <div class="form-grid">
              <div class="form-group">
                <label for="first-name">First Name</label>
                <input type="text" id="first-name" class="form-input focus-visible" value=""/>
              </div>
              <div class="form-group">
                <label for="last-name">Last Name</label>
                <input type="text" id="last-name" class="form-input focus-visible" value=""/>
              </div>
              <div class="form-group">
                <label for="email">Email</label>
                <div class="form-input-with-icon">
                  <input type="email" id="email" class="form-input is-valid" value="" readonly/>
                  <span class="input-icon success">
                    <i class="fas fa-check-circle"></i>
                  </span>
                </div>
                <div class="form-hint">Your email is verified and cannot be changed</div>
              </div>
              <div class="form-group">
                <label for="phone">Phone Number</label>
                <div class="form-input-with-icon">
                  <input type="tel" id="phone" class="form-input focus-visible" value=""/>
                  <span class="input-icon">
                    <i class="fas fa-phone"></i>
                  </span>
                </div>
              </div>
              <div class="form-group">
                <label for="password">Password</label>
                <div class="form-input-with-icon">
                  <input type="password" id="password" class="form-input password-input focus-visible" placeholder="Change your password" />
                  <span class="input-icon toggle-password">
                    <i class="fas fa-eye"></i>
                  </span>
                </div>
                <div class="password-strength">
                  <div class="strength-meter">
                    <div class="strength-meter-fill"></div>
                  </div>
                  <div class="strength-text">Password strength will appear here</div>
                </div>
              </div>
              <div class="form-group">
                <label for="confirm-password">Confirm Password</label>
                <div class="form-input-with-icon">
                  <input type="password" id="confirm-password" class="form-input confirm-password-input focus-visible" placeholder="Confirm new password" />
                  <span class="input-icon toggle-password">
                    <i class="fas fa-eye"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Preferences -->
          <div class="card">
            <h2 class="card-title"><i class="fas fa-sliders-h"></i> Preferences</h2>
            <p class="card-subtitle">Customize your account settings</p>

            <div class="form-grid">
              <div class="form-group">
                <label for="currency">Currency</label>
                <select id="currency" class="form-input focus-visible">
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="MAD">MAD (د.م.)</option>
                  <option value="GBP">GBP (£)</option>
                  <option value="JPY">JPY (¥)</option>
                </select>
              </div>
              <div class="form-group">
                <label for="language">Language</label>
                <select id="language" class="form-input focus-visible">
                  <option value="English">English</option>
                  <option value="Français">Français</option>
                  <option value="العربية">العربية</option>
                  <option value="Español">Español</option>
                  <option value="Deutsch">Deutsch</option>
                </select>
              </div>
              <div class="form-group">
                <label for="timezone">Time Zone</label>
                <select id="timezone" class="form-input focus-visible">
                  <option value="Eastern Time (ET)">Eastern Time (ET)</option>
                  <option value="Pacific Time (PT)">Pacific Time (PT)</option>
                  <option value="Morocco Time (GMT+1)">Morocco Time (GMT+1)</option>
                  <option value="Central European Time (CET)">Central European Time (CET)</option>
                  <option value="Japan Standard Time (JST)">Japan Standard Time (JST)</option>
                </select>
              </div>
              <div class="form-group">
                <label for="date-format">Date Format</label>
                <select id="date-format" class="form-input focus-visible">
                  <option>MM/DD/YYYY</option>
                  <option>DD/MM/YYYY</option>
                  <option>YYYY-MM-DD</option>
                </select>
              </div>
              <div class="form-group">
                <label for="theme-preference">Theme</label>
                <select id="theme-preference" class="form-input focus-visible">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto (System)</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Travel Preferences -->
          <div class="card">
            <h2 class="card-title"><i class="fas fa-plane"></i> Travel Preferences</h2>
            <p class="card-subtitle">Rate your interest level for different travel experiences</p>

            <div class="travel-preferences-grid">
              <div class="preference-item">
                <label>Historical Sites & Museums</label>
                <div class="preference-control" data-interest-id="2">
                  <div class="preference-option" data-value="0.0">Very Low</div>
                  <div class="preference-option" data-value="0.25">Low</div>
                  <div class="preference-option active" data-value="0.5">Medium</div>
                  <div class="preference-option" data-value="0.75">High</div>
                  <div class="preference-option" data-value="1.0">Very High</div>
                </div>
              </div>

              <div class="preference-item">
                <label>Local Cuisine & Food Tours</label>
                <div class="preference-control" data-interest-id="3">
                  <div class="preference-option" data-value="0.0">Very Low</div>
                  <div class="preference-option" data-value="0.25">Low</div>
                  <div class="preference-option active" data-value="0.5">Medium</div>
                  <div class="preference-option" data-value="0.75">High</div>
                  <div class="preference-option" data-value="1.0">Very High</div>
                </div>
              </div>

              <div class="preference-item">
                <label>Outdoor Activities & Nature</label>
                <div class="preference-control" data-interest-id="4">
                  <div class="preference-option" data-value="0.0">Very Low</div>
                  <div class="preference-option" data-value="0.25">Low</div>
                  <div class="preference-option active" data-value="0.5">Medium</div>
                  <div class="preference-option" data-value="0.75">High</div>
                  <div class="preference-option" data-value="1.0">Very High</div>
                </div>
              </div>

              <div class="preference-item">
                <label>Shopping & Markets</label>
                <div class="preference-control" data-interest-id="5">
                  <div class="preference-option" data-value="0.0">Very Low</div>
                  <div class="preference-option" data-value="0.25">Low</div>
                  <div class="preference-option active" data-value="0.5">Medium</div>
                  <div class="preference-option" data-value="0.75">High</div>
                  <div class="preference-option" data-value="1.0">Very High</div>
                </div>
              </div>

              <div class="preference-item">
                <label>Arts & Cultural Events</label>
                <div class="preference-control" data-interest-id="6">
                  <div class="preference-option" data-value="0.0">Very Low</div>
                  <div class="preference-option" data-value="0.25">Low</div>
                  <div class="preference-option active" data-value="0.5">Medium</div>
                  <div class="preference-option" data-value="0.75">High</div>
                  <div class="preference-option" data-value="1.0">Very High</div>
                </div>
              </div>
            </div>
          </div>




        </section>



        <!-- Notifications -->
        <section id="notifications" class="content-section slide-in-up">
          <div class="section-header">
            <h1>Notifications</h1>
            <button class="text-button focus-visible">
              <i class="fas fa-check-double"></i>
              Mark all as read
            </button>
          </div>

          <!-- Notification Filters -->
          <div class="notification-filters">
            <span class="filter-label">Filter by:</span>
            <div class="notification-category-filter">
              <div class="category-option active" data-filter="all">All</div>
              <div class="category-option" data-filter="messages">Messages</div>
              <div class="category-option" data-filter="trips">Trips</div>
              <div class="category-option" data-filter="system">System</div>
            </div>
          </div>

          <div class="notification-list">
            <div class="notifications-container">
              <!-- Notifications will be populated dynamically from userData.json -->
            </div>

            <!-- Empty state for notifications -->
            <div class="empty-notifications" style="display: none;">
              <div class="empty-notifications-icon">
                <i class="fas fa-bell-slash"></i>
              </div>
              <h3>No notifications</h3>
              <p>You don't have any notifications at the moment.</p>
            </div>
          </div>

          <!-- Pagination -->
          <div class="pagination" style="margin-top: 1.5rem; text-align: center;">
            <button class="icon-button focus-visible">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span style="margin: 0 1rem;">Page 1 of 1</span>
            <button class="icon-button focus-visible">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </section>



        <!-- Saved Places Section -->
        <section id="saved" class="content-section slide-in-up">
          <div class="section-header">
            <h1>Saved Places</h1>
            <div class="d-flex gap-2">
              <button class="text-button focus-visible">
                <i class="fas fa-th-large"></i>
                Grid View
              </button>
              <button class="text-button focus-visible">
                <i class="fas fa-list"></i>
                List View
              </button>
            </div>
          </div>

          <!-- Filters -->
          <div class="itinerary-filters">
            <span class="filter-label">Filter by:</span>
            <div class="filter-group">
              <div class="filter-option active">All</div>
              <div class="filter-option">Attractions</div>
              <div class="filter-option">Restaurants</div>
              <div class="filter-option">Hotels</div>
            </div>
          </div>

          <!-- Empty state for saved places -->
          <div class="empty-state">
            <div class="empty-state-icon">
              <i class="fas fa-bookmark"></i>
            </div>
            <h3>No saved places yet</h3>
            <p>Start exploring destinations and save your favorite places to visit during your trip.</p>
            <button class="primary-button focus-visible">
              <i class="fas fa-globe-africa"></i>
              Explore Destinations
            </button>
          </div>
        </section>

        <!-- Help & Support Section -->
        <section id="help" class="content-section slide-in-up">
          <div class="section-header">
            <h1>Help & Support</h1>
          </div>

          <div class="card">
            <h2 class="card-title"><i class="fas fa-question-circle"></i> Frequently Asked Questions</h2>
            <p class="card-subtitle">Find quick answers to common questions</p>

            <div class="faq-list" style="margin-top: 1.5rem;">
              <div class="faq-item" style="margin-bottom: 1.5rem; border-bottom: 1px solid var(--border-color); padding-bottom: 1.5rem;">
                <h3 style="font-size: 1.125rem; margin-bottom: 0.5rem;">How do I book a guide?</h3>
                <p style="color: var(--text-color-secondary);">You can book a guide by browsing our guide listings, selecting a guide that matches your needs, and sending a booking request with your preferred dates and details.</p>
              </div>

              <div class="faq-item" style="margin-bottom: 1.5rem; border-bottom: 1px solid var(--border-color); padding-bottom: 1.5rem;">
                <h3 style="font-size: 1.125rem; margin-bottom: 0.5rem;">What is the cancellation policy?</h3>
                <p style="color: var(--text-color-secondary);">Our standard cancellation policy allows free cancellation up to 48 hours before your scheduled tour. Cancellations made within 48 hours may be subject to a fee.</p>
              </div>

              <div class="faq-item" style="margin-bottom: 1.5rem;">
                <h3 style="font-size: 1.125rem; margin-bottom: 0.5rem;">How do I become a guide on Tsafira?</h3>
                <p style="color: var(--text-color-secondary);">To become a guide, you need to create an account, complete your profile with relevant experience and qualifications, and submit an application for review by our team.</p>
              </div>
            </div>
          </div>

          <div class="card">
            <h2 class="card-title"><i class="fas fa-headset"></i> Contact Support</h2>
            <p class="card-subtitle">Get in touch with our support team</p>

            <div class="form-group" style="margin-top: 1.5rem;">
              <label for="support-subject">Subject</label>
              <select id="support-subject" class="form-input focus-visible">
                <option>Booking Issue</option>
                <option>Account Problem</option>
                <option>Payment Question</option>
                <option>Guide Inquiry</option>
                <option>Other</option>
              </select>
            </div>

            <div class="form-group">
              <label for="support-message">Message</label>
              <textarea id="support-message" class="form-input focus-visible" rows="5" placeholder="Describe your issue or question in detail..."></textarea>
            </div>

            <button class="primary-button focus-visible" style="margin-top: 1rem;">
              <i class="fas fa-paper-plane"></i>
              Send Message
            </button>
          </div>
        </section>
      </div>
    </main>
  </div>

  <!-- Toast Message Example -->
  <div class="toast-message success" style="display: none;">
    <div class="toast-icon">
      <i class="fas fa-check-circle"></i>
    </div>
    <div class="toast-content">
      <div class="toast-title">Success!</div>
      <div>Your changes have been saved successfully.</div>
    </div>
    <button class="toast-close">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Scripts -->
  <!-- Immediate auth check to prevent content flash -->
  <script type="module">
    import { supabase } from '../js/supabaseClient.js';
    import headerAuthManager from '../js/core/headerAuth.js';

    // Immediate authentication check
    (async function() {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          // Redirect immediately before any content renders
          window.location.href = './signin.html';
          return;
        }

        // User is authenticated, show the page content
        document.body.style.visibility = 'visible';

        // Initialize header authentication
        await headerAuthManager.initialize();
      } catch (error) {
        console.error('Auth check error:', error);
        // On error, redirect to signin
        window.location.href = './signin.html';
      }
    })();
  </script>

  <script type="module" src="../js/pages/account/index-acc.js"></script>

  <!-- Initial theme application to prevent flash of wrong theme -->
  <script>
    (function() {
      // Apply theme immediately before any JS modules load
      const isDark = localStorage.getItem('theme') === 'dark';
      if (isDark) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark-mode');
      }
    })();
  </script>

</body>
</html>