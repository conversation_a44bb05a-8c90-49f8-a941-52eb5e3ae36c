/**
 * Clean Transport Service
 * 
 * Simplified transport calculation and saving system that:
 * - Removes Edge Function dependencies
 * - Handles all calculations in frontend
 * - Uses simple database operations
 * - Integrates cleanly with budget_allocation table
 * - Follows the established transport flow
 */

import { supabase } from '../supabaseClient.js';

export class CleanTransportService {
  constructor() {
    // Transport calculation constants
    this.DAILY_CAR_RENTAL_USD = 20.0;
    this.USD_TO_MAD_RATE = 9.5;
    this.FUEL_EFFICIENCY_KMPL = 17.5; // km per liter
    this.GASOLINE_PRICE_USD = 1.20; // per liter
    
    // Taxi pricing constants
    this.TAXI_BASE_FARE_MAD = 7.50;
    this.TAXI_RATE_PER_80M_MAD = 0.20;
    this.TAXI_MARGIN = 1.20; // 20% margin
    
    // Walking threshold
    this.WALKING_THRESHOLD_KM = 0.5;
    this.WALKING_SPEED_KMH = 4.0;
    
    // OSRM API base URL
    this.OSRM_BASE_URL = 'https://osrm-morocco-308196743891.europe-west1.run.app';
    
    // Transport category ID for budget allocation
    this.TRANSPORT_CATEGORY_ID = 4;
  }

  /**
   * Calculate and save complete transport system for a trip
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Complete transport calculation and save results
   */
  async calculateAndSaveTransport(tripId) {
    console.log(`🚀 CleanTransportService: Starting transport calculation for trip ${tripId}`);
    
    try {
      // Step 1: Clear existing transport data
      await this.clearExistingTransportData(tripId);
      
      // Step 2: Get trip data
      const tripData = await this.getTripData(tripId);
      if (!tripData.success) {
        throw new Error(tripData.error);
      }
      
      // Step 3: Calculate transport for each day
      const transportResults = {
        tripId,
        dailyTransport: [],
        interCityTransport: [],
        totalCostUSD: 0
      };
      
      for (const day of tripData.days) {
        console.log(`📅 Calculating transport for Day ${day.dayNumber}`);
        const dailyResult = await this.calculateDailyTransport(day);
        transportResults.dailyTransport.push(dailyResult);
        transportResults.totalCostUSD += dailyResult.totalCostUSD;
      }
      
      // Step 4: Calculate inter-city transport
      const interCityResults = await this.calculateInterCityTransport(tripData.days);
      transportResults.interCityTransport = interCityResults;
      transportResults.totalCostUSD += interCityResults.reduce((sum, route) => sum + route.costUSD, 0);
      
      // Step 5: Save to database
      await this.saveTransportToDatabase(transportResults);
      
      // Step 6: Update budget allocations
      await this.updateBudgetAllocations(tripId, transportResults.totalCostUSD, transportResults.dailyTransport);
      
      console.log(`✅ Transport calculation completed. Total cost: $${transportResults.totalCostUSD.toFixed(2)}`);
      
      return {
        success: true,
        transportData: transportResults,
        summary: {
          totalSegments: transportResults.dailyTransport.reduce((sum, day) => sum + day.segments.length, 0),
          totalCostUSD: transportResults.totalCostUSD,
          totalDays: transportResults.dailyTransport.length,
          interCityRoutes: transportResults.interCityTransport.length
        }
      };
      
    } catch (error) {
      console.error('❌ Transport calculation failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get trip data for transport calculations
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Trip data
   */
  async getTripData(tripId) {
    try {
      const { data: tripData, error } = await supabase.rpc('get_trip_plan_json', {
        p_trip_id: tripId,
        p_current_day: 1
      });

      if (error) throw error;
      if (!tripData || !tripData.days) throw new Error('No trip data found');

      // Transform data for transport calculations
      const days = tripData.days.map(day => ({
        dayNumber: day.day_number,
        date: day.date,
        cityName: day.location.city,
        
        // Extract locations with coordinates
        accommodation: this.extractLocationData(day.lodging),
        breakfast: this.extractMealLocation(day.meals, 'breakfast'),
        lunch: this.extractMealLocation(day.meals, 'lunch'),
        dinner: this.extractMealLocation(day.meals, 'dinner'),
        activities: day.activities.map(activity => this.extractLocationData(activity))
      }));

      return {
        success: true,
        days: days.filter(day => day.accommodation || day.activities.length > 0)
      };

    } catch (error) {
      console.error('Error getting trip data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract location data from trip item
   * @param {Object} item - Trip item (lodging, activity, etc.)
   * @returns {Object|null} Location data with coordinates
   */
  extractLocationData(item) {
    if (!item || !item.location) {
      console.warn('⚠️ Missing item or location data:', item);
      return null;
    }

    // Try multiple coordinate extraction patterns based on data structure
    let lat, lng;

    // Pattern 1: item.location.coordinates.lat/lng (for activities)
    if (item.location.coordinates) {
      lat = parseFloat(item.location.coordinates.lat);
      lng = parseFloat(item.location.coordinates.lng);
    }

    // Pattern 2: item.location.latitude/longitude (for some items)
    if (isNaN(lat) || isNaN(lng)) {
      lat = parseFloat(item.location.latitude);
      lng = parseFloat(item.location.longitude);
    }

    // Pattern 3: Direct coordinates object (fallback)
    if (isNaN(lat) || isNaN(lng) && item.coordinates) {
      lat = parseFloat(item.coordinates.lat);
      lng = parseFloat(item.coordinates.lng);
    }

    // Validate coordinates
    if (isNaN(lat) || isNaN(lng) || Math.abs(lat) > 90 || Math.abs(lng) > 180) {
      console.warn(`⚠️ Invalid coordinates for ${item.name || 'Unknown'}: lat=${lat}, lng=${lng}`);
      console.warn('⚠️ Item structure:', JSON.stringify(item, null, 2));
      return null;
    }

    const locationData = {
      name: item.name || item.title || 'Unknown Location',
      address: item.location.address || '',
      coordinates: { lat, lng }
    };

    console.log(`📍 Extracted location: ${locationData.name} (${lat.toFixed(6)}, ${lng.toFixed(6)})`);
    return locationData;
  }

  /**
   * Extract meal location by type
   * @param {Array} meals - Array of meals
   * @param {string} mealType - Type of meal (breakfast, lunch, dinner)
   * @returns {Object|null} Meal location data
   */
  extractMealLocation(meals, mealType) {
    if (!meals || !Array.isArray(meals)) {
      console.warn(`⚠️ No meals array provided for ${mealType}`);
      return null;
    }

    // Look for meal by type field (the actual field name in the data)
    const meal = meals.find(m => m.type === mealType);

    if (!meal) {
      console.warn(`⚠️ No ${mealType} meal found in meals:`, meals.map(m => m.type));
      return null;
    }

    // Extract location from restaurant data
    if (meal.restaurant && meal.restaurant.location) {
      const locationData = {
        name: meal.restaurant.name || `${mealType} restaurant`,
        address: meal.restaurant.location.address || '',
        coordinates: {
          lat: parseFloat(meal.restaurant.location.coordinates?.lat),
          lng: parseFloat(meal.restaurant.location.coordinates?.lng)
        }
      };

      // Validate coordinates
      if (isNaN(locationData.coordinates.lat) || isNaN(locationData.coordinates.lng)) {
        console.warn(`⚠️ Invalid coordinates for ${mealType} restaurant:`, meal.restaurant.location);
        return null;
      }

      console.log(`📍 Extracted ${mealType} location: ${locationData.name} (${locationData.coordinates.lat.toFixed(6)}, ${locationData.coordinates.lng.toFixed(6)})`);
      return locationData;
    }

    console.warn(`⚠️ No restaurant location data for ${mealType} meal:`, meal);
    return null;
  }

  /**
   * Calculate daily transport segments following the established flow
   * @param {Object} day - Day data with locations
   * @returns {Promise<Object>} Daily transport calculation
   */
  async calculateDailyTransport(day) {
    const segments = [];
    let totalCostUSD = 0;
    
    // Define transport sequence: Hotel→Restaurant→Activity→Restaurant→Hotel→Restaurant→Hotel
    const transportSequence = this.buildTransportSequence(day);
    
    for (let i = 0; i < transportSequence.length; i++) {
      const { from, to, description } = transportSequence[i];
      
      if (!from || !to) {
        console.warn(`⚠️ Missing location for segment: ${description}`);
        continue;
      }
      
      const segment = await this.calculateTransportSegment(from, to, description, i + 1);
      segments.push(segment);
      totalCostUSD += segment.costUSD;
    }
    
    return {
      dayNumber: day.dayNumber,
      date: day.date,
      cityName: day.cityName,
      segments,
      totalCostUSD
    };
  }

  /**
   * Build transport sequence for a day
   * @param {Object} day - Day data
   * @returns {Array} Transport sequence
   */
  buildTransportSequence(day) {
    const sequence = [];
    
    // Only proceed if we have accommodation
    if (!day.accommodation) {
      console.warn(`⚠️ No accommodation found for day ${day.dayNumber}`);
      return sequence;
    }
    
    // 1. Hotel → Breakfast Restaurant
    if (day.breakfast) {
      sequence.push({
        from: day.accommodation,
        to: day.breakfast,
        description: 'Hotel to Breakfast Restaurant'
      });
    }
    
    // 2. Restaurant → Activity (use first activity)
    if (day.activities.length > 0 && (day.breakfast || day.accommodation)) {
      sequence.push({
        from: day.breakfast || day.accommodation,
        to: day.activities[0],
        description: 'Restaurant to Activity'
      });
    }
    
    // 3. Activity → Lunch Restaurant
    if (day.lunch && day.activities.length > 0) {
      sequence.push({
        from: day.activities[0],
        to: day.lunch,
        description: 'Activity to Lunch Restaurant'
      });
    }
    
    // 4. Restaurant → Hotel
    if (day.lunch) {
      sequence.push({
        from: day.lunch,
        to: day.accommodation,
        description: 'Lunch Restaurant to Hotel'
      });
    }
    
    // 5. Hotel → Dinner Restaurant
    if (day.dinner) {
      sequence.push({
        from: day.accommodation,
        to: day.dinner,
        description: 'Hotel to Dinner Restaurant'
      });
      
      // 6. Restaurant → Hotel
      sequence.push({
        from: day.dinner,
        to: day.accommodation,
        description: 'Dinner Restaurant to Hotel'
      });
    }
    
    return sequence;
  }

  /**
   * Calculate transport segment between two locations
   * @param {Object} from - Origin location
   * @param {Object} to - Destination location
   * @param {string} description - Segment description
   * @param {number} sequence - Sequence number
   * @returns {Promise<Object>} Transport segment calculation
   */
  async calculateTransportSegment(from, to, description, sequence) {
    try {
      // Get distance and duration
      const routeData = await this.getRouteData(from.coordinates, to.coordinates);
      
      // Determine best transport option
      const transportOption = this.selectBestTransportOption(routeData.distance, routeData.duration);
      
      return {
        sequence,
        description,
        from: from.name,
        to: to.name,
        distance: routeData.distance,
        duration: routeData.duration,
        transportType: transportOption.type,
        costUSD: transportOption.costUSD,
        costMAD: transportOption.costMAD,
        reasoning: transportOption.reasoning
      };
      
    } catch (error) {
      console.error(`Error calculating segment ${description}:`, error);
      
      // Return fallback calculation
      return {
        sequence,
        description,
        from: from.name,
        to: to.name,
        distance: 5.0, // Fallback distance
        duration: 15, // Fallback duration
        transportType: 'taxi',
        costUSD: 5.0,
        costMAD: 47.5,
        reasoning: 'Fallback calculation due to routing error'
      };
    }
  }

  /**
   * Get route data using OSRM API
   * @param {Object} from - Origin coordinates {lat, lng}
   * @param {Object} to - Destination coordinates {lat, lng}
   * @returns {Promise<Object>} Route data with distance and duration
   */
  async getRouteData(from, to) {
    try {
      // Validate coordinates
      if (!from || !to || typeof from.lat !== 'number' || typeof from.lng !== 'number' ||
          typeof to.lat !== 'number' || typeof to.lng !== 'number') {
        throw new Error('Invalid coordinates provided');
      }

      // Check for valid coordinate ranges
      if (Math.abs(from.lat) > 90 || Math.abs(to.lat) > 90 ||
          Math.abs(from.lng) > 180 || Math.abs(to.lng) > 180) {
        throw new Error('Coordinates out of valid range');
      }

      const url = `${this.OSRM_BASE_URL}/route/v1/driving/${from.lng},${from.lat};${to.lng},${to.lat}?overview=false&alternatives=false&steps=false`;
      console.log(`🌐 OSRM API URL: ${url}`);

      const response = await fetch(url);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`OSRM API error ${response.status}:`, errorText);
        throw new Error(`OSRM API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('🌐 OSRM API response:', data);

      if (!data.routes || data.routes.length === 0) {
        throw new Error('No route found in OSRM response');
      }

      const route = data.routes[0];
      const result = {
        distance: route.distance / 1000, // Convert to km
        duration: route.duration / 60 // Convert to minutes
      };

      console.log(`✅ OSRM route: ${result.distance.toFixed(2)}km, ${result.duration.toFixed(1)}min`);
      return result;

    } catch (error) {
      console.warn(`⚠️ OSRM API failed (${from.lat},${from.lng} → ${to.lat},${to.lng}):`, error.message);

      // Fallback to straight-line distance with road factor
      const straightDistance = this.calculateStraightLineDistance(from, to);

      if (!straightDistance || straightDistance <= 0) {
        console.error('❌ Fallback calculation also failed, using default values');
        return {
          distance: 1.0, // Default 1km
          duration: 5 // Default 5 minutes
        };
      }

      const fallbackResult = {
        distance: straightDistance * 1.3, // Apply road factor
        duration: (straightDistance * 1.3) / 30 * 60 // Assume 30 km/h average speed
      };

      console.log(`🔄 Fallback calculation: ${fallbackResult.distance.toFixed(2)}km, ${fallbackResult.duration.toFixed(1)}min`);
      return fallbackResult;
    }
  }

  /**
   * Calculate straight-line distance between two coordinates
   * @param {Object} from - Origin coordinates {lat, lng}
   * @param {Object} to - Destination coordinates {lat, lng}
   * @returns {number} Distance in kilometers
   */
  calculateStraightLineDistance(from, to) {
    try {
      // Validate input coordinates
      if (!from || !to || typeof from.lat !== 'number' || typeof from.lng !== 'number' ||
          typeof to.lat !== 'number' || typeof to.lng !== 'number') {
        console.error('❌ Invalid coordinates for distance calculation:', { from, to });
        return null;
      }

      // Check for NaN values
      if (isNaN(from.lat) || isNaN(from.lng) || isNaN(to.lat) || isNaN(to.lng)) {
        console.error('❌ NaN coordinates detected:', { from, to });
        return null;
      }

      const R = 6371; // Earth's radius in kilometers
      const φ1 = from.lat * Math.PI / 180;
      const φ2 = to.lat * Math.PI / 180;
      const Δφ = (to.lat - from.lat) * Math.PI / 180;
      const Δλ = (to.lng - from.lng) * Math.PI / 180;

      const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                Math.cos(φ1) * Math.cos(φ2) *
                Math.sin(Δλ/2) * Math.sin(Δλ/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

      const distance = R * c;

      // Validate result
      if (isNaN(distance) || distance < 0) {
        console.error('❌ Invalid distance calculation result:', distance);
        return null;
      }

      console.log(`📏 Straight-line distance: ${distance.toFixed(2)}km`);
      return distance;

    } catch (error) {
      console.error('❌ Error in distance calculation:', error);
      return null;
    }
  }

  /**
   * Select best transport option based on distance and duration
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Object} Best transport option
   */
  selectBestTransportOption(distanceKm, durationMinutes) {
    // Walking for short distances
    if (distanceKm <= this.WALKING_THRESHOLD_KM) {
      return {
        type: 'walking',
        costUSD: 0,
        costMAD: 0,
        reasoning: `Short distance (${distanceKm.toFixed(2)}km), walking is optimal`
      };
    }

    // Calculate taxi cost
    const taxiCostMAD = (this.TAXI_BASE_FARE_MAD + (distanceKm * 1000 / 80.0) * this.TAXI_RATE_PER_80M_MAD) * this.TAXI_MARGIN;
    const taxiCostUSD = taxiCostMAD / this.USD_TO_MAD_RATE;

    // Calculate car rental cost (prorated daily fee + fuel)
    const fuelCostUSD = (distanceKm / this.FUEL_EFFICIENCY_KMPL) * this.GASOLINE_PRICE_USD;
    const proratedRentalFeeUSD = this.DAILY_CAR_RENTAL_USD / 6.0; // Assume 6 segments per day
    const carCostUSD = fuelCostUSD + proratedRentalFeeUSD;
    const carCostMAD = carCostUSD * this.USD_TO_MAD_RATE;

    // Choose cheaper option
    if (carCostUSD < taxiCostUSD) {
      return {
        type: 'car_rental',
        costUSD: carCostUSD,
        costMAD: carCostMAD,
        reasoning: `Car rental cheaper ($${carCostUSD.toFixed(2)} vs $${taxiCostUSD.toFixed(2)} taxi)`
      };
    } else {
      return {
        type: 'taxi',
        costUSD: taxiCostUSD,
        costMAD: taxiCostMAD,
        reasoning: `Taxi cheaper ($${taxiCostUSD.toFixed(2)} vs $${carCostUSD.toFixed(2)} car rental)`
      };
    }
  }

  /**
   * Calculate inter-city transport
   * @param {Array} days - Array of day data
   * @returns {Promise<Array>} Inter-city transport routes
   */
  async calculateInterCityTransport(days) {
    const interCityRoutes = [];

    // Find city transitions (last day of each city to first day of next city)
    for (let i = 0; i < days.length - 1; i++) {
      const currentDay = days[i];
      const nextDay = days[i + 1];

      if (currentDay.cityName !== nextDay.cityName) {
        try {
          const route = await this.calculateInterCityRoute(currentDay, nextDay);
          interCityRoutes.push(route);
        } catch (error) {
          console.error(`Error calculating inter-city route ${currentDay.cityName} → ${nextDay.cityName}:`, error);
        }
      }
    }

    return interCityRoutes;
  }

  /**
   * Calculate inter-city route
   * @param {Object} fromDay - Origin day data
   * @param {Object} toDay - Destination day data
   * @returns {Promise<Object>} Inter-city route calculation
   */
  async calculateInterCityRoute(fromDay, toDay) {
    // Use accommodation coordinates for inter-city calculation
    const fromCoords = fromDay.accommodation?.coordinates;
    const toCoords = toDay.accommodation?.coordinates;

    if (!fromCoords || !toCoords) {
      throw new Error('Missing accommodation coordinates for inter-city route');
    }

    // Get route data
    const routeData = await this.getRouteData(fromCoords, toCoords);

    // For inter-city, use full daily car rental fee
    const fuelCostUSD = (routeData.distance / this.FUEL_EFFICIENCY_KMPL) * this.GASOLINE_PRICE_USD;
    const totalCostUSD = fuelCostUSD + this.DAILY_CAR_RENTAL_USD;
    const totalCostMAD = totalCostUSD * this.USD_TO_MAD_RATE;

    return {
      fromCity: fromDay.cityName,
      toCity: toDay.cityName,
      fromDay: fromDay.dayNumber,
      toDay: toDay.dayNumber,
      distance: routeData.distance,
      duration: routeData.duration,
      transportType: 'car_rental',
      costUSD: totalCostUSD,
      costMAD: totalCostMAD,
      description: `Inter-city transport from ${fromDay.cityName} to ${toDay.cityName}`,
      reasoning: `Inter-city route requires full daily car rental fee ($${this.DAILY_CAR_RENTAL_USD}) plus fuel costs`
    };
  }

  /**
   * Clear existing transport data for a trip
   * @param {number} tripId - Trip ID
   * @returns {Promise<void>}
   */
  async clearExistingTransportData(tripId) {
    try {
      console.log(`🗑️ Clearing existing transport data for trip ${tripId}`);

      // Delete existing transport records
      const { error: transportError } = await supabase
        .from('trip_transport_calculated')
        .delete()
        .eq('trip_id', tripId);

      if (transportError) throw transportError;

      // Clear transport budget allocations
      const { error: budgetError } = await supabase
        .from('budget_allocation')
        .delete()
        .eq('trip_id', tripId)
        .eq('category_id', this.TRANSPORT_CATEGORY_ID);

      if (budgetError) throw budgetError;

      console.log(`✅ Cleared existing transport data for trip ${tripId}`);

    } catch (error) {
      console.error('Error clearing existing transport data:', error);
      throw error;
    }
  }

  /**
   * Validate transport record before saving
   * @param {Object} record - Transport record to validate
   * @returns {boolean} True if valid, false otherwise
   */
  validateTransportRecord(record) {
    const requiredFields = [
      'trip_id', 'day_number', 'transport_type', 'from_location', 'to_location',
      'distance_km', 'duration_minutes', 'price_usd', 'price_mad'
    ];

    for (const field of requiredFields) {
      if (record[field] === null || record[field] === undefined) {
        console.error(`❌ Missing required field: ${field}`, record);
        return false;
      }

      // Check numeric fields
      if (['distance_km', 'duration_minutes', 'price_usd', 'price_mad'].includes(field)) {
        if (typeof record[field] !== 'number' || isNaN(record[field]) || record[field] < 0) {
          console.error(`❌ Invalid numeric value for ${field}: ${record[field]}`, record);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Save transport data to database
   * @param {Object} transportResults - Transport calculation results
   * @returns {Promise<void>}
   */
  async saveTransportToDatabase(transportResults) {
    console.log(`💾 Saving transport data to database for trip ${transportResults.tripId}`);

    const transportRecords = [];

    // Save daily transport segments
    for (const day of transportResults.dailyTransport) {
      for (const segment of day.segments) {
        const record = {
          trip_id: transportResults.tripId,
          day_number: day.dayNumber,
          transport_type: segment.transportType || 'unknown',
          from_location: segment.from || 'Unknown',
          to_location: segment.to || 'Unknown',
          distance_km: segment.distance || 0,
          duration_minutes: Math.round(segment.duration || 0),
          price_usd: segment.costUSD || 0,
          price_mad: segment.costMAD || 0,
          description: segment.reasoning || 'No description',
          is_primary_recommendation: true,
          transport_sequence: segment.sequence || 1,
          segment_description: segment.description || 'Transport segment',
          reasoning: segment.reasoning || 'No reasoning provided',
          is_inter_city: false
        };

        if (this.validateTransportRecord(record)) {
          transportRecords.push(record);
        } else {
          console.warn(`⚠️ Skipping invalid transport record for day ${day.dayNumber}`);
        }
      }
    }

    // Save inter-city transport
    for (const route of transportResults.interCityTransport) {
      const record = {
        trip_id: transportResults.tripId,
        day_number: route.fromDay,
        transport_type: route.transportType || 'car_rental',
        from_location: route.fromCity || 'Unknown',
        to_location: route.toCity || 'Unknown',
        distance_km: route.distance || 0,
        duration_minutes: Math.round(route.duration || 0),
        price_usd: route.costUSD || 0,
        price_mad: route.costMAD || 0,
        description: route.reasoning || 'Inter-city transport',
        is_primary_recommendation: true,
        transport_sequence: 1, // Inter-city is always sequence 1
        segment_description: route.description || 'Inter-city transport',
        reasoning: route.reasoning || 'Inter-city transport reasoning',
        is_inter_city: true
      };

      if (this.validateTransportRecord(record)) {
        transportRecords.push(record);
      } else {
        console.warn(`⚠️ Skipping invalid inter-city record: ${route.fromCity} → ${route.toCity}`);
      }
    }

    // Batch insert all records
    if (transportRecords.length > 0) {
      console.log(`📝 Attempting to save ${transportRecords.length} validated transport records`);

      const { error } = await supabase
        .from('trip_transport_calculated')
        .insert(transportRecords);

      if (error) {
        console.error('❌ Database insert error:', error);
        throw error;
      }

      console.log(`✅ Successfully saved ${transportRecords.length} transport records to database`);
    } else {
      console.warn('⚠️ No valid transport records to save');
    }
  }

  /**
   * Update budget allocations for transport
   * @param {number} tripId - Trip ID
   * @param {number} totalCostUSD - Total transport cost in USD
   * @param {Array} dailyTransport - Daily transport data
   * @returns {Promise<void>}
   */
  async updateBudgetAllocations(tripId, totalCostUSD, dailyTransport) {
    console.log(`💰 Updating budget allocations for trip ${tripId}`);

    try {
      // First, get the correct trip_day IDs for this trip
      const { data: tripDays, error: tripDayError } = await supabase
        .from('trip_day')
        .select('id, day_number')
        .eq('trip_id', tripId)
        .order('day_number');

      if (tripDayError) {
        console.error('❌ Error fetching trip days:', tripDayError);
        throw tripDayError;
      }

      if (!tripDays || tripDays.length === 0) {
        console.warn('⚠️ No trip days found for trip', tripId);
        throw new Error(`No trip days found for trip ${tripId}`);
      }

      console.log(`📅 Found ${tripDays.length} trip days for trip ${tripId}`);

      // Create a mapping from day number to trip_day ID
      const dayNumberToTripDayId = {};
      tripDays.forEach(tripDay => {
        dayNumberToTripDayId[tripDay.day_number] = tripDay.id;
      });

      // Create trip-level budget allocation
      const tripLevelAllocation = {
        trip_id: tripId,
        category_id: this.TRANSPORT_CATEGORY_ID,
        allocated_amount: totalCostUSD,
        spent_amount: totalCostUSD,
        currency: 'USD',
        trip_day_id: null // Trip-level allocation
      };

      // Create daily budget allocations using correct trip_day_id
      const dailyAllocations = [];
      for (const day of dailyTransport) {
        if (day.totalCostUSD > 0) {
          const tripDayId = dayNumberToTripDayId[day.dayNumber];

          if (!tripDayId) {
            console.warn(`⚠️ No trip_day_id found for day ${day.dayNumber}, skipping budget allocation`);
            continue;
          }

          dailyAllocations.push({
            trip_id: tripId,
            category_id: this.TRANSPORT_CATEGORY_ID,
            allocated_amount: day.totalCostUSD,
            spent_amount: day.totalCostUSD,
            currency: 'USD',
            trip_day_id: tripDayId // Use correct trip_day.id
          });

          console.log(`📊 Day ${day.dayNumber} → trip_day_id: ${tripDayId}, cost: $${day.totalCostUSD.toFixed(2)}`);
        }
      }

      // Insert all budget allocations
      const allAllocations = [tripLevelAllocation, ...dailyAllocations];

      if (allAllocations.length > 0) {
        console.log(`💾 Inserting ${allAllocations.length} budget allocation records`);

        const { error } = await supabase
          .from('budget_allocation')
          .insert(allAllocations);

        if (error) {
          console.error('❌ Budget allocation insert error:', error);
          throw error;
        }

        console.log(`✅ Successfully created ${allAllocations.length} budget allocations`);
        console.log(`📊 Trip-level allocation: $${totalCostUSD.toFixed(2)}`);
        console.log(`📅 Daily allocations: ${dailyAllocations.length} days`);
      } else {
        console.warn('⚠️ No budget allocations to insert');
      }

    } catch (error) {
      console.error('❌ Error updating budget allocations:', error.message || error);
      throw error;
    }
  }

  /**
   * Get transport status for a trip
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Transport status
   */
  async getTransportStatus(tripId) {
    try {
      const { data: transportData, error } = await supabase
        .from('trip_transport_calculated')
        .select('id')
        .eq('trip_id', tripId)
        .limit(1);

      if (error) throw error;

      return {
        hasTransportData: transportData && transportData.length > 0,
        recordCount: transportData ? transportData.length : 0
      };

    } catch (error) {
      console.error('Error checking transport status:', error);
      return {
        hasTransportData: false,
        recordCount: 0,
        error: error.message
      };
    }
  }

  /**
   * Get transport data for display
   * @param {number} tripId - Trip ID
   * @param {number} currentDay - Current day number
   * @returns {Promise<Object>} Transport data formatted for display
   */
  async getTransportForDisplay(tripId, currentDay = 1) {
    try {
      const { data: transportData, error } = await supabase
        .from('trip_transport_calculated')
        .select('*')
        .eq('trip_id', tripId)
        .order('day_number', { ascending: true })
        .order('transport_sequence', { ascending: true });

      if (error) throw error;

      if (!transportData || transportData.length === 0) {
        return {
          success: false,
          error: 'No transport data found'
        };
      }

      // Format data for display
      const dailyTransport = [];
      const interCityTransport = [];
      let totalCostUSD = 0;

      // Group by day and type
      const dayGroups = {};

      for (const record of transportData) {
        totalCostUSD += record.price_usd;

        if (record.is_inter_city) {
          interCityTransport.push({
            fromCity: record.from_location,
            toCity: record.to_location,
            fromDay: record.day_number,
            distance: record.distance_km,
            duration: record.duration_minutes,
            transportType: record.transport_type,
            costUSD: record.price_usd,
            costMAD: record.price_mad,
            description: record.segment_description,
            reasoning: record.reasoning
          });
        } else {
          if (!dayGroups[record.day_number]) {
            dayGroups[record.day_number] = {
              dayNumber: record.day_number,
              segments: [],
              totalCostUSD: 0
            };
          }

          dayGroups[record.day_number].segments.push({
            sequence: record.transport_sequence,
            description: record.segment_description,
            from: record.from_location,
            to: record.to_location,
            distance: record.distance_km,
            duration: record.duration_minutes,
            transportType: record.transport_type,
            costUSD: record.price_usd,
            costMAD: record.price_mad,
            reasoning: record.reasoning
          });

          dayGroups[record.day_number].totalCostUSD += record.price_usd;
        }
      }

      // Convert to arrays and sort
      for (const dayNumber in dayGroups) {
        const day = dayGroups[dayNumber];
        day.segments.sort((a, b) => a.sequence - b.sequence);
        dailyTransport.push(day);
      }

      dailyTransport.sort((a, b) => a.dayNumber - b.dayNumber);

      const currentDayTransport = dailyTransport.find(day => day.dayNumber === currentDay);

      return {
        success: true,
        transportData: {
          dailyTransport,
          interCityTransport,
          currentDayTransport,
          totalCostUSD
        }
      };

    } catch (error) {
      console.error('Error getting transport data for display:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
