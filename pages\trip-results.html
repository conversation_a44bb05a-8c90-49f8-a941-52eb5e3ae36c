<!DOCTYPE html>
<html lang="en" class="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Trip Results - Tsafira</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          colors: {
            orange: {
              50: '#fff7ed',
              100: '#ffedd5',
              200: '#fed7aa',
              300: '#fdba74',
              400: '#fb923c',
              500: '#f97316',
              600: '#ea580c',
              700: '#c2410c',
              800: '#9a3412',
              900: '#7c2d12'
            }
          }
        }
      }
    }
  </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200">
  <!-- Header -->
  <header class="bg-white dark:bg-gray-800 shadow-sm">
    <div class="container mx-auto px-4 py-4 flex items-center justify-between">
      <a href="/" class="text-2xl font-bold text-orange-600 dark:text-orange-500">Tsafira</a>
      <div class="flex items-center gap-4">
        <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
          <i id="theme-icon" class="fa-solid fa-moon"></i>
        </button>
        <a href="/" class="text-gray-600 dark:text-gray-300 hover:text-orange-600">
          <i class="fa-solid fa-home"></i>
        </a>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="container mx-auto px-4 py-8">
    <!-- Loading State -->
    <div id="loading-state" class="text-center py-12">
      <div class="animate-spin text-orange-600 text-4xl mb-4">
        <i class="fas fa-circle-notch"></i>
      </div>
      <h2 class="text-2xl font-semibold mb-2 text-gray-900 dark:text-white">Loading Your Trip Results</h2>
      <p class="text-gray-600 dark:text-gray-400">Please wait while we fetch your personalized itinerary...</p>
    </div>

    <!-- Error State -->
    <div id="error-state" class="hidden text-center py-12">
      <div class="text-red-500 text-4xl mb-4">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h2 class="text-2xl font-semibold mb-2 text-gray-900 dark:text-white">Oops! Something went wrong</h2>
      <p class="text-gray-600 dark:text-gray-400 mb-4" id="error-message">We couldn't load your trip results.</p>
      <button onclick="window.location.reload()" class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700">
        Try Again
      </button>
    </div>

    <!-- Results Content -->
    <div id="results-content" class="hidden">
      <!-- Trip Header -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white" id="trip-title">Your Morocco Adventure</h1>
          <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium" id="trip-status">
            Generated Successfully
          </span>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span class="text-gray-500 dark:text-gray-400">Duration:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2" id="trip-duration">-</span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Budget:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2" id="trip-budget">-</span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Cities:</span>
            <span class="font-medium text-gray-900 dark:text-white ml-2" id="trip-cities-count">-</span>
          </div>
        </div>
      </div>

      <!-- Selected Cities -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Your Selected Cities</h2>
        <div id="cities-list" class="space-y-4">
          <!-- Cities will be populated here -->
        </div>
      </div>

      <!-- Trip Summary -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Trip Summary</h2>
        <div id="trip-summary" class="text-gray-600 dark:text-gray-400">
          <!-- Summary will be populated here -->
        </div>
      </div>

      <!-- Actions -->
      <div class="flex flex-wrap gap-4 justify-center">
        <button onclick="window.print()" class="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 flex items-center gap-2">
          <i class="fas fa-print"></i>
          Print Itinerary
        </button>
        <button onclick="shareTrip()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2">
          <i class="fas fa-share"></i>
          Share Trip
        </button>
        <a href="wizard.html" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 flex items-center gap-2">
          <i class="fas fa-plus"></i>
          Plan Another Trip
        </a>
      </div>
    </div>
  </div>

  <script type="module">
    import { supabase } from '../js/supabaseClient.js';

    // Get trip ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const tripId = urlParams.get('trip_id');

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');

    function initializeTheme() {
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
      }
    }

    themeToggle?.addEventListener('click', () => {
      document.documentElement.classList.toggle('dark');
      const isDark = document.documentElement.classList.contains('dark');
      themeIcon.classList.toggle('fa-moon', !isDark);
      themeIcon.classList.toggle('fa-sun', isDark);
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });

    // Load trip results
    async function loadTripResults() {
      if (!tripId) {
        showError('No trip ID provided in the URL.');
        return;
      }

      try {
        // Fetch trip data from Supabase
        const { data: trip, error } = await supabase
          .from('trip')
          .select('*')
          .eq('id', tripId)
          .single();

        if (error) {
          throw error;
        }

        if (!trip) {
          throw new Error('Trip not found');
        }

        // Display the trip results
        displayTripResults(trip);

      } catch (error) {
        console.error('Error loading trip results:', error);
        showError(`Failed to load trip results: ${error.message}`);
      }
    }

    function displayTripResults(trip) {
      // Hide loading state
      document.getElementById('loading-state').classList.add('hidden');
      
      // Show results content
      document.getElementById('results-content').classList.remove('hidden');

      // Populate trip header
      document.getElementById('trip-title').textContent = trip.title || 'Your Morocco Adventure';
      
      // Calculate duration
      const startDate = new Date(trip.start_date);
      const endDate = new Date(trip.end_date);
      const duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      document.getElementById('trip-duration').textContent = `${duration} days`;
      
      // Display budget
      document.getElementById('trip-budget').textContent = `${trip.budget_total} ${trip.currency}`;

      // Display trip summary
      document.getElementById('trip-summary').textContent = trip.description || 'A personalized Morocco travel itinerary.';

      // Check if we have processed results (budget_plan)
      if (trip.budget_plan && trip.budget_plan.selected_cities) {
        displayProcessedResults(trip.budget_plan);
      } else {
        // Show basic trip info
        document.getElementById('trip-cities-count').textContent = 'Processing...';
        document.getElementById('cities-list').innerHTML = '<p class="text-gray-500">Your itinerary is being processed. Please check back in a few minutes.</p>';
      }
    }

    function displayProcessedResults(budgetPlan) {
      const cities = budgetPlan.selected_cities || [];
      document.getElementById('trip-cities-count').textContent = `${cities.length} cities`;

      // Display cities
      const citiesList = document.getElementById('cities-list');
      citiesList.innerHTML = '';

      cities.forEach((city, index) => {
        const cityElement = document.createElement('div');
        cityElement.className = 'border border-gray-200 dark:border-gray-700 rounded-lg p-4';
        cityElement.innerHTML = `
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${city.city_name}</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">${city.days} days</span>
          </div>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500 dark:text-gray-400">Budget:</span>
              <span class="font-medium text-gray-900 dark:text-white ml-1">${city.budget_allocation} MAD</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">Daily Budget:</span>
              <span class="font-medium text-gray-900 dark:text-white ml-1">${city.daily_budget} MAD</span>
            </div>
          </div>
          ${city.primary_strengths && city.primary_strengths.length > 0 ? `
            <div class="mt-2">
              <span class="text-gray-500 dark:text-gray-400 text-sm">Highlights:</span>
              <div class="flex flex-wrap gap-1 mt-1">
                ${city.primary_strengths.map(strength => `
                  <span class="bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 px-2 py-1 rounded text-xs">${strength}</span>
                `).join('')}
              </div>
            </div>
          ` : ''}
        `;
        citiesList.appendChild(cityElement);
      });
    }

    function showError(message) {
      document.getElementById('loading-state').classList.add('hidden');
      document.getElementById('error-state').classList.remove('hidden');
      document.getElementById('error-message').textContent = message;
    }

    // Share functionality
    window.shareTrip = function() {
      if (navigator.share) {
        navigator.share({
          title: 'My Morocco Trip Itinerary',
          text: 'Check out my personalized Morocco travel itinerary!',
          url: window.location.href
        });
      } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
          alert('Trip link copied to clipboard!');
        });
      }
    };

    // Initialize
    initializeTheme();
    loadTripResults();
  </script>
</body>
</html>
