<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Tsafira</title>
    <link rel="stylesheet" href="../css/signin.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .verification-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        .verification-card {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .verification-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        .success-icon { color: #10b981; }
        .error-icon { color: #ef4444; }
        .loading-icon { color: #667eea; animation: spin 1s linear infinite; }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .verification-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1a202c;
        }
        .verification-message {
            font-size: 16px;
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .verification-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .verification-button:hover {
            transform: translateY(-2px);
        }
        .resend-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
        }
        .resend-button {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
        }
        .resend-button:hover {
            background: #667eea;
            color: white;
        }
        .resend-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-card">
            <!-- Loading State -->
            <div id="loading-state">
                <div class="verification-icon loading-icon">
                    <i class="fas fa-spinner"></i>
                </div>
                <h1 class="verification-title">Verifying Your Email</h1>
                <p class="verification-message">
                    Please wait while we verify your email address...
                </p>
            </div>

            <!-- Success State -->
            <div id="success-state" style="display: none;">
                <div class="verification-icon success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1 class="verification-title">Email Verified Successfully!</h1>
                <p class="verification-message">
                    Welcome to Tsafira! Your email has been verified and your account is now active. 
                    You can start planning your amazing journeys.
                </p>
                <a href="../pages/destinations.html" class="verification-button">
                    <i class="fas fa-compass mr-2"></i>
                    Explore Destinations
                </a>
            </div>

            <!-- Error State -->
            <div id="error-state" style="display: none;">
                <div class="verification-icon error-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <h1 class="verification-title">Verification Failed</h1>
                <p class="verification-message" id="error-message">
                    We couldn't verify your email address. The link may have expired or already been used.
                </p>
                <a href="../pages/signin.html" class="verification-button">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Back to Sign In
                </a>
                
                <div class="resend-section">
                    <p style="color: #718096; font-size: 14px; margin-bottom: 15px;">
                        Need a new verification email?
                    </p>
                    <button id="resend-button" class="resend-button">
                        <i class="fas fa-envelope mr-2"></i>
                        Resend Verification Email
                    </button>
                </div>
            </div>

            <!-- Already Verified State -->
            <div id="already-verified-state" style="display: none;">
                <div class="verification-icon success-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h1 class="verification-title">Already Verified</h1>
                <p class="verification-message">
                    Your email address has already been verified. You can sign in to your account.
                </p>
                <a href="../pages/signin.html" class="verification-button">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In to Your Account
                </a>
            </div>
        </div>
    </div>

    <script type="module">
        import { supabase } from '../js/supabaseClient.js';

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const type = urlParams.get('type');
        const email = urlParams.get('email');

        // State management
        function showState(stateId) {
            const states = ['loading-state', 'success-state', 'error-state', 'already-verified-state'];
            states.forEach(id => {
                document.getElementById(id).style.display = id === stateId ? 'block' : 'none';
            });
        }

        // Verify email function
        async function verifyEmail() {
            if (!token || !type) {
                showError('Invalid verification link. Please check your email for the correct link.');
                return;
            }

            try {
                const { data, error } = await supabase.auth.verifyOtp({
                    token_hash: token,
                    type: type
                });

                if (error) {
                    if (error.message.includes('already been verified')) {
                        showState('already-verified-state');
                    } else if (error.message.includes('expired')) {
                        showError('This verification link has expired. Please request a new one.');
                    } else {
                        showError(error.message);
                    }
                    return;
                }

                // Success
                showState('success-state');
                
                // Redirect to destinations after 3 seconds
                setTimeout(() => {
                    window.location.href = '../pages/destinations.html';
                }, 3000);

            } catch (error) {
                console.error('Verification error:', error);
                showError('An unexpected error occurred. Please try again.');
            }
        }

        // Show error state
        function showError(message) {
            document.getElementById('error-message').textContent = message;
            showState('error-state');
        }

        // Resend verification email
        async function resendVerification() {
            const resendButton = document.getElementById('resend-button');
            
            if (!email) {
                alert('Email address not found. Please sign up again.');
                return;
            }

            resendButton.disabled = true;
            resendButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

            try {
                const { error } = await supabase.auth.resend({
                    type: 'signup',
                    email: email
                });

                if (error) {
                    throw error;
                }

                resendButton.innerHTML = '<i class="fas fa-check mr-2"></i>Email Sent!';
                resendButton.style.background = '#10b981';
                resendButton.style.borderColor = '#10b981';
                resendButton.style.color = 'white';

                setTimeout(() => {
                    resendButton.disabled = false;
                    resendButton.innerHTML = '<i class="fas fa-envelope mr-2"></i>Resend Verification Email';
                    resendButton.style.background = 'transparent';
                    resendButton.style.borderColor = '#667eea';
                    resendButton.style.color = '#667eea';
                }, 3000);

            } catch (error) {
                console.error('Resend error:', error);
                resendButton.disabled = false;
                resendButton.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Failed to Send';
                resendButton.style.background = '#ef4444';
                resendButton.style.borderColor = '#ef4444';
                resendButton.style.color = 'white';

                setTimeout(() => {
                    resendButton.innerHTML = '<i class="fas fa-envelope mr-2"></i>Resend Verification Email';
                    resendButton.style.background = 'transparent';
                    resendButton.style.borderColor = '#667eea';
                    resendButton.style.color = '#667eea';
                }, 3000);
            }
        }

        // Event listeners
        document.getElementById('resend-button').addEventListener('click', resendVerification);

        // Start verification process
        verifyEmail();
    </script>
</body>
</html>
