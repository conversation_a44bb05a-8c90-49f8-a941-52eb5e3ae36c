/**
 * Accommodations Tab Module
 *
 * Handles rendering and functionality for the accommodations tab using the known
 * consistent data structure from get_trip_plan_json.
 */

/**
 * Generate star rating HTML
 * @param {number} rating - Rating value (0-5)
 * @returns {string} HTML string for star rating
 */
function generateStarRating(rating) {
  const validRating = Math.max(0, Math.min(5, parseFloat(rating) || 0));
  const fullStars = Math.floor(validRating);
  const hasHalfStar = validRating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  let stars = '';
  for (let i = 0; i < fullStars; i++) {
    stars += '<i class="fas fa-star"></i>';
  }
  if (hasHalfStar) {
    stars += '<i class="fas fa-star-half-alt"></i>';
  }
  for (let i = 0; i < emptyStars; i++) {
    stars += '<i class="far fa-star"></i>';
  }

  return stars;
}

/**
 * Render amenities HTML using the known data structure
 * @param {Array} amenities - Array of amenity objects with name and icon properties
 * @returns {string} HTML string for amenities
 */
function renderAmenities(amenities) {
  if (!amenities || amenities.length === 0) {
    return `
      <div class="amenity-tag">
        <i class="fas fa-wifi"></i>
        <span>Free WiFi</span>
      </div>
    `;
  }

  return amenities.slice(0, 8).map(amenity => `
    <div class="amenity-tag">
      <i class="fas ${amenity.icon || 'fa-check'}"></i>
      <span>${amenity.name || amenity}</span>
    </div>
  `).join('');
}





/**
 * Render accommodations tab using the known consistent data structure
 * @param {Object} dayData - Day data from trip plan
 * @param {Object} AppState - Global application state
 * @returns {Promise<void>}
 */
async function renderAccommodationsTab(dayData, AppState) {
  console.log('🏨 Rendering accommodations tab for day', dayData?.day_number || 'unknown');
  console.log('🏨 Day data structure:', dayData);

  const accommodationsTab = document.getElementById('accommodations-tab');
  if (!accommodationsTab) {
    console.error('❌ Accommodations tab element not found in DOM');
    return;
  }

  try {
    // Check for lodging data in the known location
    const lodging = dayData.lodging;

    if (!lodging) {
      console.log('⚠️ No lodging data found in dayData.lodging');
      console.log('🔍 Available day data fields:', Object.keys(dayData || {}));

      accommodationsTab.innerHTML = `
        <div class="no-data-message">
          <i class="fas fa-bed"></i>
          <h3>No accommodation data</h3>
          <p>No accommodation information available for this day.</p>
          <div class="debug-info" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
            <strong>Debug Info:</strong> Available fields: ${Object.keys(dayData || {}).join(', ')}
          </div>
        </div>
      `;
      return;
    }

    console.log('✅ Lodging data found:', lodging);

    // Extract data directly from the known structure
    const name = lodging.name || 'Accommodation';
    const rating = lodging.rating?.score ? lodging.rating.score / 2 : (lodging.rating?.stars || 4.5); // Convert 10-point to 5-point scale
    const location = lodging.location?.address || 'Location not specified';
    const pricePerNight = lodging.pricing?.per_night || 180;
    const currency = lodging.pricing?.currency || 'USD';
    const primaryImage = lodging.images?.[0]?.url || 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800';
    const amenities = lodging.amenities || [];
    const phone = lodging.contact?.phone || '';
    const email = lodging.contact?.email || '';

    console.log('📊 Extracted accommodation data:', {
      name, rating, location, pricePerNight, currency, imageCount: lodging.images?.length || 0, amenitiesCount: amenities.length
    });

    // Generate star rating HTML
    const starRating = generateStarRating(rating);

    // Generate amenities HTML
    const amenitiesHtml = renderAmenities(amenities);

    // Load reviews if accommodation ID is available
    let reviews = [];
    if (lodging.id && AppState.dataManager) {
      try {
        reviews = await AppState.dataManager.loadAccommodationReviews(lodging.id);
        console.log(`✅ Loaded ${reviews.length} reviews for accommodation`);
      } catch (reviewError) {
        console.warn('⚠️ Failed to load accommodation reviews:', reviewError);
      }
    }

    // Generate reviews HTML with enhanced error handling
    const reviewsHtml = reviews.length > 0 ? reviews.slice(0, 3).map(review => `
      <div class="review-item">
        <div class="review-header">
          <span class="reviewer-name">${review.reviewerName || 'Anonymous'}</span>
          <div class="review-rating">
            ${generateStarRating(review.rating || 4)}
          </div>
        </div>
        <p class="review-text">${(review.text || '').length > 150 ? (review.text || '').substring(0, 150) + '...' : (review.text || 'No review text available')}</p>
      </div>
    `).join('') : `
      <div class="no-reviews">
        <p>No reviews available for this accommodation.</p>
      </div>
    `;

    // Get room type information
    const roomType = lodging.type || 'Hotel';

    // Format pricing
    const priceFormatted = currency === 'USD' ? `$${pricePerNight}` : `${currency} ${pricePerNight}`;

    // Render enhanced accommodation card with the correct data structure
    accommodationsTab.innerHTML = `
      <div class="accommodation-card">
        <div class="accommodation-image">
          <img src="${primaryImage}"
               alt="${name}"
               onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800';"
               loading="lazy">
          <div class="accommodation-badge">Primary Choice</div>
          ${lodging.images && lodging.images.length > 1 ? `<div class="image-count">${lodging.images.length} photos</div>` : ''}
        </div>
        <div class="accommodation-content">
          <div class="accommodation-header">
            <h3>${name}</h3>
            <div class="accommodation-rating">
              <div class="stars">
                ${starRating}
              </div>
              <span class="rating-score">${rating.toFixed(1)}</span>
            </div>
          </div>

          <div class="accommodation-details">
            <div class="detail-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>${location}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-bed"></i>
              <span>${roomType}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-users"></i>
              <span>2 Guests</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-calendar"></i>
              <span>Check-in - Check-out</span>
            </div>
            ${phone ? `
            <div class="detail-item">
              <i class="fas fa-phone"></i>
              <span>${phone}</span>
            </div>
            ` : ''}
            ${email ? `
            <div class="detail-item">
              <i class="fas fa-envelope"></i>
              <span>${email}</span>
            </div>
            ` : ''}
          </div>

          <div class="accommodation-amenities">
            <h4>Amenities</h4>
            <div class="amenities-grid">
              ${amenitiesHtml}
            </div>
          </div>

          ${reviews.length > 0 ? `
          <div class="accommodation-reviews">
            <h4>Guest Reviews (${reviews.length})</h4>
            <div class="reviews-container">
              ${reviewsHtml}
            </div>
          </div>
          ` : ''}

          <div class="accommodation-pricing">
            <div class="price-info">
              <span class="price">${priceFormatted}</span>
              <span class="period">/ night</span>
            </div>
            <div class="accommodation-actions">
              <button class="btn btn-outline" onclick="showAccommodationDetails('${lodging.id || ''}')">
                <i class="fas fa-info-circle"></i>
                View Details
              </button>
              <button class="btn btn-primary" onclick="changeAccommodation('${lodging.id || ''}')">
                <i class="fas fa-exchange-alt"></i>
                Change Hotel
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    console.log(`✅ Successfully rendered accommodation: ${name}`);
    console.log(`📊 Accommodation details: Rating ${rating}, ${amenities.length} amenities, ${reviews.length} reviews`);

  } catch (error) {
    console.error('❌ Error rendering accommodations tab:', error);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Day data that caused error:', dayData);

    accommodationsTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading accommodation</h3>
        <p>Unable to load accommodation information. Please try again.</p>
        <div class="error-details" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
          <strong>Error:</strong> ${error.message}<br>
          <strong>Day:</strong> ${dayData?.day_number || 'unknown'}<br>
          <strong>Available fields:</strong> ${Object.keys(dayData || {}).join(', ')}
        </div>
      </div>
    `;
  }
}

/**
 * Show accommodation details (placeholder for future implementation)
 * @param {string} accommodationId - Accommodation ID
 */
function showAccommodationDetails(accommodationId) {
  console.log('🏨 Show details for accommodation:', accommodationId);
  // TODO: Implement accommodation details modal
}

/**
 * Change accommodation (placeholder for future implementation)
 * @param {string} accommodationId - Current accommodation ID
 */
function changeAccommodation(accommodationId) {
  console.log('🏨 Change accommodation from:', accommodationId);
  // TODO: Implement accommodation change flow
}

// Make functions available globally for onclick handlers
if (typeof window !== 'undefined') {
  window.showAccommodationDetails = showAccommodationDetails;
  window.changeAccommodation = changeAccommodation;
}

export default renderAccommodationsTab;
