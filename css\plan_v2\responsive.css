/* ===== RESPONSIVE DESIGN ===== */

/* ===== MOBILE FIRST APPROACH ===== */

/* Base styles are mobile-first, then we enhance for larger screens */

/* ===== TABLET STYLES (768px and up) ===== */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-6);
  }
  
  .trip-header-content {
    gap: var(--spacing-8);
  }
  
  .trip-actions {
    gap: var(--spacing-4);
  }
  
  .overview-grid {
    gap: var(--spacing-6);
  }
  
  .day-selector {
    gap: var(--spacing-4);
  }
  
  .day-tabs {
    padding: var(--spacing-2);
  }
  
  .day-tab {
    padding: var(--spacing-4) var(--spacing-6);
  }
}

/* ===== MOBILE STYLES (767px and below) ===== */
@media (max-width: 767px) {
  /* Container adjustments */
  .container {
    padding: 0 var(--spacing-4);
  }

  /* Header mobile styles */
  .header-content {
    padding: var(--spacing-4) 0;
  }

  .logo {
    font-size: var(--font-size-lg);
  }

  .logo i {
    font-size: var(--font-size-xl);
  }

  .main-nav {
    gap: var(--spacing-4);
  }

  .nav-link span {
    display: none;
  }
  
  /* Trip header mobile styles */
  .trip-header {
    padding: var(--spacing-8) 0;
  }

  .trip-header-content {
    flex-direction: column;
    gap: var(--spacing-6);
    text-align: center;
  }

  .trip-title {
    font-size: var(--font-size-3xl);
    padding: var(--spacing-3);
  }

  .trip-meta {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .trip-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-3);
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }

  .trip-actions .btn {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
  }
  
  /* Overview section mobile styles */
  .overview-section {
    padding: var(--spacing-6) 0;
  }

  /* Grid is already mobile-first in layout.css */
  
  /* Day navigation mobile styles */
  .day-navigation-section {
    padding: var(--spacing-6) 0;
  }

  .day-navigation-header {
    flex-direction: column;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }

  .current-day-info {
    order: -1;
  }

  .day-title {
    font-size: var(--font-size-xl);
  }

  .day-subtitle {
    font-size: var(--font-size-base);
  }

  .navigation-controls {
    order: 1;
    justify-content: center;
  }

  .day-actions {
    order: 2;
    justify-content: center;
  }

  .day-selector {
    gap: var(--spacing-3);
    padding: var(--spacing-2) 0;
    justify-content: flex-start;
  }

  .day-card {
    min-width: 70px;
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
  }

  .day-number {
    font-size: var(--font-size-lg);
  }

  .day-date {
    font-size: var(--font-size-xs);
  }
  
  /* Day content mobile styles */
  .day-content-section {
    padding: var(--spacing-6) 0;
  }

  .day-tabs {
    padding: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    border-radius: var(--radius-xl);
  }

  .day-tabs::-webkit-scrollbar {
    display: none;
  }

  .day-tab {
    padding: var(--spacing-3) var(--spacing-4);
    white-space: nowrap;
    flex: none;
    min-width: 120px;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
  }

  .day-tab span {
    display: inline;
  }

  .day-tab i {
    font-size: var(--font-size-sm);
  }

  .tab-pane {
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
  }
  
  /* Card mobile styles */
  .accommodation-card {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .accommodation-image {
    height: 200px;
  }
  
  .accommodation-content {
    padding: var(--spacing-4);
  }
  
  .accommodation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .accommodation-details {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
  
  .accommodation-pricing {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .accommodation-actions {
    width: 100%;
  }
  
  .accommodation-actions .btn {
    flex: 1;
  }
  
  /* Restaurant cards mobile styles */
  .restaurants-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .restaurant-card {
    margin-bottom: var(--spacing-4);
  }
  
  .restaurant-image {
    height: 150px;
  }
  
  .restaurant-content {
    padding: var(--spacing-3);
  }
  
  .restaurant-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }
  
  .restaurant-pricing {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
  }
  
  .restaurant-actions {
    width: 100%;
  }
  
  .restaurant-actions .btn {
    flex: 1;
  }
  
  /* Activity card mobile styles */
  .activity-card {
    margin-bottom: var(--spacing-4);
  }

  .activity-content {
    padding: var(--spacing-4);
  }
  
  .activity-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .activity-details {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
  
  .activity-pricing {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .activity-actions {
    width: 100%;
  }
  
  .activity-actions .btn {
    flex: 1;
  }
  
  /* Footer mobile styles */
  .footer {
    padding: var(--spacing-8) 0 var(--spacing-4);
    margin-top: var(--spacing-8);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
  
  .social-links {
    justify-content: center;
  }
}

/* ===== SMALL MOBILE STYLES (480px and below) ===== */
@media (max-width: 480px) {
  /* Even smaller adjustments for very small screens */
  .container {
    padding: 0 var(--spacing-3);
  }

  .trip-title {
    font-size: var(--font-size-2xl);
    padding: var(--spacing-2);
  }

  .day-title {
    font-size: var(--font-size-lg);
  }

  .card-header h2 {
    font-size: var(--font-size-lg);
  }

  .accommodation-header h3,
  .activity-header h3 {
    font-size: var(--font-size-xl);
  }

  .restaurant-header h4 {
    font-size: var(--font-size-base);
  }

  .btn {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
  }

  .day-card {
    min-width: 60px;
    padding: var(--spacing-3);
  }

  .day-number {
    font-size: var(--font-size-base);
  }

  .tab-pane {
    padding: var(--spacing-4);
  }

  .day-tabs {
    padding: var(--spacing-1);
  }

  .day-tab {
    padding: var(--spacing-2) var(--spacing-3);
    min-width: 100px;
  }

  /* Transport mobile styles */
  .transport-header {
    padding: var(--spacing-4);
  }

  .transport-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .transport-segments {
    padding: var(--spacing-4);
    gap: var(--spacing-3);
  }

  .transport-route {
    padding: var(--spacing-3);
  }

  .route-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }

  .route-stats {
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .transport-option {
    padding: var(--spacing-3);
  }

  .option-header {
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .option-details {
    margin-left: 0;
    margin-top: var(--spacing-2);
  }

  .inter-city-header {
    padding: var(--spacing-4);
  }

  .inter-city-card {
    margin: var(--spacing-4);
  }

  .inter-city-route {
    padding: var(--spacing-3);
  }
}

/* ===== LARGE DESKTOP STYLES (1200px and up) ===== */
@media (min-width: 1200px) {
  .container {
    padding: 0 var(--spacing-8);
  }
  
  .overview-grid {
    gap: var(--spacing-10);
  }
  
  .day-selector {
    gap: var(--spacing-4);
    justify-content: center;
  }
  
  .day-card {
    min-width: 100px;
    padding: var(--spacing-5);
  }
  
  .restaurants-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .timeline {
    max-width: 900px;
  }
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for mobile devices */
  .btn {
    min-height: 44px;
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .day-card {
    min-height: 80px;
    padding: var(--spacing-4);
  }
  
  .day-tab {
    min-height: 44px;
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .nav-btn {
    min-width: 44px;
    min-height: 44px;
    padding: var(--spacing-3);
  }
  
  .theme-toggle {
    min-width: 44px;
    min-height: 44px;
    padding: var(--spacing-3);
  }
  
  /* Remove hover effects on touch devices */
  .card:hover,
  .restaurant-card:hover,
  .day-card:hover,
  .timeline-content:hover {
    transform: none;
    box-shadow: var(--shadow-sm);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .footer,
  .trip-actions,
  .day-actions,
  .accommodation-actions,
  .restaurant-actions,
  .activity-actions,
  .transport-actions,
  .day-tabs,
  .navigation-controls {
    display: none !important;
  }
  
  .trip-header {
    background: none !important;
    color: var(--gray-900) !important;
    padding: var(--spacing-4) 0;
  }
  
  .card,
  .accommodation-card,
  .restaurant-card,
  .activity-card,
  .transport-card {
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
    break-inside: avoid;
  }
  
  .tab-pane {
    display: block !important;
    page-break-before: always;
  }
  
  .tab-pane:first-child {
    page-break-before: auto;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .loading-spinner {
    animation: none;
  }
  
  .timeline-item {
    animation: none;
    opacity: 1;
    transform: none;
  }
}
