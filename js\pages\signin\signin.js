// This script handles the functionality of the sign-in and sign-up forms, including theme toggling, form switching, password strength checking, and form submission.
import { signInWithEmail, signUpWithEmail, signInWithSocial, resetPassword, resendVerificationEmail } from '../../core/auth.js';

document.addEventListener('DOMContentLoaded', function() {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;

    function setTheme(theme) {
      body.setAttribute('data-theme', theme);
      themeToggle.innerHTML = `<i class="fas fa-${theme === 'dark' ? 'sun' : 'moon'}"></i>`;
      localStorage.setItem('theme', theme);
    }

    // Load saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);

    themeToggle.addEventListener('click', () => {
      const currentTheme = body.getAttribute('data-theme');
      setTheme(currentTheme === 'dark' ? 'light' : 'dark');
    });

    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const navMenu = document.getElementById('nav-menu');

    mobileMenuButton.addEventListener('click', () => {
      navMenu.classList.toggle('show');
    });

    // Form switching - Fixed the variable redeclaration issue
    const tabs = document.querySelectorAll('.tab');
    const formElements = {
      signin: document.getElementById('signin-form'),
      signup: document.getElementById('signup-form')
    };

    function switchForm(formId) {
      // Update tabs
      tabs.forEach(tab => {
        tab.classList.toggle('active', tab.dataset.tab === formId);
      });

      // Switch forms with fade animation
      Object.entries(formElements).forEach(([id, form]) => {
        if (id === formId) {
          form.style.display = 'block';
          form.style.opacity = '0';
          setTimeout(() => form.style.opacity = '1', 50);
        } else {
          form.style.opacity = '0';
          setTimeout(() => form.style.display = 'none', 300);
        }
      });
    }

    // Tab click handlers
    tabs.forEach(tab => {
      tab.addEventListener('click', () => switchForm(tab.dataset.tab));
    });

    // Form toggle links - Fixed by using a new class
    document.querySelectorAll('.form-toggle').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        switchForm(link.dataset.form);
      });
    });

    // Password visibility toggle - Fix to ensure proper functionality
    document.querySelectorAll('.password-toggle').forEach(toggle => {
      toggle.addEventListener('click', function() {
        const input = this.parentElement.querySelector('input');
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        this.innerHTML = `<i class="fas fa-${type === 'password' ? 'eye' : 'eye-slash'}"></i>`;
      });
    });

    // Password strength checker
    const passwordInput = document.getElementById('signup-password');
    const strengthBars = document.querySelectorAll('.strength-bar');
    const strengthText = document.querySelector('.strength-text span');
    const strengthContainer = document.querySelector('.strength-bars');

    function checkPasswordStrength(password) {
      let score = 0;

      // Length check
      if (password.length >= 8) score++;
      if (password.length >= 12) score++;

      // Character variety checks
      if (/[A-Z]/.test(password)) score++;
      if (/[0-9]/.test(password)) score++;
      if (/[^A-Za-z0-9]/.test(password)) score++;

      return {
        score: Math.min(score, 3),
        label: score <= 1 ? 'weak' : score === 2 ? 'medium' : 'strong'
      };
    }

    passwordInput.addEventListener('input', function() {
      const { score, label } = checkPasswordStrength(this.value);

      // Remove previous classes
      strengthContainer.classList.remove('strength-weak', 'strength-medium', 'strength-strong');

      // Add appropriate class
      if (this.value) {
        strengthContainer.classList.add(`strength-${label}`);
      }

      // Update bars
      strengthBars.forEach((bar, index) => {
        bar.style.backgroundColor = index < score
          ? score === 1 ? '#EF4444' : score === 2 ? '#F59E0B' : '#10B981'
          : 'var(--border-color)';
      });

      strengthText.textContent = this.value ? label : 'weak';
    });

    // Form validation and submission
    const signinForm = document.getElementById('signin-form');
    const signupForm = document.getElementById('signup-form');

    // Sign in form submission
    signinForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const submitButton = signinForm.querySelector('.submit-button');
      const spinner = submitButton.querySelector('.spinner');
      const emailInput = document.getElementById('signin-email');
      const passwordInput = document.getElementById('signin-password');

      // Clear previous errors
      clearFormErrors(signinForm);

      // Basic validation
      if (!validateForm(signinForm)) return;

      // Show loading state
      setLoadingState(submitButton, spinner, true);

      try {
        const result = await signInWithEmail(emailInput.value, passwordInput.value);

        if (result.success) {
          // Check for redirect after login
          const redirectAfterLogin = localStorage.getItem('redirectAfterLogin');

          if (redirectAfterLogin === 'wizard') {
            localStorage.removeItem('redirectAfterLogin');
            window.location.href = '../pages/wizard.html';
          } else {
            // Default redirect to homepage
            window.location.href = '../index.html';
          }
        } else {
          // Handle email verification errors
          if (result.error.includes('email not confirmed') || result.error.includes('Email not confirmed')) {
            showEmailVerificationError(signinForm, emailInput.value);
          } else {
            showFormError(signinForm, result.error);
          }
        }
      } catch (error) {
        console.error('Sign in error:', error);
        showFormError(signinForm, 'An unexpected error occurred. Please try again.');
      } finally {
        setLoadingState(submitButton, spinner, false);
      }
    });

    // Sign up form submission
    signupForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const submitButton = signupForm.querySelector('.submit-button');
      const spinner = submitButton.querySelector('.spinner');
      const nameInput = document.getElementById('signup-name');
      const emailInput = document.getElementById('signup-email');
      const passwordInput = document.getElementById('signup-password');

      // Clear previous errors
      clearFormErrors(signupForm);

      // Basic validation
      if (!validateForm(signupForm)) return;

      // Show loading state
      setLoadingState(submitButton, spinner, true);

      try {
        const result = await signUpWithEmail(emailInput.value, passwordInput.value, nameInput.value);

        if (result.success) {
          if (result.needsConfirmation) {
            showFormSuccess(signupForm, 'Please check your email to confirm your account.');
          } else {
            // Check for redirect after login
            const redirectAfterLogin = localStorage.getItem('redirectAfterLogin');

            if (redirectAfterLogin === 'wizard') {
              localStorage.removeItem('redirectAfterLogin');
              window.location.href = '../pages/wizard.html';
            } else {
              // Default redirect to homepage
              window.location.href = '../index.html';
            }
          }
        } else {
          showFormError(signupForm, result.error);
        }
      } catch (error) {
        console.error('Sign up error:', error);
        showFormError(signupForm, 'An unexpected error occurred. Please try again.');
      } finally {
        setLoadingState(submitButton, spinner, false);
      }
    });

    // Terms acceptance toggle
    const termsCheckbox = document.getElementById('terms-acceptance');
    const signupButton = document.querySelector('#signup-form .submit-button');

    termsCheckbox.addEventListener('change', function() {
      signupButton.disabled = !this.checked;
    });

    // Social login handlers - hide buttons if providers are disabled
    document.querySelectorAll('.social-button').forEach(button => {
      const provider = button.classList[1]; // google, facebook, github

      // Hide social buttons since OAuth providers are currently disabled
      // This prevents users from seeing non-functional buttons
      button.style.display = 'none';

      button.addEventListener('click', async () => {
        button.disabled = true;

        try {
          const result = await signInWithSocial(provider);

          if (!result.success) {
            showFormError(document.querySelector('.auth-form:not([style*="display: none"])'), result.error);
          }
          // Note: For OAuth, the redirect happens automatically
        } catch (error) {
          console.error('Social login error:', error);
          showFormError(document.querySelector('.auth-form:not([style*="display: none"])'), 'Social login failed. Please try again.');
        } finally {
          button.disabled = false;
        }
      });
    });

    // Hide social login sections if no providers are available
    const socialSections = document.querySelectorAll('.social-login, .social-buttons');
    socialSections.forEach(section => {
      const visibleButtons = section.querySelectorAll('.social-button:not([style*="display: none"])');
      if (visibleButtons.length === 0) {
        section.style.display = 'none';
      }
    });

    // Forgot password handler
    document.getElementById('forgot-password').addEventListener('click', async (e) => {
      e.preventDefault();

      const email = document.getElementById('signin-email').value;

      if (!email) {
        showFormError(signinForm, 'Please enter your email address first.');
        return;
      }

      try {
        const result = await resetPassword(email);

        if (result.success) {
          showFormSuccess(signinForm, 'Password reset email sent. Please check your inbox.');
        } else {
          showFormError(signinForm, result.error);
        }
      } catch (error) {
        console.error('Password reset error:', error);
        showFormError(signinForm, 'Failed to send reset email. Please try again.');
      }
    });

    // Add form validation in real-time
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
      input.addEventListener('blur', function() {
        validateInput(this);
      });
    });

    function validateInput(input) {
      const errorElement = document.getElementById(`${input.id}-error`);

      if (!errorElement) return;

      // Email validation
      if (input.type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!input.value) {
          errorElement.textContent = 'Email is required';
          errorElement.style.display = 'block';
        } else if (!emailRegex.test(input.value)) {
          errorElement.textContent = 'Please enter a valid email address';
          errorElement.style.display = 'block';
        } else {
          errorElement.style.display = 'none';
        }
      }

      // Name validation
      if (input.id === 'signup-name') {
        if (!input.value) {
          errorElement.textContent = 'Name is required';
          errorElement.style.display = 'block';
        } else if (input.value.length < 2) {
          errorElement.textContent = 'Name is too short';
          errorElement.style.display = 'block';
        } else {
          errorElement.style.display = 'none';
        }
      }

      // Password validation
      if (input.type === 'password') {
        if (!input.value) {
          errorElement.textContent = 'Password is required';
          errorElement.style.display = 'block';
        } else if (input.value.length < 6) {
          errorElement.textContent = 'Password must be at least 6 characters';
          errorElement.style.display = 'block';
        } else {
          errorElement.style.display = 'none';
        }
      }
    }

    // Helper functions for form handling
    function validateForm(form) {
      let isValid = true;

      // Validate email
      const emailInput = form.querySelector('input[type="email"]');
      if (emailInput) {
        const emailError = document.getElementById(`${emailInput.id}-error`);
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailInput.value) {
          emailError.textContent = 'Email is required';
          emailError.style.display = 'block';
          isValid = false;
        } else if (!emailRegex.test(emailInput.value)) {
          emailError.textContent = 'Please enter a valid email address';
          emailError.style.display = 'block';
          isValid = false;
        } else {
          emailError.style.display = 'none';
        }
      }

      // Validate password
      const passwordInput = form.querySelector('input[type="password"]');
      if (passwordInput) {
        const passwordError = document.getElementById(`${passwordInput.id}-error`);
        if (!passwordInput.value || passwordInput.value.length < 6) {
          passwordError.textContent = 'Password must be at least 6 characters';
          passwordError.style.display = 'block';
          isValid = false;
        } else {
          passwordError.style.display = 'none';
        }
      }

      // Validate name (for signup)
      const nameInput = form.querySelector('#signup-name');
      if (nameInput) {
        const nameError = document.getElementById(`${nameInput.id}-error`);
        if (!nameInput.value || nameInput.value.length < 2) {
          nameError.textContent = 'Name must be at least 2 characters';
          nameError.style.display = 'block';
          isValid = false;
        } else {
          nameError.style.display = 'none';
        }
      }

      return isValid;
    }

    function clearFormErrors(form) {
      const errorElements = form.querySelectorAll('.error-message');
      errorElements.forEach(error => {
        error.style.display = 'none';
        error.textContent = '';
      });

      // Remove any general error/success messages
      const existingMessages = form.querySelectorAll('.form-message');
      existingMessages.forEach(msg => msg.remove());
    }

    function showFormError(form, message) {
      clearFormErrors(form);

      const errorDiv = document.createElement('div');
      errorDiv.className = 'form-message error-message';
      errorDiv.style.cssText = 'display: block; color: #ef4444; text-align: center; margin-top: 1rem; padding: 0.75rem; background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.375rem;';
      errorDiv.textContent = message;

      form.appendChild(errorDiv);
    }

    function showFormSuccess(form, message) {
      clearFormErrors(form);

      const successDiv = document.createElement('div');
      successDiv.className = 'form-message success-message';
      successDiv.style.cssText = 'display: block; color: #10b981; text-align: center; margin-top: 1rem; padding: 0.75rem; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 0.375rem;';
      successDiv.textContent = message;

      form.appendChild(successDiv);
    }

    function setLoadingState(button, spinner, isLoading) {
      if (isLoading) {
        button.disabled = true;
        button.style.color = 'transparent';
        spinner.style.display = 'block';
      } else {
        button.disabled = false;
        button.style.color = 'white';
        spinner.style.display = 'none';
      }
    }

    function showEmailVerificationError(form, email) {
      clearFormErrors(form);

      const errorDiv = document.createElement('div');
      errorDiv.className = 'form-message verification-error';
      errorDiv.style.cssText = 'display: block; color: #f59e0b; text-align: center; margin-top: 1rem; padding: 0.75rem; background: #fef3c7; border: 1px solid #fbbf24; border-radius: 0.375rem;';

      errorDiv.innerHTML = `
        <div style="margin-bottom: 10px;">
          <i class="fas fa-envelope" style="margin-right: 8px;"></i>
          <strong>Email verification required</strong>
        </div>
        <div style="margin-bottom: 15px; font-size: 14px;">
          Please check your email and click the verification link to activate your account.
        </div>
        <button id="resend-verification" style="background: #f59e0b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
          <i class="fas fa-paper-plane" style="margin-right: 5px;"></i>
          Resend Verification Email
        </button>
      `;

      form.appendChild(errorDiv);

      // Add resend functionality
      document.getElementById('resend-verification').addEventListener('click', async function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 5px;"></i>Sending...';

        try {
          const result = await resendVerificationEmail(email);

          if (result.success) {
            this.innerHTML = '<i class="fas fa-check" style="margin-right: 5px;"></i>Email Sent!';
            this.style.background = '#10b981';

            setTimeout(() => {
              this.disabled = false;
              this.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 5px;"></i>Resend Verification Email';
              this.style.background = '#f59e0b';
            }, 3000);
          } else {
            throw new Error(result.error);
          }
        } catch (error) {
          console.error('Resend error:', error);
          this.innerHTML = '<i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>Failed to Send';
          this.style.background = '#ef4444';

          setTimeout(() => {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 5px;"></i>Resend Verification Email';
            this.style.background = '#f59e0b';
          }, 3000);
        }
      });
    }
  });