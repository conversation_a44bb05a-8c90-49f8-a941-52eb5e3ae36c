/**
 * Weather Component - Phase 2 Implementation
 * 
 * This component will handle weather display and integrate with the existing
 * weather-service.js that is working correctly and should be preserved.
 */

/**
 * Weather Component Class
 * 
 * Responsibilities:
 * - Render current weather
 * - Display weather forecast
 * - Handle weather data updates
 * - Show weather alerts
 * - Integrate with existing weather-service.js
 */
export class WeatherComponent {
  constructor(container, stateManager) {
    this.container = container;
    this.stateManager = stateManager;
    this.elements = {};
    this.unsubscribers = [];
    this.weatherService = null; // Will be imported from existing service
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  async initialize() {
    console.log('🌤️ WeatherComponent: Initializing...');
    
    this.cacheElements();
    this.setupEventListeners();
    this.subscribeToState();
    
    // Phase 2: Import existing weather service
    // this.weatherService = await import('../services/weather-service.js');
    
    console.log('🌤️ WeatherComponent: Initialized');
  }

  /**
   * Cache DOM elements
   */
  cacheElements() {
    this.elements = {
      location: this.container.querySelector('.weather-location span'),
      currentIcon: this.container.querySelector('.weather-icon i'),
      temperature: this.container.querySelector('.temp'),
      condition: this.container.querySelector('.condition'),
      humidity: this.container.querySelector('.detail-item:nth-child(1) span'),
      wind: this.container.querySelector('.detail-item:nth-child(2) span'),
      visibility: this.container.querySelector('.detail-item:nth-child(3) span'),
      forecastDays: this.container.querySelectorAll('.forecast-day'),
      loadingState: this.container.querySelector('.weather-loading'),
      errorState: this.container.querySelector('.weather-error'),
      retryButton: this.container.querySelector('.weather-retry')
    };
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Retry button
    if (this.elements.retryButton) {
      this.elements.retryButton.addEventListener('click', () => {
        this.refreshWeatherData();
      });
    }

    // Unit toggle (if exists)
    const unitToggles = this.container.querySelectorAll('.unit-toggle');
    unitToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        const unit = toggle.dataset.unit;
        this.handleUnitChange(unit);
      });
    });
  }

  /**
   * Subscribe to state changes
   */
  subscribeToState() {
    // Subscribe to weather data changes
    const unsubscribe = this.stateManager.subscribe('data.weatherData', (weatherData) => {
      this.render(weatherData);
    });

    this.unsubscribers.push(unsubscribe);

    // Subscribe to location changes
    const locationUnsubscribe = this.stateManager.subscribe('trip.currentLocation', (location) => {
      this.handleLocationChange(location);
    });

    this.unsubscribers.push(locationUnsubscribe);
  }

  /**
   * Load weather data directly from trip data (for direct usage without state manager)
   * @param {Object} tripData - Complete trip data from get_trip_plan_json
   * @param {number} currentDay - Current day number
   */
  async loadFromTripData(tripData, currentDay) {
    if (!tripData) {
      console.warn('🌤️ WeatherComponent: No trip data available');
      this.showErrorState('No trip data available');
      return;
    }

    try {
      console.log(`🌤️ WeatherComponent: Loading weather for day ${currentDay}`);
      this.showLoadingState();

      // Import weather service functions
      const { getWeatherFromTripData } = await import('../services/weather-service.js');

      // Get weather data for the current day
      const weatherData = await getWeatherFromTripData(tripData, currentDay, 3);

      if (!weatherData || weatherData.length === 0) {
        throw new Error('No weather data received');
      }

      // Transform weather data to match component expectations
      const transformedData = this.transformWeatherDataForDisplay(weatherData, tripData, currentDay);

      // Render the weather data
      this.renderWeatherData(transformedData);

      console.log('✅ WeatherComponent: Weather data loaded successfully');

    } catch (error) {
      console.error('❌ WeatherComponent: Failed to load weather from trip data:', error);
      this.showErrorState(error.message);
    }
  }

  /**
   * Transform weather service data for component display
   * @param {Array} weatherData - Weather data from service
   * @param {Object} tripData - Trip data for location info
   * @param {number} currentDay - Current day number
   * @returns {Object} Transformed data for display
   */
  transformWeatherDataForDisplay(weatherData, tripData, currentDay) {
    try {
      // Get current day data for location
      const dayData = tripData.days?.find(day => day.day_number === currentDay);
      const locationName = dayData?.location?.city || dayData?.city_name || 'Unknown Location';

      // Current weather (first day in forecast)
      const currentWeather = weatherData[0] || {};

      // Forecast (all days)
      const forecast = weatherData.map((day, index) => ({
        day: index === 0 ? 'Today' : index === 1 ? 'Tomorrow' : day.date,
        condition: day.condition || 'Unknown',
        high: day.temperature ? day.temperature.replace('°C', '') : 'N/A',
        low: day.temperature ? day.temperature.replace('°C', '') : 'N/A', // Using same temp for both for now
        icon: day.icon || 'fa-question'
      }));

      return {
        current: {
          location: locationName,
          temperature: currentWeather.temperature || 'N/A',
          condition: currentWeather.condition || 'Unknown',
          humidity: currentWeather.humidity || 'N/A',
          wind: currentWeather.wind || 'N/A',
          sunrise: currentWeather.sunrise || 'N/A',
          sunset: currentWeather.sunset || 'N/A',
          icon: currentWeather.icon || 'fa-question'
        },
        forecast: forecast,
        alerts: [] // No alerts for now
      };

    } catch (error) {
      console.error('❌ Error transforming weather data:', error);
      return {
        current: {
          location: 'Unknown Location',
          temperature: 'N/A',
          condition: 'Unknown',
          humidity: 'N/A',
          wind: 'N/A',
          sunrise: 'N/A',
          sunset: 'N/A',
          icon: 'fa-question'
        },
        forecast: [],
        alerts: []
      };
    }
  }

  /**
   * Render weather data
   * @param {Object} weatherData - Weather data from state
   */
  render(weatherData) {
    if (!weatherData) {
      this.showLoadingState();
      return;
    }

    if (weatherData.error) {
      this.showErrorState(weatherData.error);
      return;
    }

    console.log('🌤️ WeatherComponent: Rendering weather data');

    this.hideLoadingState();
    this.hideErrorState();

    this.renderCurrentWeather(weatherData.current);
    this.renderForecast(weatherData.forecast);
    this.renderWeatherAlerts(weatherData.alerts);
  }

  /**
   * Render weather data directly (used by loadFromTripData)
   * @param {Object} weatherData - Transformed weather data
   */
  renderWeatherData(weatherData) {
    if (!weatherData) {
      this.showErrorState('No weather data available');
      return;
    }

    console.log('🌤️ WeatherComponent: Rendering weather data directly');

    this.hideLoadingState();
    this.hideErrorState();

    this.renderCurrentWeatherDirect(weatherData.current);
    this.renderForecastDirect(weatherData.forecast);
  }

  /**
   * Render current weather
   * @param {Object} current - Current weather data
   */
  renderCurrentWeather(current) {
    if (!current) return;

    // Update location
    if (this.elements.location) {
      this.elements.location.textContent = current.location;
    }

    // Update temperature
    if (this.elements.temperature) {
      this.elements.temperature.textContent = `${current.temperature}°C`;
    }

    // Update condition
    if (this.elements.condition) {
      this.elements.condition.textContent = current.condition;
    }

    // Update weather icon
    if (this.elements.currentIcon) {
      this.elements.currentIcon.className = this.getWeatherIcon(current.condition);
    }

    // Update details
    if (this.elements.humidity) {
      this.elements.humidity.textContent = `${current.humidity}%`;
    }

    if (this.elements.wind) {
      this.elements.wind.textContent = `${current.wind} km/h`;
    }

    if (this.elements.visibility) {
      this.elements.visibility.textContent = `${current.visibility} km`;
    }
  }

  /**
   * Render current weather directly using DOM selectors
   * @param {Object} current - Current weather data
   */
  renderCurrentWeatherDirect(current) {
    if (!current) return;

    try {
      // Update location
      const locationEl = document.querySelector('.weather-location span');
      if (locationEl) {
        locationEl.textContent = current.location;
      }

      // Update temperature
      const tempEl = document.querySelector('.weather-temp .temp');
      if (tempEl) {
        tempEl.textContent = current.temperature;
      }

      // Update condition
      const conditionEl = document.querySelector('.weather-temp .condition');
      if (conditionEl) {
        conditionEl.textContent = current.condition;
      }

      // Update weather icon
      const iconEl = document.querySelector('.weather-icon i');
      if (iconEl) {
        iconEl.className = this.getWeatherIcon(current.condition);
      }

      // Update details
      const detailItems = document.querySelectorAll('.weather-details .detail-item');
      detailItems.forEach(item => {
        const icon = item.querySelector('i');
        const span = item.querySelector('span');

        if (icon && span) {
          if (icon.classList.contains('fa-droplet')) {
            span.textContent = current.humidity;
          } else if (icon.classList.contains('fa-wind')) {
            span.textContent = current.wind;
          } else if (icon.classList.contains('fa-sunrise')) {
            span.textContent = current.sunrise;
          } else if (icon.classList.contains('fa-sunset')) {
            span.textContent = current.sunset;
          }
        }
      });

      console.log('✅ Current weather rendered successfully');

    } catch (error) {
      console.error('❌ Error rendering current weather:', error);
    }
  }

  /**
   * Render weather forecast
   * @param {Array} forecast - Forecast data
   */
  renderForecast(forecast) {
    if (!forecast || !this.elements.forecastDays) return;

    this.elements.forecastDays.forEach((dayEl, index) => {
      if (forecast[index]) {
        const day = forecast[index];

        const labelEl = dayEl.querySelector('.day-label');
        const iconEl = dayEl.querySelector('.day-icon i');
        const highEl = dayEl.querySelector('.high');
        const lowEl = dayEl.querySelector('.low');
        const conditionEl = dayEl.querySelector('.day-condition');

        if (labelEl) labelEl.textContent = day.day;
        if (iconEl) iconEl.className = this.getWeatherIcon(day.condition);
        if (highEl) highEl.textContent = `${day.high}°`;
        if (lowEl) lowEl.textContent = `${day.low}°`;
        if (conditionEl) conditionEl.textContent = day.condition;
      }
    });
  }

  /**
   * Render weather forecast directly using DOM selectors
   * @param {Array} forecast - Forecast data
   */
  renderForecastDirect(forecast) {
    if (!forecast || forecast.length === 0) return;

    try {
      const forecastDays = document.querySelectorAll('.weather-forecast .forecast-day');

      forecastDays.forEach((dayEl, index) => {
        // Skip today (index 0) and show tomorrow and day after
        const forecastIndex = index + 1;

        if (forecast[forecastIndex]) {
          const day = forecast[forecastIndex];

          const labelEl = dayEl.querySelector('.day-label');
          const iconEl = dayEl.querySelector('.day-icon i');
          const highEl = dayEl.querySelector('.high');
          const lowEl = dayEl.querySelector('.low');
          const conditionEl = dayEl.querySelector('.day-condition');

          if (labelEl) {
            labelEl.textContent = day.day;
          }
          if (iconEl) {
            iconEl.className = this.getWeatherIcon(day.condition);
          }
          if (highEl) {
            highEl.textContent = `${day.high}°`;
          }
          if (lowEl) {
            lowEl.textContent = `${day.low}°`;
          }
          if (conditionEl) {
            conditionEl.textContent = day.condition;
          }
        }
      });

      console.log('✅ Weather forecast rendered successfully');

    } catch (error) {
      console.error('❌ Error rendering weather forecast:', error);
    }
  }

  /**
   * Render weather alerts
   * @param {Array} alerts - Weather alerts
   */
  renderWeatherAlerts(alerts) {
    if (!alerts || alerts.length === 0) {
      this.hideWeatherAlerts();
      return;
    }

    // Phase 2: Implement weather alerts display
    console.log('🌤️ WeatherComponent: Rendering weather alerts:', alerts);
  }

  /**
   * Get weather icon class based on condition
   * @param {string} condition - Weather condition
   * @returns {string} Font Awesome icon class
   */
  getWeatherIcon(condition) {
    const iconMap = {
      'sunny': 'fas fa-sun',
      'clear': 'fas fa-sun',
      'partly cloudy': 'fas fa-cloud-sun',
      'cloudy': 'fas fa-cloud',
      'overcast': 'fas fa-cloud',
      'rainy': 'fas fa-cloud-rain',
      'rain': 'fas fa-cloud-rain',
      'stormy': 'fas fa-bolt',
      'snowy': 'fas fa-snowflake',
      'snow': 'fas fa-snowflake',
      'foggy': 'fas fa-smog',
      'fog': 'fas fa-smog'
    };

    const normalizedCondition = condition.toLowerCase();
    return iconMap[normalizedCondition] || 'fas fa-cloud';
  }

  /**
   * Show loading state
   */
  showLoadingState() {
    if (this.elements.loadingState) {
      this.elements.loadingState.classList.remove('hidden');
    }
    
    // Hide other states
    this.hideErrorState();
  }

  /**
   * Hide loading state
   */
  hideLoadingState() {
    if (this.elements.loadingState) {
      this.elements.loadingState.classList.add('hidden');
    }
  }

  /**
   * Show error state
   * @param {string} error - Error message
   */
  showErrorState(error) {
    if (this.elements.errorState) {
      this.elements.errorState.classList.remove('hidden');
      
      const errorMessage = this.elements.errorState.querySelector('p');
      if (errorMessage) {
        errorMessage.textContent = error || 'Failed to load weather data';
      }
    }
    
    // Hide other states
    this.hideLoadingState();
  }

  /**
   * Hide error state
   */
  hideErrorState() {
    if (this.elements.errorState) {
      this.elements.errorState.classList.add('hidden');
    }
  }

  /**
   * Show weather alerts
   */
  showWeatherAlerts() {
    // Phase 2: Implement weather alerts display
    console.log('🌤️ WeatherComponent: Showing weather alerts');
  }

  /**
   * Hide weather alerts
   */
  hideWeatherAlerts() {
    // Phase 2: Hide weather alerts
    console.log('🌤️ WeatherComponent: Hiding weather alerts');
  }

  /**
   * Handle location change
   * @param {string} location - New location
   */
  handleLocationChange(location) {
    console.log(`🌤️ WeatherComponent: Location changed to: ${location}`);
    this.refreshWeatherData();
  }

  /**
   * Handle unit change (Celsius/Fahrenheit)
   * @param {string} unit - Temperature unit
   */
  handleUnitChange(unit) {
    console.log(`🌤️ WeatherComponent: Unit changed to: ${unit}`);
    
    // Update state
    this.stateManager.setState('preferences.units', unit);
    
    // Re-render with new units
    const weatherData = this.stateManager.getState('data.weatherData');
    if (weatherData) {
      this.render(this.convertUnits(weatherData, unit));
    }
  }

  /**
   * Convert temperature units
   * @param {Object} weatherData - Weather data
   * @param {string} unit - Target unit
   * @returns {Object} Converted weather data
   */
  convertUnits(weatherData, unit) {
    // Phase 2: Implement unit conversion
    return weatherData;
  }

  /**
   * Refresh weather data
   */
  async refreshWeatherData() {
    console.log('🌤️ WeatherComponent: Refreshing weather data');
    
    this.showLoadingState();
    
    try {
      // Phase 2: Use existing weather service
      // const location = this.stateManager.getState('trip.currentLocation');
      // const weatherData = await this.weatherService.getWeather(location);
      // this.stateManager.setState('data.weatherData', weatherData);
      
      // Placeholder for Phase 1
      setTimeout(() => {
        this.hideLoadingState();
      }, 1000);
      
    } catch (error) {
      console.error('🌤️ WeatherComponent: Failed to refresh weather:', error);
      this.showErrorState(error.message);
    }
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Unsubscribe from state changes
    this.unsubscribers.forEach(unsubscribe => unsubscribe());
    this.unsubscribers = [];
    
    console.log('🌤️ WeatherComponent: Destroyed');
  }
}
