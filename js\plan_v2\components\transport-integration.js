/**
 * Transport Integration Component
 * 
 * Integrates the comprehensive transport calculation system with the plan_v2 page.
 * Handles transport data loading, display, and budget integration.
 */

import { CleanTransportService } from '../../services/transport-service-clean.js';

export class TransportIntegration {
  constructor(stateManager) {
    this.stateManager = stateManager;
    this.transportService = new CleanTransportService();
    this.currentTripId = null;
    this.transportData = null;
    this.isCalculating = false;
  }

  /**
   * Initialize transport integration
   * @param {number} tripId - Trip ID
   * @returns {Promise<void>}
   */
  async initialize(tripId) {
    console.log(`🚀 TransportIntegration: Initializing for trip ${tripId}`);
    
    this.currentTripId = tripId;
    
    try {
      // Check if transport data exists
      const status = await this.transportService.getTransportStatus(tripId);
      
      if (!status.hasTransportData) {
        console.log('⚠️ No transport data found, will calculate when needed');
        this.showTransportCalculationPrompt();
      } else {
        console.log('✅ Existing transport data found, loading...');
        await this.loadTransportData();
      }

    } catch (error) {
      console.error('❌ Error initializing transport integration:', error);
      this.showTransportError(error.message);
    }
  }

  /**
   * Load transport data for display
   * @param {number} currentDay - Current day number
   * @returns {Promise<void>}
   */
  async loadTransportData(currentDay = 1) {
    if (!this.currentTripId) {
      console.error('No trip ID set for transport loading');
      return;
    }

    try {
      console.log(`📊 Loading transport data for trip ${this.currentTripId}, day ${currentDay}`);
      
      const result = await this.transportService.getTransportForDisplay(
        this.currentTripId, 
        currentDay
      );

      if (!result.success) {
        throw new Error(result.error);
      }

      this.transportData = result.transportData;
      
      // Update state manager with transport data
      this.stateManager.setState('transport.data', this.transportData);
      this.stateManager.setState('transport.currentDay', currentDay);
      this.stateManager.setState('transport.isLoaded', true);

      // Update budget with transport costs
      this.updateBudgetWithTransport();

      // Render transport in UI
      this.renderTransportData(currentDay);

      console.log('✅ Transport data loaded and rendered successfully');

    } catch (error) {
      console.error('❌ Error loading transport data:', error);
      this.showTransportError(error.message);
    }
  }

  /**
   * Calculate transport for the trip
   * @returns {Promise<void>}
   */
  async calculateTransport() {
    if (!this.currentTripId || this.isCalculating) {
      return;
    }

    this.isCalculating = true;
    this.showTransportCalculating();

    try {
      console.log(`🔄 Calculating transport for trip ${this.currentTripId}`);
      
      const result = await this.transportService.calculateAndSaveTransport(
        this.currentTripId,
        {
          clearExisting: true,
          saveToDatabase: true,
          updateBudget: true
        }
      );

      if (!result.success) {
        throw new Error(result.error);
      }

      console.log('✅ Transport calculation completed successfully');
      console.log(`📈 Summary: ${result.summary.totalSegments} segments, $${result.summary.totalCostUSD.toFixed(2)} total`);

      // Load the calculated data
      await this.loadTransportData();

      this.showTransportCalculationSuccess(result.summary);

    } catch (error) {
      console.error('❌ Transport calculation failed:', error);
      this.showTransportError(error.message);
    } finally {
      this.isCalculating = false;
    }
  }

  /**
   * Update budget display with transport costs
   */
  updateBudgetWithTransport() {
    if (!this.transportData) return;

    const transportBudget = {
      category: 'Transport',
      totalBudget: this.transportData.totalCostUSD || 0,
      spent: this.transportData.totalCostUSD || 0,
      remaining: 0,
      percentage: 100
    };

    // Update state with transport budget
    this.stateManager.setState('budget.transport', transportBudget);
    
    console.log(`💰 Updated transport budget: $${transportBudget.totalBudget.toFixed(2)}`);
  }

  /**
   * Render transport data in the UI
   * @param {number} currentDay - Current day number
   */
  renderTransportData(currentDay) {
    // Render current day transport in transport tab
    this.renderDailyTransport(currentDay);
    
    // Render inter-city transport if applicable
    this.renderInterCityTransport(currentDay);
    
    // Update transport budget in budget card
    this.updateTransportBudgetDisplay();
  }

  /**
   * Render daily transport segments
   * @param {number} currentDay - Current day number
   */
  renderDailyTransport(currentDay) {
    const transportTab = document.getElementById('transport-tab');
    if (!transportTab || !this.transportData.currentDayTransport) {
      return;
    }

    const currentDayData = this.transportData.currentDayTransport;
    
    // Clear existing content
    transportTab.innerHTML = '';

    // Create transport header
    const header = document.createElement('div');
    header.className = 'transport-header';
    header.innerHTML = `
      <h3>
        <i class="fas fa-route"></i>
        Day ${currentDay} Transport
      </h3>
      <p>Daily movement between locations</p>
      <div class="transport-summary">
        <span class="segment-count">${currentDayData.segments.length} segments</span>
        <span class="total-cost">$${currentDayData.totalCostUSD.toFixed(2)} total</span>
      </div>
    `;
    transportTab.appendChild(header);

    // Create transport segments
    const segmentsContainer = document.createElement('div');
    segmentsContainer.className = 'transport-segments';

    for (const segment of currentDayData.segments) {
      const segmentCard = this.createTransportSegmentCard(segment);
      segmentsContainer.appendChild(segmentCard);
    }

    transportTab.appendChild(segmentsContainer);
  }

  /**
   * Create transport segment card
   * @param {Object} segment - Transport segment data
   * @returns {HTMLElement} Segment card element
   */
  createTransportSegmentCard(segment) {
    const card = document.createElement('div');
    card.className = 'transport-card';
    
    const primaryOption = segment.primaryOption;
    const hasAlternative = segment.alternativeOption !== null;

    card.innerHTML = `
      <div class="transport-route">
        <div class="route-info">
          <h4>${segment.description}</h4>
          <div class="route-details">
            <span class="from">${segment.from}</span>
            <i class="fas fa-arrow-right"></i>
            <span class="to">${segment.to}</span>
          </div>
        </div>
        <div class="route-stats">
          <span class="distance">${segment.distance.toFixed(1)} km</span>
          <span class="duration">${Math.round(segment.duration)} min</span>
        </div>
      </div>
      
      <div class="transport-option primary">
        <div class="option-header">
          <i class="fas ${primaryOption.icon}"></i>
          <span class="mode-name">${primaryOption.modeName}</span>
          <span class="price">$${primaryOption.priceUSD.toFixed(2)}</span>
        </div>
        <div class="option-details">
          <p class="reasoning">${primaryOption.reasoning}</p>
        </div>
      </div>
      
      ${hasAlternative ? `
        <div class="transport-option alternative">
          <div class="option-header">
            <i class="fas ${segment.alternativeOption.icon}"></i>
            <span class="mode-name">${segment.alternativeOption.modeName}</span>
            <span class="price">$${segment.alternativeOption.priceUSD.toFixed(2)}</span>
          </div>
          <div class="option-details">
            <p class="reasoning">${segment.alternativeOption.reasoning}</p>
          </div>
        </div>
      ` : ''}
    `;

    return card;
  }

  /**
   * Render inter-city transport
   * @param {number} currentDay - Current day number
   */
  renderInterCityTransport(currentDay) {
    if (!this.transportData.interCityTransport || this.transportData.interCityTransport.length === 0) {
      return;
    }

    // Find inter-city transport for current day
    const interCityForDay = this.transportData.interCityTransport.find(
      transport => transport.fromDay === currentDay
    );

    if (!interCityForDay) {
      return;
    }

    const transportTab = document.getElementById('transport-tab');
    if (!transportTab) return;

    // Add inter-city section
    const interCitySection = document.createElement('div');
    interCitySection.className = 'inter-city-transport';
    interCitySection.innerHTML = `
      <div class="inter-city-header">
        <h3>
          <i class="fas fa-map-marked-alt"></i>
          Inter-City Travel
        </h3>
        <p>${interCityForDay.description}</p>
      </div>
    `;

    const interCityCard = this.createInterCityCard(interCityForDay);
    interCitySection.appendChild(interCityCard);
    
    transportTab.appendChild(interCitySection);
  }

  /**
   * Create inter-city transport card
   * @param {Object} interCity - Inter-city transport data
   * @returns {HTMLElement} Inter-city card element
   */
  createInterCityCard(interCity) {
    const card = document.createElement('div');
    card.className = 'inter-city-card';
    
    const primaryOption = interCity.primaryOption;
    const hasAlternatives = interCity.alternativeOptions.length > 0;

    let alternativesHtml = '';
    if (hasAlternatives) {
      alternativesHtml = interCity.alternativeOptions.map(alt => `
        <div class="transport-option alternative">
          <div class="option-header">
            <i class="fas ${alt.icon}"></i>
            <span class="mode-name">${alt.modeName}</span>
            <span class="price">$${alt.priceUSD.toFixed(2)}</span>
          </div>
          <div class="option-details">
            <p class="reasoning">${alt.reasoning}</p>
          </div>
        </div>
      `).join('');
    }

    card.innerHTML = `
      <div class="inter-city-route">
        <div class="route-info">
          <h4>${interCity.fromCity} → ${interCity.toCity}</h4>
          <div class="route-stats">
            <span class="distance">${interCity.distance.toFixed(0)} km</span>
            <span class="duration">${Math.round(interCity.duration / 60)} hours</span>
          </div>
        </div>
      </div>
      
      <div class="transport-option primary">
        <div class="option-header">
          <i class="fas ${primaryOption.icon}"></i>
          <span class="mode-name">${primaryOption.modeName}</span>
          <span class="price">$${primaryOption.priceUSD.toFixed(2)}</span>
        </div>
        <div class="option-details">
          <p class="reasoning">${primaryOption.reasoning}</p>
        </div>
      </div>
      
      ${alternativesHtml}
    `;

    return card;
  }

  /**
   * Update transport budget display
   */
  updateTransportBudgetDisplay() {
    const budgetCard = document.querySelector('.budget-overview');
    if (!budgetCard || !this.transportData) return;

    // Find or create transport budget item
    let transportBudgetItem = budgetCard.querySelector('.category-item[data-category="transport"]');
    
    if (!transportBudgetItem) {
      // Create new transport budget item
      const budgetCategories = budgetCard.querySelector('.budget-categories');
      if (budgetCategories) {
        transportBudgetItem = document.createElement('div');
        transportBudgetItem.className = 'category-item';
        transportBudgetItem.setAttribute('data-category', 'transport');
        budgetCategories.appendChild(transportBudgetItem);
      }
    }

    if (transportBudgetItem) {
      const totalCost = this.transportData.totalCostUSD || 0;
      
      transportBudgetItem.innerHTML = `
        <div class="category-info">
          <div class="category-icon transport">
            <i class="fas fa-car"></i>
          </div>
          <div class="category-details">
            <h4>Transport</h4>
            <p>Daily and inter-city travel</p>
          </div>
        </div>
        <div class="category-amounts">
          <div class="spent">$${totalCost.toFixed(2)}</div>
          <div class="allocated">$${totalCost.toFixed(2)}</div>
        </div>
        <div class="category-progress">
          <div class="progress-bar">
            <div class="progress-fill" style="width: 100%"></div>
          </div>
          <span class="progress-text">100%</span>
        </div>
      `;
    }
  }

  /**
   * Show transport calculation prompt
   */
  showTransportCalculationPrompt() {
    // Implementation for showing calculation prompt
    console.log('🔔 Showing transport calculation prompt');
  }

  /**
   * Show transport calculating state
   */
  showTransportCalculating() {
    // Implementation for showing calculating state
    console.log('⏳ Showing transport calculating state');
  }

  /**
   * Show transport calculation success
   * @param {Object} summary - Calculation summary
   */
  showTransportCalculationSuccess(summary) {
    // Implementation for showing success message
    console.log('✅ Transport calculation completed:', summary);
  }

  /**
   * Show transport error
   * @param {string} errorMessage - Error message
   */
  showTransportError(errorMessage) {
    // Implementation for showing error message
    console.error('❌ Transport error:', errorMessage);
  }

  /**
   * Handle day change
   * @param {number} newDay - New day number
   */
  async onDayChange(newDay) {
    if (this.transportData) {
      await this.loadTransportData(newDay);
    }
  }

  /**
   * Recalculate transport
   */
  async recalculateTransport() {
    if (!this.currentTripId) return;
    
    await this.calculateTransport();
  }
}
