/* Main styling for Tsafira website */
/* CSS Variables for light/dark themes */
:root {
  --color-primary: #F26522;
  --color-primary-hover: #e05a1a;
  --color-secondary: #3F20FB;
  --color-text: #1f2937;
  --color-text-inverse: #ffffff;
  --color-text-muted: #6b7280;
  --color-bg: #ffffff;
  --color-bg-alt: #f9fafb;
  --color-bg-accent: #f3f4f6;
  --color-border: #e5e7eb;
}

.dark {
  --color-primary: #ff7733;
  --color-primary-hover: #ff8c4d;
  --color-secondary: #4f46e5;
  --color-text: #f3f4f6;
  --color-text-inverse: #ffffff;
  --color-text-muted: #9ca3af;
  --color-bg: #1f2937;
  --color-bg-alt: #111827;
  --color-bg-accent: #2d3748;
  --color-border: #374151;
}

/* Base styles */
* {
  font-family: "Inter", sans-serif;
}
::-webkit-scrollbar {
  display: none;
}

/* Scroll progress bar */
.scroll-progress-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  z-index: 9999;
}

.dark .scroll-progress-container {
  background: rgba(255, 255, 255, 0.1);
}

.scroll-progress-bar {
  height: 100%;
  background: var(--color-primary);
  width: 0%;
  border-radius: 0 2px 2px 0;
  box-shadow: 0 1px 3px rgba(242, 101, 34, 0.3);
  transition: width 0.1s ease;
}

.dark .scroll-progress-bar {
  background: var(--color-primary);
  box-shadow: 0 1px 3px rgba(255, 119, 51, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-fadeOut {
  animation: fadeOut 0.3s ease-out;
}

/* Form elements styling */
input, select, textarea {
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

input[type="checkbox"] {
  accent-color: var(--color-primary);
}

/* Button hover effects */
button:hover:not(:disabled),
a.btn:hover:not(:disabled) {
  transform: translateY(-1px);
}

button:active:not(:disabled),
a.btn:active:not(:disabled) {
  transform: translateY(0);
}

/* Mobile menu transition */
#mobile-menu {
  transition: opacity 0.3s ease, visibility 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

#mobile-menu.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

#mobile-menu:not(.hidden) {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Theme toggle button styling */
#theme-toggle {
  transition: background-color 0.3s ease;
}

#theme-toggle:hover {
  opacity: 0.9;
}

/* Dark mode transition effects */
html.transition,
html.transition *,
html.transition *:before,
html.transition *:after {
  transition: all 0.3s ease-in-out !important;
  transition-delay: 0 !important;
}

/* Fix for header & z-index hierarchy */
#header {
  z-index: 990 !important;
}

/* Accessibility improvements */
.focus-visible:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Enhanced buttons */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(242, 101, 34, 0.15);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.btn-primary:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(100, 100);
    opacity: 0;
  }
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: rgba(242, 101, 34, 0.1);
  transform: translateY(-2px);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* Enhanced inputs */
.input-primary {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
}

.input-primary:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(242, 101, 34, 0.2);
  outline: none;
}

.dark .input-primary {
  background-color: var(--color-bg-accent);
  border-color: var(--color-border);
  color: var(--color-text);
}

.dark .input-primary:focus {
  border-color: var(--color-primary);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.badge-primary {
  background-color: var(--color-primary);
  color: white;
}

.badge-secondary {
  background-color: white;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.dark .badge-secondary {
  background-color: var(--color-bg-accent);
  color: var(--color-text);
  border-color: var(--color-border);
}

.badge-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .desktop-only {
    display: block;
  }

  .mobile-only {
    display: none;
  }
}

@media (max-width: 767px) {
  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: block;
  }
}