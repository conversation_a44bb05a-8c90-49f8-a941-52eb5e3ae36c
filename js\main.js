/**
 * Main JavaScript entry point
 * Initializes all necessary components based on the current page
 */
import { initPartials } from './core/loader.js';
import { qs } from './utils/utils.js';
import { initAuth } from './core/auth.js';
import headerAuthManager from './core/headerAuth.js';
import { initUI } from './ui.js';

// Initialize the partials (header and footer)
initPartials();

// Main initialization function
function init() {
  // Initialize UI components
  initUI();

  // Initialize authentication once after header is loaded
  let authInitialized = false;

  // Listen for header loaded event specifically
  document.addEventListener('partialLoaded', function(e) {
    if (e.detail.id === 'header-placeholder' && !authInitialized) {
      console.log('Header loaded, initializing auth...');
      // Initialize immediately since we now have immediate auth check in header
      (async () => {
        await initAuth();
        await headerAuthManager.initialize();
        // Make headerAuthManager globally available
        window.headerAuthManager = headerAuthManager;
        authInitialized = true;
      })();
    }
  });

  // Fallback: initialize auth after a shorter delay if header doesn't load
  setTimeout(async () => {
    if (!authInitialized) {
      console.log('Fallback: initializing auth after timeout...');
      await initAuth();
      await headerAuthManager.initialize();
      // Make headerAuthManager globally available
      window.headerAuthManager = headerAuthManager;
      authInitialized = true;
    }
  }, 1000);

  // Dynamically import page-specific modules based on data-page attribute
  const body = document.body;
  const pageName = body.dataset.page;

  if (pageName) {
    try {
      import(`./${pageName}-page.js`)
        .then(module => {
          if (typeof module.init === 'function') {
            module.init();
          } else if (typeof module.default === 'object' && typeof module.default.init === 'function') {
            module.default.init();
          }
        })
        .catch(error => console.error(`Error loading page module for ${pageName}:`, error));
    } catch (error) {
      console.error(`Error importing page module for ${pageName}:`, error);
    }
  }
}

// Auth state changes are now handled directly in auth.js

// Initialize when DOM is fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}