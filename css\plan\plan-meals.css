/* Meals Tab */
.meals-banner {
  position: relative;
  height: 250px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-6);
}

.meals-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.meals-banner:hover img {
  transform: scale(1.05);
}

.meals-banner-content {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
}

.meals-banner-content h2 {
  font-size: 1.875rem;
  margin-bottom: var(--spacing-2);
}

.meals-banner-content p {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-4);
}

.meal-budget {
  display: inline-block;
  background-color: var(--color-primary);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-full);
  font-weight: 500;
}

.day-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6) 0;
}

.day-title {
  text-align: center;
}

.day-title h2 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-1);
}

.day-title p {
  color: var(--color-gray-600);
}

.dark .day-title p {
  color: var(--color-gray-400);
}

.nav-arrow {
  color: var(--color-gray-500);
  transition: all var(--transition-fast);
  font-size: 1.25rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
}

.dark .nav-arrow {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-300);
  color: var(--color-gray-400);
}

.nav-arrow:hover {
  color: var(--color-primary);
  background-color: var(--color-gray-100);
  transform: scale(1.1);
  box-shadow: var(--shadow-sm);
}

.dark .nav-arrow:hover {
  background-color: var(--color-gray-300);
}

.meals-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.meal-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  opacity: 1;
  transform: translateY(0);
  height: 100%;
  margin-bottom: var(--spacing-6);
  position: relative;
  backdrop-filter: blur(10px);
}

.meal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.meal-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.meal-card:hover::before {
  opacity: 1;
}

.dark .meal-card {
  background: linear-gradient(145deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.meal-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  color: var(--color-gray-700);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: var(--spacing-4);
  font-size: 0.9rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
}

.meal-time::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.dark .meal-time {
  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--color-gray-600);
}

.meal-time i {
  color: var(--color-primary);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
}

.meal-content {
  display: flex;
  flex-direction: column;
}

.meal-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
  border-radius: 0;
}

.meal-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 60%,
    rgba(0, 0, 0, 0.8) 100%
  );
  z-index: 1;
  transition: opacity 0.3s ease;
}

.meal-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(249, 115, 22, 0.1) 0%, transparent 70%);
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.meal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.meal-card:hover .meal-image img {
  transform: scale(1.1);
}

.meal-card:hover .meal-image::before {
  opacity: 1;
}

.meal-details {
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  flex: 1;
}

.meal-details h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-1);
  font-weight: 600;
  color: var(--color-gray-800);
}

.dark .meal-details h3 {
  color: var(--color-gray-700);
}

.meal-type {
  font-size: 0.875rem;
  color: var(--color-gray-600);
  font-weight: 500;
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dark .meal-type {
  color: var(--color-gray-500);
}

.meal-type::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-sm);
}

.meal-description {
  color: var(--color-gray-600);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-4);
  flex: 1;
}

.dark .meal-description {
  color: var(--color-gray-500);
}

.meal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: var(--spacing-3);
  border-top: 1px dashed var(--color-gray-200);
}

.dark .meal-footer {
  border-color: var(--color-gray-300);
}

.meal-tags {
  display: flex;
  gap: var(--spacing-2);
  color: var(--color-primary);
}

.meal-tags i {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all var(--transition-fast);
}

.meal-tags i:hover {
  transform: scale(1.1);
  background-color: var(--color-primary);
  color: white;
}

.meal-price {
  font-weight: 700;
  color: white;
  font-size: 1rem;
  padding: var(--spacing-2) var(--spacing-4);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  border-radius: var(--border-radius-full);
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
  position: relative;
  overflow: hidden;
}

.meal-price::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: var(--border-radius-full);
}

.meal-card-actions {
  padding: var(--spacing-4);
  border-top: 1px dashed rgba(226, 232, 240, 0.5);
  background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
}

.meal-card-actions .btn {
  width: 100%;
  justify-content: center;
  font-weight: 600;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.meal-card-actions .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.meal-card-actions .btn:hover::before {
  left: 100%;
}

.meal-card-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

.meals-summary {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

.summary-box {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.summary-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.dark .summary-box {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.summary-box:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.summary-box h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.summary-box h3::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 1.25rem;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-sm);
}

.summary-items {
  margin-bottom: var(--spacing-6);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.summary-total {
  display: flex;
  justify-content: space-between;
  padding-top: var(--spacing-3);
  margin-top: var(--spacing-3);
  border-top: 1px solid var(--color-gray-300);
  font-weight: 700;
  transition: all var(--transition-normal);
}

.dark .summary-total {
  border-color: var(--color-gray-400);
}

.summary-total span:last-child {
  color: var(--color-primary);
  font-size: 1.125rem;
}

.summary-note {
  font-size: 0.875rem;
  color: var(--color-gray-500);
  margin-bottom: var(--spacing-4);
  font-style: italic;
}

.dark .summary-note {
  color: var(--color-gray-400);
}

.dining-tips h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dining-tips h3::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 1.25rem;
  background-color: var(--color-secondary);
  border-radius: var(--border-radius-sm);
}

.tips-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

.tip-item {
  background-color: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.dark .tip-item {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
  color: var(--color-gray-800);
}

.tip-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.tip-item i {
  color: var(--color-secondary);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-3);
}

.tip-item h4 {
  font-weight: 500;
  margin-bottom: var(--spacing-2);
}

.tip-item p {
  font-size: 0.875rem;
  color: var(--color-gray-600);
  line-height: 1.6;
}

.dark .tip-item p {
  color: var(--color-gray-400);
}

/* Carousel for Dining Tips */
#dining-carousel {
  position: relative;
  overflow: hidden;
  height: 12rem;
  margin-bottom: var(--spacing-4);
  background-color: white;
  border-radius: var(--border-radius-lg);
  color: var(--color-gray-800);
  text-align: center;
  border: 1px solid var(--color-gray-200);
}

.dark #dining-carousel {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
  border-color: var(--color-gray-300);
}

.carousel-slide {
  position: absolute;
  inset: 0;
  padding: var(--spacing-4);
  opacity: 0;
  transition: opacity var(--transition-slow), transform var(--transition-slow);
  transform: translateX(100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.carousel-slide.active {
  opacity: 1;
  transform: translateX(0);
}

#dining-carousel-dots {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
}

.carousel-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
}

.dark .carousel-dot {
  background-color: var(--color-gray-400);
}

.carousel-dot.active {
  background-color: var(--color-secondary);
  transform: scale(1.2);
}

.carousel-dot:hover {
  background-color: var(--color-secondary-light);
  transform: scale(1.1);
}

#dining-carousel .nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  color: var(--color-gray-600);
  background-color: transparent;
  border: none;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark #dining-carousel .nav-arrow {
  color: var(--color-gray-600);
}

#dining-carousel #prev-dining-slide {
  left: 0.5rem;
}

#dining-carousel #next-dining-slide {
  right: 0.5rem;
}

#dining-carousel .carousel-slide i {
  color: var(--color-secondary);
  font-size: 2rem;
  margin-bottom: 1rem;
}

#dining-carousel .carousel-slide h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

/* Enhanced Mobile Responsiveness for Meals */
@media (max-width: 768px) {
  .meals-banner {
    height: 200px;
    margin-bottom: var(--spacing-4);
  }

  .meals-banner-content {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .meals-banner-content h2 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-1);
  }

  .meals-banner-content p {
    font-size: 1rem;
    margin-bottom: var(--spacing-2);
  }

  .meal-budget {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 0.875rem;
  }

  .meals-list {
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }

  .meal-card {
    margin-bottom: var(--spacing-4);
    border-radius: var(--border-radius-lg);
  }

  .meal-card:hover {
    transform: translateY(-6px) scale(1.01);
  }

  .meal-time {
    padding: var(--spacing-3);
    font-size: 0.85rem;
  }

  .meal-time i {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .meal-image {
    height: 160px;
  }

  .meal-details {
    padding: var(--spacing-4);
    gap: var(--spacing-2);
  }

  .meal-details h3 {
    font-size: 1.125rem;
  }

  .meal-type {
    font-size: 0.8rem;
  }

  .meal-description {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .meal-footer {
    padding-top: var(--spacing-2);
  }

  .meal-tags i {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .meal-price {
    font-size: 0.9rem;
    padding: var(--spacing-2) var(--spacing-3);
  }

  .meal-card-actions {
    padding: var(--spacing-4);
  }

  .meal-card-actions .btn {
    padding: var(--spacing-3);
    font-size: 0.875rem;
  }

  .meals-summary {
    gap: var(--spacing-6);
  }

  .summary-box {
    padding: var(--spacing-6);
    border-radius: var(--border-radius-lg);
  }

  .summary-box h3 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-3);
  }

  .summary-items {
    margin-bottom: var(--spacing-4);
  }

  .summary-item {
    margin-bottom: var(--spacing-2);
    font-size: 0.875rem;
  }

  .summary-total {
    font-size: 0.9rem;
  }

  .summary-note {
    font-size: 0.8rem;
    margin-bottom: var(--spacing-3);
  }

  #dining-carousel {
    height: 10rem;
    border-radius: var(--border-radius-lg);
  }

  .carousel-slide {
    padding: var(--spacing-3);
  }

  #dining-carousel .carousel-slide i {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  #dining-carousel .carousel-slide h4 {
    font-size: 1rem;
    margin-bottom: 0.375rem;
  }

  #dining-carousel .carousel-slide p {
    font-size: 0.8rem;
    line-height: 1.4;
  }
}

@media (max-width: 576px) {
  .meals-banner {
    height: 180px;
    border-radius: var(--border-radius-lg);
  }

  .meals-banner-content {
    padding: var(--spacing-3);
  }

  .meals-banner-content h2 {
    font-size: 1.25rem;
  }

  .meals-banner-content p {
    font-size: 0.9rem;
  }

  .meal-budget {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: 0.8rem;
  }

  .meal-card {
    border-radius: var(--border-radius-lg);
  }

  .meal-time {
    padding: var(--spacing-3);
    font-size: 0.8rem;
  }

  .meal-image {
    height: 140px;
  }

  .meal-details {
    padding: var(--spacing-3);
  }

  .meal-details h3 {
    font-size: 1rem;
  }

  .meal-type {
    font-size: 0.75rem;
  }

  .meal-description {
    font-size: 0.8rem;
  }

  .meal-tags i {
    width: 20px;
    height: 20px;
    font-size: 0.65rem;
  }

  .meal-price {
    font-size: 0.85rem;
    padding: var(--spacing-1) var(--spacing-3);
  }

  .meal-card-actions {
    padding: var(--spacing-3);
  }

  .meal-card-actions .btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 0.8rem;
  }

  .summary-box {
    padding: var(--spacing-4);
  }

  .summary-box h3 {
    font-size: 1rem;
  }

  .summary-item {
    font-size: 0.8rem;
  }

  .summary-total {
    font-size: 0.85rem;
  }

  .summary-note {
    font-size: 0.75rem;
  }

  #dining-carousel {
    height: 9rem;
  }

  .carousel-slide {
    padding: var(--spacing-2);
  }

  #dining-carousel .carousel-slide i {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  #dining-carousel .carousel-slide h4 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  #dining-carousel .carousel-slide p {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  /* Enhanced restaurant cards responsive */
  .restaurant-image {
    height: 150px;
  }

  .restaurant-content {
    padding: var(--spacing-4);
  }

  .restaurant-pricing {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }

  .view-menu-btn {
    width: 100%;
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    max-height: 90vh;
  }
}

/* Menu Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content.menu-modal {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
}

.dark .modal-content.menu-modal {
  background: var(--color-gray-100);
  color: var(--color-gray-800);
}

.modal-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .modal-header {
  border-bottom-color: var(--color-gray-400);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-900);
}

.dark .modal-header h3 {
  color: var(--color-gray-100);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-gray-500);
  transition: color 0.2s ease;
  padding: var(--spacing-2);
  border-radius: var(--border-radius-md);
}

.modal-close:hover {
  color: var(--color-gray-700);
  background: var(--color-gray-100);
}

.dark .modal-close:hover {
  color: var(--color-gray-300);
  background: var(--color-gray-300);
}

.modal-body {
  padding: var(--spacing-6);
  max-height: 60vh;
  overflow-y: auto;
}

.menu-loading {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--color-gray-500);
}

.menu-loading i {
  font-size: 2rem;
  margin-bottom: var(--spacing-4);
  color: var(--color-primary);
}

.menu-section {
  margin-bottom: var(--spacing-6);
}

.menu-section:last-child {
  margin-bottom: 0;
}

.menu-section h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: 2px solid var(--color-primary);
}

.dark .menu-section h4 {
  color: var(--color-gray-100);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--color-gray-200);
  transition: background-color 0.2s ease;
}

.dark .menu-item {
  border-bottom-color: var(--color-gray-400);
}

.menu-item:hover {
  background: var(--color-gray-50);
}

.dark .menu-item:hover {
  background: var(--color-gray-200);
}

.menu-item:last-child {
  border-bottom: none;
}

.item-name {
  font-weight: 500;
  color: var(--color-gray-800);
}

.dark .item-name {
  color: var(--color-gray-200);
}

.item-price {
  font-weight: 600;
  color: var(--color-primary);
  font-size: 1.1rem;
}

/* Enhanced Restaurant Cards for Day-by-Day Tab */
.restaurants-grid.enhanced {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
  margin-top: var(--spacing-4);
}

.restaurant-card.enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(10px);
}

.restaurant-card.enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.restaurant-card.enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.restaurant-card.enhanced:hover::before {
  opacity: 1;
}

.dark .restaurant-card.enhanced {
  background: linear-gradient(145deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.restaurant-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.restaurant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.restaurant-card.enhanced:hover .restaurant-image img {
  transform: scale(1.1);
}

.restaurant-image .meal-type-badge {
  position: absolute;
  top: var(--spacing-3);
  left: var(--spacing-3);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.restaurant-image .restaurant-time {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  z-index: 2;
}

.restaurant-content {
  padding: var(--spacing-5);
}

.restaurant-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-3);
}

.dark .restaurant-name {
  color: var(--color-gray-700);
}

.restaurant-location,
.restaurant-cuisine,
.restaurant-distance,
.restaurant-hours {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
  font-size: 0.875rem;
  color: var(--color-gray-600);
}

.dark .restaurant-location,
.dark .restaurant-cuisine,
.dark .restaurant-distance,
.dark .restaurant-hours {
  color: var(--color-gray-500);
}

.restaurant-location i,
.restaurant-cuisine i,
.restaurant-distance i,
.restaurant-hours i {
  color: var(--color-primary);
  width: 16px;
  text-align: center;
}

.restaurant-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars i {
  color: #fbbf24;
  font-size: 0.875rem;
}

.rating-score {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600);
}

.dark .rating-score {
  color: var(--color-gray-500);
}

.restaurant-description {
  margin: var(--spacing-3) 0;
  padding: var(--spacing-3);
  background: rgba(249, 115, 22, 0.05);
  border-radius: var(--border-radius-md);
  border-left: 3px solid var(--color-primary);
}

.restaurant-description p {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-gray-700);
  margin: 0;
}

.dark .restaurant-description {
  background: rgba(249, 115, 22, 0.1);
}

.dark .restaurant-description p {
  color: var(--color-gray-600);
}

.restaurant-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-3);
  border-top: 1px dashed var(--color-gray-200);
}

.dark .restaurant-pricing {
  border-color: var(--color-gray-300);
}

.restaurant-price {
  font-weight: 700;
  color: var(--color-primary);
  font-size: 1.125rem;
}

.view-menu-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.view-menu-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.view-menu-btn:hover::before {
  left: 100%;
}

.view-menu-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal-backdrop {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}