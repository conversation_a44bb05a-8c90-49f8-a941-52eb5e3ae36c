/**
 * Plan V2 - Main Application Entry Point
 * 
 * This is the main orchestration file for the clean plan page implementation.
 * It initializes all components and manages the overall application state.
 */

// Import modules
import { DataManager } from './data-manager.js';
import { BudgetComponent } from './components/budget-component.js';
import renderAccommodationsTab from './tabs/accommodations-tab.js';
import renderRestaurantsTab from './tabs/restaurants-tab.js';
import renderTransportTab from './tabs/transport-tab.js';

/**
 * Safe localStorage access with error handling for file:// protocol
 */
function safeGetLocalStorage(key, defaultValue = null) {
  try {
    return localStorage.getItem(key) || defaultValue;
  } catch (error) {
    console.warn(`⚠️ localStorage access denied for key "${key}":`, error.message);
    return defaultValue;
  }
}

function safeSetLocalStorage(key, value) {
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (error) {
    console.warn(`⚠️ localStorage write denied for key "${key}":`, error.message);
    return false;
  }
}

/**
 * Application State
 */
const AppState = {
  currentTrip: null,
  currentDay: 1,
  totalDays: 8,
  tripData: null,
  weatherData: null,
  isLoading: false,
  theme: safeGetLocalStorage('theme', 'light'),
  dataManager: null,
  tripId: null,
  budgetComponent: null
};

/**
 * DOM Elements
 */
const Elements = {
  loadingOverlay: document.getElementById('loading-overlay'),
  themeToggle: document.getElementById('theme-toggle'),
  tripStatusBanner: document.getElementById('trip-status-banner'),
  statusClose: document.querySelector('.status-close'),
  prevDayBtn: document.getElementById('prev-day'),
  nextDayBtn: document.getElementById('next-day'),
  dayCards: document.querySelectorAll('.day-card'),
  dayTabs: document.querySelectorAll('.day-tab'),
  tabPanes: document.querySelectorAll('.tab-pane')
};

/**
 * Initialize the application
 */
async function initializeApp() {
  console.log('🚀 Initializing Plan V2 Application...');

  try {
    // Show loading overlay
    showLoading();

    // Extract trip ID from URL
    const tripId = DataManager.extractTripIdFromUrl();
    if (!tripId) {
      throw new Error('Trip ID is required. Please use format: plan_v2.html?id=123');
    }

    AppState.tripId = tripId;

    // Initialize theme
    initializeTheme();

    // Set up event listeners
    setupEventListeners();

    // Initialize tab system
    initializeTabSystem();

    // Initialize day navigation
    initializeDayNavigation();

    // Initialize data manager and load real data
    await initializeDataManager(tripId);

    // Initialize budget component
    initializeBudgetComponent();

    // Initialize weather component
    initializeWeatherComponent();

    // Hide loading overlay
    hideLoading();

    console.log('✅ Plan V2 Application initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize application:', error);
    showErrorState(error.message);
  }
}

/**
 * Initialize Data Manager and load trip data
 */
async function initializeDataManager(tripId) {
  try {
    console.log(`📊 Initializing data manager for trip ${tripId}...`);

    // Create and initialize data manager
    AppState.dataManager = new DataManager();
    await AppState.dataManager.initialize(tripId);

    // Load complete trip data
    const tripData = await AppState.dataManager.loadCompleteData();
    AppState.tripData = tripData;

    // Update UI with real data
    updateTripHeader(tripData);

    // Update budget if component is already initialized
    if (AppState.budgetComponent) {
      updateBudgetOverview(tripData);
    }

    // Update weather if component is already initialized
    if (AppState.weatherComponent) {
      updateWeatherOverview(tripData, AppState.currentDay);
    }

    // Update current day from trip data
    if (tripData.trip.dates?.current_day) {
      AppState.currentDay = tripData.trip.dates.current_day;
    }

    updateDayNavigation();
    updateDayContent();

    console.log('✅ Data manager initialized and data loaded');

  } catch (error) {
    console.error('❌ Failed to initialize data manager:', error);
    throw error;
  }
}

/**
 * Theme Management
 */
function initializeTheme() {
  // Apply saved theme using class-based approach to match main.css
  if (AppState.theme === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
  updateThemeIcon();

  console.log(`🎨 Theme initialized: ${AppState.theme}`);
}

function toggleTheme() {
  AppState.theme = AppState.theme === 'light' ? 'dark' : 'light';

  // Use class-based approach to match main.css
  if (AppState.theme === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }

  // Safe localStorage access
  safeSetLocalStorage('theme', AppState.theme);
  updateThemeIcon();

  console.log(`🎨 Theme switched to: ${AppState.theme}`);
}

function updateThemeIcon() {
  const icon = Elements.themeToggle.querySelector('i');
  icon.className = AppState.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
}

/**
 * Event Listeners Setup
 */
function setupEventListeners() {
  // Theme toggle
  Elements.themeToggle?.addEventListener('click', toggleTheme);
  
  // Status banner close
  Elements.statusClose?.addEventListener('click', hideStatusBanner);
  
  // Day navigation
  Elements.prevDayBtn?.addEventListener('click', () => navigateDay(-1));
  Elements.nextDayBtn?.addEventListener('click', () => navigateDay(1));
  
  // Day cards
  Elements.dayCards.forEach(card => {
    card.addEventListener('click', () => {
      const day = parseInt(card.dataset.day);
      selectDay(day);
    });
  });
  
  // Tab navigation
  Elements.dayTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const tabName = tab.dataset.tab;
      switchTab(tabName);
    });
  });
  
  // Keyboard navigation
  document.addEventListener('keydown', handleKeyboardNavigation);
  
  console.log('🎯 Event listeners set up');
}

/**
 * Tab System
 */
function initializeTabSystem() {
  // Ensure first tab is active
  const firstTab = Elements.dayTabs[0];
  const firstPane = Elements.tabPanes[0];
  
  if (firstTab && firstPane) {
    firstTab.classList.add('active');
    firstPane.classList.add('active');
  }
  
  console.log('📑 Tab system initialized');
}

function switchTab(tabName) {
  // Remove active class from all tabs and panes
  Elements.dayTabs.forEach(tab => tab.classList.remove('active'));
  Elements.tabPanes.forEach(pane => pane.classList.remove('active'));
  
  // Add active class to selected tab and pane
  const selectedTab = document.querySelector(`[data-tab="${tabName}"]`);
  const selectedPane = document.getElementById(`${tabName}-tab`);
  
  if (selectedTab && selectedPane) {
    selectedTab.classList.add('active');
    selectedPane.classList.add('active');
    
    console.log(`📑 Switched to tab: ${tabName}`);
    
    // Trigger tab-specific loading if needed
    loadTabContent(tabName).catch(error => {
      console.error('❌ Error loading tab content:', error);
    });
  }
}

/**
 * Day Navigation
 */
function initializeDayNavigation() {
  updateDayNavigation();
  console.log('📅 Day navigation initialized');
}

function navigateDay(direction) {
  const newDay = AppState.currentDay + direction;
  
  if (newDay >= 1 && newDay <= AppState.totalDays) {
    selectDay(newDay);
  }
}

function selectDay(day) {
  if (day < 1 || day > AppState.totalDays) return;
  
  AppState.currentDay = day;
  updateDayNavigation();
  updateDayContent();
  
  console.log(`📅 Selected day: ${day}`);
}

function updateDayNavigation() {
  // Update navigation buttons
  if (Elements.prevDayBtn) {
    Elements.prevDayBtn.disabled = AppState.currentDay <= 1;
  }
  
  if (Elements.nextDayBtn) {
    Elements.nextDayBtn.disabled = AppState.currentDay >= AppState.totalDays;
  }
  
  // Update day cards
  Elements.dayCards.forEach(card => {
    const cardDay = parseInt(card.dataset.day);
    card.classList.toggle('active', cardDay === AppState.currentDay);
  });
  
  // Update day title
  updateDayTitle();
}

// updateDayTitle function moved to bottom of file to use real trip data

/**
 * Content Loading
 */
function updateDayContent() {
  console.log(`📄 Loading content for day ${AppState.currentDay}`);

  if (!AppState.tripData) {
    console.warn('⚠️ No trip data available for content update');
    return;
  }

  // Update day title and subtitle
  updateDayTitle();

  // Update weather for the current day
  if (AppState.weatherComponent) {
    updateWeatherOverview(AppState.tripData, AppState.currentDay);
  }

  // Trigger the current tab to refresh with real data
  const activeTab = document.querySelector('.day-tab.active');
  if (activeTab) {
    const tabName = activeTab.dataset.tab;
    loadTabContent(tabName).catch(error => {
      console.error('❌ Error loading tab content:', error);
    });
  }
}

async function loadTabContent(tabName) {
  console.log(`📄 Loading ${tabName} content for day ${AppState.currentDay}`);

  if (!AppState.dataManager || !AppState.tripData) {
    console.warn('⚠️ Data manager or trip data not available');
    return;
  }

  try {
    // Get day-specific data
    const dayData = AppState.tripData.days?.find(day => day.day_number === AppState.currentDay);

    if (!dayData) {
      console.warn(`⚠️ No data found for day ${AppState.currentDay}`);
      showNoDataMessage(tabName);
      return;
    }

    console.log(`📊 Day ${AppState.currentDay} data:`, dayData);

    // Load content based on tab type
    switch (tabName) {
      case 'timeline':
        renderTimelineTab(dayData);
        break;
      case 'accommodations':
        await renderAccommodationsTab(dayData, AppState);
        break;
      case 'restaurants':
        await renderRestaurantsTab(dayData, AppState);
        break;
      case 'activities':
        renderActivitiesTab(dayData);
        break;
      case 'transport':
        await renderTransportTab(dayData, AppState);
        break;
    }

  } catch (error) {
    console.error(`❌ Error loading ${tabName} content:`, error);
    showErrorMessage(tabName, error.message);
  }
}

/**
 * Tab Rendering Functions
 */





/**
 * Loading States
 */
function showLoading() {
  AppState.isLoading = true;
  Elements.loadingOverlay?.classList.remove('hidden');
}

function hideLoading() {
  AppState.isLoading = false;
  Elements.loadingOverlay?.classList.add('hidden');
}

/**
 * Status Banner Management
 */
function showStatusBanner(type, title, message) {
  const banner = Elements.tripStatusBanner;
  if (!banner) return;
  
  banner.className = `trip-status-banner ${type}`;
  
  const icon = banner.querySelector('.status-icon');
  const titleEl = banner.querySelector('.status-title');
  const messageEl = banner.querySelector('.status-message');
  
  // Set icon based on type
  const icons = {
    info: 'fas fa-info-circle',
    warning: 'fas fa-exclamation-triangle',
    success: 'fas fa-check-circle',
    error: 'fas fa-times-circle'
  };
  
  if (icon) icon.className = `status-icon ${icons[type] || icons.info}`;
  if (titleEl) titleEl.textContent = title;
  if (messageEl) messageEl.textContent = message;
  
  banner.classList.remove('hidden');
  
  // Auto-hide after 10 seconds
  setTimeout(() => hideStatusBanner(), 10000);
}

function hideStatusBanner() {
  Elements.tripStatusBanner?.classList.add('hidden');
}

/**
 * Keyboard Navigation
 */
function handleKeyboardNavigation(event) {
  if (event.target.tagName === 'INPUT' || event.target.contentEditable === 'true') {
    return; // Don't interfere with input fields
  }
  
  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault();
      navigateDay(-1);
      break;
    case 'ArrowRight':
      event.preventDefault();
      navigateDay(1);
      break;
    case '1':
    case '2':
    case '3':
    case '4':
    case '5':
    case '6':
    case '7':
    case '8':
      event.preventDefault();
      selectDay(parseInt(event.key));
      break;
  }
}

/**
 * Error Handling
 */
function showErrorState() {
  hideLoading();
  showStatusBanner(
    'error',
    'Application Error',
    'Failed to load the trip data. Please refresh the page and try again.'
  );
}

/**
 * Render activities tab with real data
 */
function renderActivitiesTab(dayData) {
  console.log('🎯 Rendering activities tab for day', dayData.day_number);

  const activitiesTab = document.getElementById('activities-tab');
  if (!activitiesTab) return;

  try {
    if (!dayData.activities || dayData.activities.length === 0) {
      activitiesTab.innerHTML = `
        <div class="no-data-message">
          <i class="fas fa-map-marker-alt"></i>
          <h3>No activity data</h3>
          <p>No activities scheduled for this day.</p>
        </div>
      `;
      return;
    }

    const activitiesHtml = dayData.activities.map(activity => `
      <div class="activity-card">
        <div class="activity-content">
          <div class="activity-header">
            <h3>${activity.name || 'Activity'}</h3>
            <div class="activity-time">${activity.time || 'Time TBD'}</div>
            <div class="activity-duration">${activity.duration || 'Duration TBD'}</div>
          </div>

          <div class="activity-details">
            <div class="detail-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>${activity.location?.address || activity.location?.city || 'Location not specified'}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-users"></i>
              <span>${activity.group_size || 'Small Group'}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-language"></i>
              <span>${activity.languages || 'English'}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-star"></i>
              <span>${activity.rating?.score || 'N/A'}/5 (${activity.rating?.review_count || 0} reviews)</span>
            </div>
          </div>

          <div class="activity-description">
            <p>${activity.description || 'Exciting activity to explore the local culture and attractions.'}</p>
            ${activity.highlights ? `
              <div class="activity-highlights">
                <h4>Activity Highlights:</h4>
                <ul>
                  ${activity.highlights.map(highlight => `<li>${highlight}</li>`).join('')}
                </ul>
              </div>
            ` : ''}
          </div>

          <div class="activity-pricing">
            <div class="price-info">
              <span class="price">$${activity.pricing?.price_per_person || 'N/A'}</span>
              <span class="period">/ person</span>
            </div>
            <div class="activity-actions">
              <button class="btn btn-outline">View Details</button>
              <button class="btn btn-primary">Modify Booking</button>
            </div>
          </div>
        </div>
      </div>
    `).join('');

    activitiesTab.innerHTML = activitiesHtml;

  } catch (error) {
    console.error('❌ Error rendering activities tab:', error);
    activitiesTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading activity data</h3>
        <p>Please try refreshing the page.</p>
      </div>
    `;
  }
}



/**
 * Render timeline tab with real data
 */
function renderTimelineTab(dayData) {
  console.log('⏰ Rendering timeline tab for day', dayData.day_number);

  const timelineTab = document.getElementById('timeline-tab');
  if (!timelineTab) return;

  try {
    // Collect all events for the day
    const events = [];

    // Add accommodation events
    if (dayData.lodging) {
      events.push({
        time: '15:00',
        type: 'accommodation',
        icon: 'bed',
        title: 'Hotel Check-in',
        description: dayData.lodging.name || 'Accommodation',
        category: 'Accommodation'
      });
    }

    // Add meal events
    if (dayData.meals && dayData.meals.length > 0) {
      dayData.meals.forEach(meal => {
        events.push({
          time: meal.time || 'TBD',
          type: 'meal',
          icon: 'utensils',
          title: `${meal.type || 'Meal'} at ${meal.restaurant?.name || 'Restaurant'}`,
          description: meal.restaurant?.description || 'Delicious local cuisine',
          category: 'Meal'
        });
      });
    }

    // Add activity events
    if (dayData.activities && dayData.activities.length > 0) {
      dayData.activities.forEach(activity => {
        events.push({
          time: activity.time || 'TBD',
          type: 'activity',
          icon: 'map-marker-alt',
          title: activity.name || 'Activity',
          description: activity.description || 'Exciting local experience',
          category: 'Activity'
        });
      });
    }

    // Add transport events
    const allTransport = [...(dayData.intra_city || []), ...(dayData.inter_city || [])];
    allTransport.forEach(transport => {
      events.push({
        time: transport.departure_time || 'TBD',
        type: 'transport',
        icon: getTransportIcon(transport.mode),
        title: `${transport.mode || 'Transport'} to ${transport.to_location || 'Destination'}`,
        description: `${transport.distance_km || 'N/A'} km • ${transport.duration_minutes ? Math.round(transport.duration_minutes / 60) + 'h ' + (transport.duration_minutes % 60) + 'min' : 'Duration TBD'}`,
        category: 'Transport'
      });
    });

    // Sort events by time (basic sorting, could be improved)
    events.sort((a, b) => {
      if (a.time === 'TBD') return 1;
      if (b.time === 'TBD') return -1;
      return a.time.localeCompare(b.time);
    });

    if (events.length === 0) {
      timelineTab.innerHTML = `
        <div class="timeline-container">
          <div class="timeline-header">
            <h3>Complete Day Timeline</h3>
            <p>No events scheduled for Day ${dayData.day_number}</p>
          </div>
          <div class="no-data-message">
            <i class="fas fa-clock"></i>
            <h3>No timeline data</h3>
            <p>No events scheduled for this day.</p>
          </div>
        </div>
      `;
      return;
    }

    const timelineHtml = events.map(event => `
      <div class="timeline-item">
        <div class="timeline-time">${event.time}</div>
        <div class="timeline-icon ${event.type}">
          <i class="fas fa-${event.icon}"></i>
        </div>
        <div class="timeline-content">
          <h4>${event.title}</h4>
          <p>${event.description}</p>
          <span class="timeline-category">${event.category}</span>
        </div>
      </div>
    `).join('');

    timelineTab.innerHTML = `
      <div class="timeline-container">
        <div class="timeline-header">
          <h3>Complete Day Timeline</h3>
          <p>All activities, meals, and transport for Day ${dayData.day_number}</p>
        </div>
        <div class="timeline">
          ${timelineHtml}
        </div>
      </div>
    `;

  } catch (error) {
    console.error('❌ Error rendering timeline tab:', error);
    timelineTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading timeline data</h3>
        <p>Please try refreshing the page.</p>
      </div>
    `;
  }
}

/**
 * Utility Functions
 */



function getTransportIcon(mode) {
  const iconMap = {
    'car': 'car',
    'bus': 'bus',
    'train': 'train',
    'plane': 'plane',
    'walking': 'walking',
    'taxi': 'taxi',
    'bike': 'bicycle'
  };

  return iconMap[mode?.toLowerCase()] || 'car';
}

function showNoDataMessage(tabName) {
  const tabElement = document.getElementById(`${tabName}-tab`);
  if (tabElement) {
    tabElement.innerHTML = `
      <div class="no-data-message">
        <i class="fas fa-info-circle"></i>
        <h3>No data available</h3>
        <p>No ${tabName} information available for this day.</p>
      </div>
    `;
  }
}

function showErrorMessage(tabName, message) {
  const tabElement = document.getElementById(`${tabName}-tab`);
  if (tabElement) {
    tabElement.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading data</h3>
        <p>${message}</p>
      </div>
    `;
  }
}

function updateDayTitle() {
  const dayTitle = document.querySelector('.day-title');
  const daySubtitle = document.querySelector('.day-subtitle');

  if (dayTitle && AppState.tripData) {
    try {
      // Use real trip data for dates
      const tripStartDate = new Date(AppState.tripData.trip.dates.start_date);
      const currentDayDate = new Date(tripStartDate);
      currentDayDate.setDate(tripStartDate.getDate() + AppState.currentDay - 1);

      const dateStr = currentDayDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });

      dayTitle.textContent = `Day ${AppState.currentDay} - ${dateStr}`;
    } catch (error) {
      console.error('❌ Error updating day title:', error);
      dayTitle.textContent = `Day ${AppState.currentDay}`;
    }
  }

  if (daySubtitle && AppState.tripData) {
    try {
      // Use real day data for subtitle
      const dayData = AppState.tripData.days?.find(day => day.day_number === AppState.currentDay);
      if (dayData && dayData.city_name) {
        daySubtitle.textContent = `Exploring ${dayData.city_name}`;
      } else {
        daySubtitle.textContent = 'Adventure Continues';
      }
    } catch (error) {
      console.error('❌ Error updating day subtitle:', error);
      daySubtitle.textContent = 'Adventure Continues';
    }
  }
}

function updateTripHeader(tripData) {
  try {
    const tripTitle = document.querySelector('.trip-title');
    const tripDates = document.querySelector('.trip-dates span');

    if (tripTitle && tripData.trip.name) {
      tripTitle.textContent = tripData.trip.name;
    }

    if (tripDates && tripData.trip.dates) {
      const startDate = new Date(tripData.trip.dates.start_date);
      const endDate = new Date(tripData.trip.dates.end_date);
      const totalDays = tripData.days ? tripData.days.length : 0;

      const startStr = startDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
      const endStr = endDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });

      tripDates.textContent = `${startStr} - ${endStr} • ${totalDays} Days`;

      // Update total days in state
      AppState.totalDays = totalDays;
    }

  } catch (error) {
    console.error('❌ Error updating trip header:', error);
  }
}

/**
 * Initialize Budget Component
 */
function initializeBudgetComponent() {
  try {
    console.log('💰 Initializing budget component...');

    // Find the budget overview container
    const budgetContainer = document.querySelector('.budget-overview');
    if (!budgetContainer) {
      console.warn('⚠️ Budget container not found');
      return;
    }

    // Create a simple state manager for the budget component
    const simpleBudgetStateManager = {
      subscribe: () => () => {}, // No-op for direct usage
      setState: () => {} // No-op for direct usage
    };

    // Initialize the budget component
    AppState.budgetComponent = new BudgetComponent(budgetContainer, simpleBudgetStateManager);

    // Load data if trip data is already available
    if (AppState.tripData) {
      AppState.budgetComponent.loadFromTripData(AppState.tripData);
    }

    console.log('✅ Budget component initialized');

  } catch (error) {
    console.error('❌ Error initializing budget component:', error);
  }
}

/**
 * Update budget component with new trip data
 */
function updateBudgetOverview(tripData) {
  try {
    if (AppState.budgetComponent) {
      AppState.budgetComponent.loadFromTripData(tripData);
    } else {
      console.warn('⚠️ Budget component not initialized yet');
    }
  } catch (error) {
    console.error('❌ Error updating budget overview:', error);
  }
}

/**
 * Initialize Weather Component
 */
function initializeWeatherComponent() {
  try {
    console.log('🌤️ Initializing weather component...');

    // Find the weather overview container
    const weatherContainer = document.querySelector('.weather-overview');
    if (!weatherContainer) {
      console.warn('⚠️ Weather container not found');
      return;
    }

    // Create a simple state manager for the weather component
    const simpleWeatherStateManager = {
      subscribe: () => () => {}, // No-op for direct usage
      setState: () => {} // No-op for direct usage
    };

    // Import and initialize the weather component
    import('./components/weather-component.js').then(module => {
      const { WeatherComponent } = module;
      AppState.weatherComponent = new WeatherComponent(weatherContainer, simpleWeatherStateManager);

      // Load data if trip data is already available
      if (AppState.tripData) {
        updateWeatherOverview(AppState.tripData, AppState.currentDay);
      }

      console.log('✅ Weather component initialized');
    }).catch(error => {
      console.error('❌ Error importing weather component:', error);
    });

  } catch (error) {
    console.error('❌ Error initializing weather component:', error);
  }
}

/**
 * Update weather component with new trip data
 */
function updateWeatherOverview(tripData, currentDay) {
  try {
    if (AppState.weatherComponent) {
      AppState.weatherComponent.loadFromTripData(tripData, currentDay);
    } else {
      console.warn('⚠️ Weather component not initialized yet');
    }
  } catch (error) {
    console.error('❌ Error updating weather overview:', error);
  }
}

/**
 * Initialize when DOM is ready
 */
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

// Export for potential use by other modules
window.PlanV2 = {
  AppState,
  selectDay,
  switchTab,
  toggleTheme,
  showStatusBanner
};
