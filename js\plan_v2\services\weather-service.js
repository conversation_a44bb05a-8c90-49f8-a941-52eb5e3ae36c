/**
 * Weather Service - Preserved from working implementation
 * Integrates with Open-Meteo API to fetch weather data for trip locations
 * 
 * This service is working correctly and should be preserved as-is.
 * It will be integrated into the new plan_v2 architecture in Phase 2.
 */

/**
 * Fetch weather data from Open-Meteo API
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {number} forecastDays - Number of forecast days (default: 3)
 * @returns {Promise<Object>} - Weather data
 */
export async function fetchWeatherData(lat, lng, forecastDays = 3) {
  try {
    // Validate coordinates
    if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
      throw new Error(`Invalid coordinates: lat=${lat}, lng=${lng}`);
    }

    // Updated Open-Meteo API URL with correct parameters
    const url = `https://api.open-meteo.com/v1/forecast?latitude=${lat}&longitude=${lng}&daily=temperature_2m_max,temperature_2m_min,precipitation_probability_max,wind_speed_10m_max,sunrise,sunset&forecast_days=${forecastDays}&timezone=auto`;

    console.log('🌤️ Fetching weather data from:', url);
    console.log('📍 Coordinates:', { lat, lng, forecastDays });

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Weather API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Weather API response received:', data);

    // Validate that we have the expected data structure
    if (!data.daily || !data.daily.time || !Array.isArray(data.daily.time)) {
      throw new Error('Invalid weather data structure received from API');
    }

    return data;
  } catch (error) {
    console.error('❌ Error fetching weather data:', error);
    throw error;
  }
}

/**
 * Transform Open-Meteo API response to match our JSON structure
 * @param {Object} weatherData - Raw weather data from Open-Meteo API
 * @param {number} dayIndex - Index of the day to extract (0 = today)
 * @returns {Object} - Transformed weather data
 */
export function transformWeatherData(weatherData, dayIndex = 0) {
  try {
    const daily = weatherData.daily;

    if (!daily || !daily.time || dayIndex >= daily.time.length) {
      throw new Error('Invalid weather data or day index out of range');
    }

    // Extract data for the specified day with null checks
    const temperatureMax = daily.temperature_2m_max?.[dayIndex];
    const temperatureMin = daily.temperature_2m_min?.[dayIndex];
    const precipitation = daily.precipitation_probability_max?.[dayIndex] || 0;
    const windSpeed = daily.wind_speed_10m_max?.[dayIndex] || 0;
    const sunrise = daily.sunrise?.[dayIndex];
    const sunset = daily.sunset?.[dayIndex];

    // Use max temperature, fallback to min if max not available
    const temperature = temperatureMax !== undefined ? temperatureMax : temperatureMin;

    if (temperature === undefined) {
      throw new Error('No temperature data available');
    }

    // Determine weather condition and icon based on precipitation and temperature
    let condition = 'Sunny';
    let icon = 'fa-sun';

    if (precipitation > 70) {
      condition = 'Rainy';
      icon = 'fa-cloud-rain';
    } else if (precipitation > 30) {
      condition = 'Partly Cloudy';
      icon = 'fa-cloud-sun';
    } else if (temperature < 15) {
      condition = 'Cool';
      icon = 'fa-cloud';
    }

    // Format sunrise and sunset times
    const formatTime = (isoString) => {
      if (!isoString) return 'N/A';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return 'N/A';
        return date.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      } catch (e) {
        return 'N/A';
      }
    };

    return {
      condition,
      temperature: `${Math.round(temperature)}°C`,
      icon,
      humidity: `${Math.round(precipitation)}%`, // Using precipitation as humidity approximation
      wind: `${Math.round(windSpeed)} km/h`,
      sunrise: formatTime(sunrise),
      sunset: formatTime(sunset)
    };
  } catch (error) {
    console.error('Error transforming weather data:', error);
    console.error('Weather data received:', weatherData);
    console.error('Day index:', dayIndex);

    // Return fallback weather data
    return {
      condition: 'Unknown',
      temperature: 'N/A',
      icon: 'fa-question',
      humidity: 'N/A',
      wind: 'N/A',
      sunrise: 'N/A',
      sunset: 'N/A'
    };
  }
}

/**
 * Get weather data for multiple days
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {number} days - Number of days
 * @returns {Promise<Array>} - Array of weather data for each day
 */
export async function getWeatherForDays(lat, lng, days = 3) {
  try {
    const weatherData = await fetchWeatherData(lat, lng, days);
    const weatherDays = [];

    for (let i = 0; i < days && i < weatherData.daily.time.length; i++) {
      const dayWeather = transformWeatherData(weatherData, i);
      const date = new Date(weatherData.daily.time[i]);

      weatherDays.push({
        date: date.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        }),
        ...dayWeather
      });
    }

    return weatherDays;
  } catch (error) {
    console.error('Error getting weather for days:', error);

    // Return fallback data
    const fallbackDays = [];
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);

      fallbackDays.push({
        date: date.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        }),
        condition: 'Unknown',
        temperature: 'N/A',
        icon: 'fa-question',
        humidity: 'N/A',
        wind: 'N/A',
        sunrise: 'N/A',
        sunset: 'N/A'
      });
    }

    return fallbackDays;
  }
}

/**
 * Cache weather data to avoid excessive API calls
 */
const weatherCache = new Map();
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

/**
 * Get cached weather data or fetch new data
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {number} days - Number of days
 * @returns {Promise<Array>} - Weather data
 */
export async function getCachedWeatherData(lat, lng, days = 3) {
  try {
    // Validate inputs
    if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
      throw new Error(`Invalid coordinates for weather cache: lat=${lat}, lng=${lng}`);
    }

    const cacheKey = `${lat.toFixed(2)},${lng.toFixed(2)},${days}`;
    const cached = weatherCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log('🗄️ Using cached weather data for:', cacheKey);
      return cached.data;
    }

    console.log('🌐 Fetching fresh weather data for:', cacheKey);
    const weatherData = await getWeatherForDays(lat, lng, days);

    if (!weatherData || weatherData.length === 0) {
      throw new Error('No weather data received from API');
    }

    weatherCache.set(cacheKey, {
      data: weatherData,
      timestamp: Date.now()
    });

    console.log('💾 Weather data cached successfully for:', cacheKey);
    return weatherData;
  } catch (error) {
    console.error('❌ Error in getCachedWeatherData:', error);

    // If we have expired cached data, use it as fallback
    const cacheKey = `${lat.toFixed(2)},${lng.toFixed(2)},${days}`;
    const cached = weatherCache.get(cacheKey);
    if (cached) {
      console.log('🔄 Using expired cached weather data as fallback');
      return cached.data;
    }

    throw error;
  }
}

/**
 * Extract coordinates from trip data for a specific day
 * @param {Object} tripData - Complete trip data from get_trip_plan_json
 * @param {number} currentDay - Current day number
 * @returns {Object|null} - Coordinates object {lat, lng} or null if not found
 */
export function extractCoordinatesFromTripData(tripData, currentDay) {
  try {
    if (!tripData || !tripData.days || !Array.isArray(tripData.days)) {
      console.warn('🌤️ Invalid trip data structure for weather coordinates');
      return null;
    }

    // Find the day data for the current day
    const dayData = tripData.days.find(day => day.day_number === currentDay);

    if (!dayData) {
      console.warn(`🌤️ No data found for day ${currentDay}`);
      return null;
    }

    let coordinates = null;
    let source = '';

    // Try to extract coordinates from lodging first (most reliable)
    if (dayData.lodging && dayData.lodging.location && dayData.lodging.location.coordinates) {
      const coords = dayData.lodging.location.coordinates;
      if (coords.lat && coords.lng && !isNaN(coords.lat) && !isNaN(coords.lng)) {
        coordinates = {
          lat: parseFloat(coords.lat),
          lng: parseFloat(coords.lng)
        };
        source = 'lodging coordinates';
      }
    }

    // Fallback to location coordinates if available
    if (!coordinates && dayData.location) {
      if (dayData.location.latitude && dayData.location.longitude) {
        coordinates = {
          lat: parseFloat(dayData.location.latitude),
          lng: parseFloat(dayData.location.longitude)
        };
        source = 'location coordinates';
      }
    }

    // Fallback to city-specific coordinates
    if (!coordinates && dayData.location && dayData.location.city) {
      const cityCoords = getCityCoordinates(dayData.location.city);
      if (cityCoords) {
        coordinates = cityCoords;
        source = `city-specific (${dayData.location.city})`;
      }
    }

    // Final fallback to Marrakech
    if (!coordinates) {
      coordinates = { lat: 31.6295, lng: -7.9811 };
      source = 'fallback (Marrakech)';
    }

    console.log(`🌤️ Weather coordinates for day ${currentDay}: ${coordinates.lat}, ${coordinates.lng} (${source})`);
    return coordinates;

  } catch (error) {
    console.error('❌ Error extracting coordinates from trip data:', error);
    // Return fallback coordinates
    return { lat: 31.6295, lng: -7.9811 };
  }
}

/**
 * Get city-specific coordinates for major Moroccan cities
 * @param {string} cityName - Name of the city
 * @returns {Object|null} - Coordinates object or null
 */
function getCityCoordinates(cityName) {
  const cityCoordinatesMap = {
    'Marrakech': { lat: 31.626906, lng: -7.988231 },
    'Essaouira': { lat: 31.526234, lng: -9.666660 },
    'Rabat': { lat: 33.996205, lng: -6.833700 },
    'Tetouan': { lat: 35.612827, lng: -5.330951 },
    'Tangier': { lat: 35.708452, lng: -5.819821 },
    'Fez': { lat: 34.011821, lng: -4.998060 },
    'Casablanca': { lat: 33.566700, lng: -7.609233 },
    'Agadir': { lat: 30.399682, lng: -9.514539 },
    'Chefchaouen': { lat: 35.171134, lng: -5.272534 },
    'Merzouga': { lat: 31.121202, lng: -4.007633 }
  };

  return cityCoordinatesMap[cityName] || null;
}

/**
 * Get weather data from trip data for a specific day
 * @param {Object} tripData - Complete trip data from get_trip_plan_json
 * @param {number} currentDay - Current day number
 * @param {number} forecastDays - Number of forecast days (default: 3)
 * @returns {Promise<Array>} - Weather data array
 */
export async function getWeatherFromTripData(tripData, currentDay, forecastDays = 3) {
  try {
    console.log(`🌤️ Getting weather data for trip day ${currentDay}`);

    // Extract coordinates from trip data
    const coordinates = extractCoordinatesFromTripData(tripData, currentDay);

    if (!coordinates) {
      throw new Error('Unable to extract coordinates from trip data');
    }

    // Get weather data using the extracted coordinates
    const weatherData = await getCachedWeatherData(coordinates.lat, coordinates.lng, forecastDays);

    console.log(`✅ Weather data loaded for day ${currentDay}:`, weatherData);
    return weatherData;

  } catch (error) {
    console.error('❌ Error getting weather from trip data:', error);

    // Return fallback weather data
    const fallbackDays = [];
    for (let i = 0; i < forecastDays; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);

      fallbackDays.push({
        date: date.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        }),
        condition: 'Unknown',
        temperature: 'N/A',
        icon: 'fa-question',
        humidity: 'N/A',
        wind: 'N/A',
        sunrise: 'N/A',
        sunset: 'N/A'
      });
    }

    return fallbackDays;
  }
}
