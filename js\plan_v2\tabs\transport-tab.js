/**
 * Enhanced Transport Tab Module
 *
 * Handles rendering and functionality for the transport tab using the enhanced transport system
 */

import { TransportSystem } from '../transport-system.js';
import { renderTransportDay, renderTransportStatus } from '../transport-ui.js';

// Global transport system instance (lazy loaded)
let transportSystemInstance = null;

/**
 * Get or create transport system instance
 * @param {number} tripId - Trip ID
 * @returns {Promise<TransportSystem>} Transport system instance
 */
async function getTransportSystem(tripId) {
  if (!transportSystemInstance || transportSystemInstance.currentTripId !== tripId) {
    transportSystemInstance = new TransportSystem();
    await transportSystemInstance.initialize(tripId);
  }
  return transportSystemInstance;
}

/**
 * Enhanced render transport tab with automatic calculation and enhanced UI
 * @param {Object} dayData - Day data from trip plan
 * @param {Object} AppState - Global application state
 * @returns {Promise<void>}
 */
async function renderTransportTab(dayData, AppState) {
  console.log('🚗 Enhanced transport tab rendering for day', dayData.day_number);

  const transportTab = document.getElementById('transport-tab');
  if (!transportTab) {
    console.error('❌ Transport tab element not found');
    return;
  }

  // Show loading state initially
  renderTransportStatus('calculating', 'Loading transport data...', 'transport-tab');

  try {
    // Get trip ID from AppState
    const tripId = AppState?.tripId;
    if (!tripId) {
      throw new Error('Trip ID not available in AppState');
    }

    // Get transport system instance
    const transportSystem = await getTransportSystem(tripId);

    // Check if transport data exists
    const status = transportSystem.getTransportStatus();

    if (!status.hasData) {
      console.log('⚠️ No transport data found, triggering automatic calculation...');

      // Show calculation status
      renderTransportStatus('calculating', 'Calculating transport routes and costs...', 'transport-tab');

      // Automatically calculate transport data
      const calcResult = await transportSystem.calculateTransport(false);

      if (!calcResult.success) {
        throw new Error(`Transport calculation failed: ${calcResult.error}`);
      }

      console.log('✅ Transport calculation completed automatically');
    }

    // Get transport data for the current day
    const transportDataResult = transportSystem.getTransportDataForUI(dayData.day_number);

    if (!transportDataResult.success) {
      throw new Error(`Failed to get transport data: ${transportDataResult.error}`);
    }

    // Check if there's data for this specific day
    if (!transportDataResult.currentDay && !transportDataResult.interCity) {
      // Show no data message for this specific day
      transportTab.innerHTML = `
        <div class="no-data-message">
          <i class="fas fa-car"></i>
          <h3>No transport data for Day ${dayData.day_number}</h3>
          <p>No transport information available for this day.</p>
          <button class="btn btn-primary" onclick="window.recalculateTransport()">
            <i class="fas fa-calculator"></i> Calculate Transport
          </button>
        </div>
      `;

      // Add recalculate function to window for button access
      window.recalculateTransport = async () => {
        try {
          renderTransportStatus('calculating', 'Recalculating transport data...', 'transport-tab');
          const recalcResult = await transportSystem.calculateTransport(true);
          if (recalcResult.success) {
            // Re-render the tab after successful calculation
            await renderTransportTab(dayData, AppState);
          } else {
            throw new Error(recalcResult.error);
          }
        } catch (error) {
          console.error('❌ Transport recalculation failed:', error);
          renderTransportStatus('error', `Recalculation failed: ${error.message}`, 'transport-tab');
        }
      };

      return;
    }

    // Get all transport data for enhanced UI rendering
    const allTransportData = transportSystem.getTransportDataForUI();
    if (allTransportData.success) {
      // Use the enhanced UI rendering
      renderTransportDay(allTransportData.data, dayData.day_number, 'transport-tab');
      console.log('✅ Enhanced transport UI rendered successfully');
    } else {
      throw new Error(`Failed to get complete transport data: ${allTransportData.error}`);
    }

  } catch (error) {
    console.error('❌ Error in enhanced transport tab:', error);

    // Show user-friendly error message
    transportTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Transport System Error</h3>
        <p>${error.message}</p>
        <div class="error-actions">
          <button class="btn btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i> Refresh Page
          </button>
          <button class="btn btn-primary" onclick="window.retryTransportLoad()">
            <i class="fas fa-redo"></i> Retry
          </button>
        </div>
      </div>
    `;

    // Add retry function
    window.retryTransportLoad = () => {
      renderTransportTab(dayData, AppState).catch(retryError => {
        console.error('❌ Retry failed:', retryError);
      });
    };
  }
}

export default renderTransportTab;
