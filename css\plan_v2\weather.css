/* ===== WEATHER COMPONENT STYLES ===== */

.weather-overview {
  grid-column: 2;
}

.weather-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
}

.weather-location i {
  font-size: var(--font-size-xs);
}

.current-weather {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.weather-main {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.weather-icon {
  font-size: 3rem;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--accent-light);
  border-radius: var(--radius-full);
}

.weather-temp {
  flex: 1;
}

.temp {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  display: block;
  line-height: 1;
}

.condition {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.detail-item {
  text-align: center;
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.detail-item:hover {
  background: var(--white);
  box-shadow: var(--shadow-sm);
}

.detail-item i {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
  display: block;
}

.detail-item span {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  display: block;
  margin-bottom: var(--spacing-1);
}

.detail-item small {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.weather-forecast {
  padding: var(--spacing-6);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

.forecast-day {
  text-align: center;
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.forecast-day:hover {
  background: var(--white);
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.day-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  margin-bottom: var(--spacing-2);
}

.day-icon {
  font-size: var(--font-size-2xl);
  color: var(--accent-color);
  margin-bottom: var(--spacing-2);
}

.day-temp {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.high {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
}

.low {
  font-size: var(--font-size-base);
  color: var(--gray-500);
}

.day-condition {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  text-transform: capitalize;
}

/* Weather icon animations */
.weather-icon i,
.day-icon i {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Weather condition specific colors */
.weather-icon .fa-sun,
.day-icon .fa-sun {
  color: #fbbf24;
}

.weather-icon .fa-cloud-sun,
.day-icon .fa-cloud-sun {
  color: #60a5fa;
}

.weather-icon .fa-cloud,
.day-icon .fa-cloud {
  color: #9ca3af;
}

.weather-icon .fa-cloud-rain,
.day-icon .fa-cloud-rain {
  color: #3b82f6;
}

.weather-icon .fa-snowflake,
.day-icon .fa-snowflake {
  color: #e5e7eb;
}

/* Weather alerts */
.weather-alert {
  background: var(--warning-light);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin: var(--spacing-4) var(--spacing-6) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.weather-alert.severe {
  background: var(--error-light);
  border-color: var(--error-color);
}

.weather-alert-icon {
  color: var(--warning-color);
  font-size: var(--font-size-lg);
}

.weather-alert.severe .weather-alert-icon {
  color: var(--error-color);
}

.weather-alert-text {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  margin: 0;
}

/* Weather loading states */
.weather-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

.weather-loading i {
  font-size: var(--font-size-2xl);
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-2);
}

/* Weather error states */
.weather-error {
  text-align: center;
  padding: var(--spacing-6);
  color: var(--gray-500);
}

.weather-error i {
  font-size: var(--font-size-3xl);
  color: var(--gray-400);
  margin-bottom: var(--spacing-2);
}

.weather-error h4 {
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
}

.weather-error p {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.weather-retry {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: background var(--transition-fast);
}

.weather-retry:hover {
  background: var(--primary-hover);
}

/* Responsive weather layout */
@media (max-width: 767px) {
  .weather-main {
    flex-direction: row;
    text-align: left;
    gap: var(--spacing-4);
    align-items: center;
  }

  .weather-icon {
    width: 64px;
    height: 64px;
    font-size: 2rem;
    flex-shrink: 0;
  }

  .temp {
    font-size: var(--font-size-3xl);
  }

  .weather-details {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
  }

  .detail-item {
    padding: var(--spacing-2);
    text-align: center;
  }

  .weather-forecast {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
  }

  .forecast-day {
    padding: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  .weather-main {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }

  .weather-details {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .weather-forecast {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
}

/* Weather data freshness indicator */
.weather-freshness {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  margin-top: var(--spacing-2);
  padding-top: var(--spacing-2);
  border-top: 1px solid var(--gray-200);
}

.weather-freshness i {
  font-size: var(--font-size-xs);
}

.weather-freshness.fresh {
  color: var(--success-color);
}

.weather-freshness.stale {
  color: var(--warning-color);
}

/* Weather unit toggle */
.weather-units {
  display: flex;
  background: var(--gray-100);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1);
  margin-left: auto;
}

.unit-toggle {
  background: none;
  border: none;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.unit-toggle.active {
  background: var(--white);
  color: var(--gray-900);
  box-shadow: var(--shadow-sm);
}
