/**
 * Data Manager - Real Database Integration
 *
 * This module handles all data fetching and processing for the plan page.
 * It provides clean, organized data management with real database integration.
 */

import { supabase } from '../supabaseClient.js';

/**
 * Data Manager Class
 *
 * Responsibilities:
 * - Fetch trip data from get_trip_plan_json
 * - Fetch weather data from weather service
 * - Fetch transport data from get_trip_transport_data
 * - Cache data for performance
 * - Handle API errors gracefully
 * - Provide consistent data format to components
 * - Verify authentication and trip ownership
 */
export class DataManager {
  constructor(tripId = null, userId = null) {
    this.cache = new Map();
    this.tripId = tripId;
    this.userId = userId;
    this.currentUser = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the data manager with authentication and trip validation
   * @param {number} tripId - Trip ID from URL parameters
   */
  async initialize(tripId) {
    console.log('📊 DataManager: Initializing WITHOUT authentication (TEMPORARY)...');

    try {
      // TEMPORARY: Skip authentication for debugging data loading issues
      console.log('🧪 TEMPORARY: Authentication checks disabled for plan_v2 debugging');

      // Set a mock user for compatibility
      this.currentUser = { id: 'temp-user-id' };
      this.tripId = tripId;

      console.log(`✅ DataManager: Bypassing authentication for trip ${tripId}`);

      // TEMPORARY: Skip trip ownership verification
      console.log('🧪 TEMPORARY: Skipping trip ownership verification');

      this.isInitialized = true;
      console.log('✅ DataManager: Initialization complete (authentication bypassed)');

    } catch (error) {
      console.error('❌ DataManager: Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Verify trip ownership for the current user
   * @param {number} tripId - Trip ID to verify
   * @returns {Promise<boolean>} True if user owns the trip
   */
  async verifyTripOwnership(tripId) {
    try {
      // TEMPORARY: Always return true to bypass ownership verification
      console.log(`🧪 TEMPORARY: Skipping ownership verification for trip ${tripId} (authentication disabled)`);
      console.log(`✅ TEMPORARY: Allowing access to trip ${tripId} without ownership check`);
      return true;

    } catch (error) {
      console.error('❌ Error in ownership verification (bypassed):', error);
      // TEMPORARY: Return true even on error to bypass authentication
      return true;
    }
  }

  /**
   * Load complete trip data with authentication
   * @returns {Promise<Object>} Complete trip data structure
   */
  async loadCompleteData() {
    if (!this.isInitialized) {
      throw new Error('DataManager not initialized. Call initialize() first.');
    }

    console.log(`📊 DataManager: Loading complete data for trip ${this.tripId}`);

    try {
      // Get basic trip info for date calculations
      const tripInfo = await this.getTripInfo();

      // Calculate current day
      const dayCalculation = this.calculateCurrentDay(tripInfo.start_date, tripInfo.end_date);

      // Fetch trip plan data
      const { data: tripData, error } = await supabase.rpc('get_trip_plan_json', {
        p_trip_id: this.tripId,
        p_current_day: dayCalculation.day
      });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (!tripData || !tripData.trip) {
        throw new Error('No trip data returned from database');
      }

      // Enhance with calculated day information
      const enhancedData = {
        ...tripData,
        trip: {
          ...tripData.trip,
          dates: {
            ...tripData.trip.dates,
            current_day: dayCalculation.day,
            trip_status: dayCalculation.status,
            status_message: dayCalculation.message
          }
        },
        meta: {
          calculated_day: dayCalculation.day,
          trip_status: dayCalculation.status,
          should_zero_budget: dayCalculation.shouldZeroBudget,
          calculation_date: new Date().toISOString()
        }
      };

      // Cache the data
      this.setCachedData(`trip_${this.tripId}`, enhancedData);

      console.log('✅ DataManager: Complete data loaded successfully');
      console.log('📊 Trip data structure:', enhancedData);

      return enhancedData;

    } catch (error) {
      console.error('❌ DataManager: Failed to load complete data:', error);
      throw error;
    }
  }

  /**
   * Get basic trip information
   * @returns {Promise<Object>} Trip info with dates
   */
  async getTripInfo() {
    try {
      const { data: tripInfo, error } = await supabase
        .from('trip')
        .select('id, title, start_date, end_date')
        .eq('id', this.tripId)
        .single();

      if (error) {
        throw error;
      }

      return tripInfo;

    } catch (error) {
      console.error('❌ Error fetching trip info:', error);
      throw error;
    }
  }

  /**
   * Calculate current day based on trip dates and real-world date
   * @param {string} startDate - Trip start date
   * @param {string} endDate - Trip end date
   * @returns {Object} Object containing calculated day, trip status, and metadata
   */
  calculateCurrentDay(startDate, endDate) {
    try {
      // Get current date in user's timezone
      const today = new Date();
      const currentDateStr = today.toISOString().split('T')[0]; // YYYY-MM-DD format

      // Parse trip dates
      const tripStart = new Date(startDate + 'T00:00:00');
      const tripEnd = new Date(endDate + 'T23:59:59');
      const currentDate = new Date(currentDateStr + 'T12:00:00'); // Use noon to avoid timezone issues

      // Calculate total days
      const totalDays = Math.ceil((tripEnd.getTime() - tripStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      console.log('📅 Date Calculation Details:');
      console.log('   Current date:', currentDateStr);
      console.log('   Trip start:', startDate);
      console.log('   Trip end:', endDate);
      console.log('   Total days:', totalDays);

      // Calculate the difference in days
      const timeDiffFromStart = currentDate.getTime() - tripStart.getTime();
      const daysDiffFromStart = Math.floor(timeDiffFromStart / (1000 * 60 * 60 * 24));

      // Determine trip status and appropriate day
      if (currentDate < tripStart) {
        // Future trip - show day 1 with zero budget spending
        console.log('🔮 Trip Status: FUTURE - Trip hasn\'t started yet');
        return {
          day: 1,
          status: 'future',
          message: 'Trip starts in ' + Math.abs(daysDiffFromStart) + ' days',
          shouldZeroBudget: true
        };
      } else if (currentDate > tripEnd) {
        // Past trip - show the last day
        console.log('📚 Trip Status: PAST - Trip has ended');
        return {
          day: totalDays,
          status: 'past',
          message: 'Trip ended ' + Math.abs(Math.floor((currentDate.getTime() - tripEnd.getTime()) / (1000 * 60 * 60 * 24))) + ' days ago',
          shouldZeroBudget: false
        };
      } else {
        // Active trip - calculate current day
        const currentDay = daysDiffFromStart + 1; // Add 1 because day 1 is the start date
        const validDay = Math.max(1, Math.min(currentDay, totalDays)); // Ensure day is within valid range

        console.log('🎯 Trip Status: ACTIVE - Currently on trip');
        console.log('   Days from start:', daysDiffFromStart);
        console.log('   Calculated day:', currentDay);
        console.log('   Valid day (clamped):', validDay);

        return {
          day: validDay,
          status: 'active',
          message: `Day ${validDay} of ${totalDays}`,
          shouldZeroBudget: false
        };
      }
    } catch (error) {
      console.error('❌ Error calculating current day:', error);
      // Fallback to day 1 if calculation fails
      return {
        day: 1,
        status: 'error',
        message: 'Unable to calculate current day, showing day 1',
        shouldZeroBudget: false
      };
    }
  }

  /**
   * Get data for a specific day
   * @param {number} dayNumber - Day number to get data for
   * @returns {Promise<Object>} Day-specific data
   */
  async getDayData(dayNumber) {
    try {
      const cacheKey = `day_${this.tripId}_${dayNumber}`;
      const cached = this.getCachedData(cacheKey);

      if (cached) {
        console.log(`📊 DataManager: Using cached data for day ${dayNumber}`);
        return cached;
      }

      // Load complete data if not cached
      const completeData = await this.loadCompleteData();

      if (!completeData.days || !Array.isArray(completeData.days)) {
        throw new Error('No days data available');
      }

      const dayData = completeData.days.find(day => day.day_number === dayNumber);

      if (!dayData) {
        throw new Error(`No data found for day ${dayNumber}`);
      }

      // Cache the day data
      this.setCachedData(cacheKey, dayData);

      return dayData;

    } catch (error) {
      console.error(`❌ Error getting data for day ${dayNumber}:`, error);
      throw error;
    }
  }

  /**
   * Extract trip ID from URL parameters
   * @returns {number|null} Trip ID or null if not found
   */
  static extractTripIdFromUrl() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const tripIdParam = urlParams.get('id');

      if (!tripIdParam) {
        console.error('❌ No trip ID found in URL parameters');
        return null;
      }

      const tripId = parseInt(tripIdParam, 10);

      if (isNaN(tripId)) {
        console.error('❌ Invalid trip ID in URL parameters:', tripIdParam);
        return null;
      }

      console.log(`✅ Extracted trip ID from URL: ${tripId}`);
      return tripId;

    } catch (error) {
      console.error('❌ Error extracting trip ID from URL:', error);
      return null;
    }
  }

  /**
   * Get cached data
   * @param {string} key - Cache key
   * @returns {any} Cached data or null
   */
  getCachedData(key) {
    const cached = this.cache.get(key);

    if (!cached) {
      return null;
    }

    // Check if expired
    if (cached.expires < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Set cached data
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} ttl - Time to live in milliseconds
   */
  setCachedData(key, data, ttl = 300000) { // 5 minutes default
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    });
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (value.expires < now) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear all cached data
   */
  clearAllCache() {
    this.cache.clear();
    console.log('🗑️ DataManager: All cache cleared');
  }

  /**
   * Extract restaurant images from trip data (DEPRECATED - images now included in get_trip_plan_json)
   * @param {Object} restaurant - Restaurant object from trip data
   * @returns {Array} Array of image URLs
   */
  extractRestaurantImages(restaurant) {
    console.log('🖼️ Extracting restaurant images from trip data');

    if (!restaurant || !restaurant.images) {
      console.log('📷 No restaurant images found in trip data');
      return [];
    }

    const imageUrls = restaurant.images
      .map(img => img.url)
      .filter(url => url && url.trim().length > 0);

    console.log(`✅ Extracted ${imageUrls.length} images from restaurant data`);
    return imageUrls;
  }

  /**
   * Extract accommodation images from trip data (DEPRECATED - images now included in get_trip_plan_json)
   * @param {Object} lodging - Lodging object from trip data
   * @returns {Array} Array of image URLs
   */
  extractAccommodationImages(lodging) {
    console.log('🖼️ Extracting accommodation images from trip data');

    if (!lodging) {
      console.log('📷 No lodging data provided');
      return [];
    }

    // Check for image in lodging.image.url format (from get_trip_plan_json)
    if (lodging.image && lodging.image.url) {
      console.log(`✅ Found accommodation image: ${lodging.image.url.substring(0, 50)}...`);
      return [lodging.image.url];
    }

    // Fallback: check for images array
    if (lodging.images && Array.isArray(lodging.images)) {
      const imageUrls = lodging.images
        .map(img => img.url)
        .filter(url => url && url.trim().length > 0);

      console.log(`✅ Extracted ${imageUrls.length} images from accommodation data`);
      return imageUrls;
    }

    console.log('📷 No accommodation images found in trip data');
    return [];
  }

  /**
   * Load accommodation reviews from database
   * @param {number} accommodationId - Accommodation ID
   * @returns {Promise<Array>} Array of reviews
   */
  async loadAccommodationReviews(accommodationId) {
    if (!accommodationId) {
      console.log('📝 No accommodation ID provided for review loading');
      return [];
    }

    try {
      console.log(`📝 Loading reviews for accommodation ${accommodationId}`);

      // Fixed query structure based on the provided SQL pattern
      const { data: reviews, error } = await supabase
        .from('review_entry')
        .select(`
          author,
          review_text,
          rating
        `)
        .in('review_id',
          supabase
            .from('review')
            .select('id')
            .eq('entity_type', 'accommodation')
            .eq('language_tag', 'en')
            .eq('entity_id', accommodationId.toString())
        )
        .limit(10);

      if (error) {
        console.warn('⚠️ Error loading accommodation reviews:', error.message);
        console.warn('Error details:', error);
        return [];
      }

      if (!reviews || reviews.length === 0) {
        console.log('📝 No reviews found for accommodation');
        return [];
      }

      // Process and normalize reviews
      const processedReviews = reviews
        .filter(review => review.review_text && review.review_text.trim().length > 0)
        .map(review => ({
          text: review.review_text,
          rating: review.rating / 2000, // Normalize rating from 0-10000 to 0-5
          reviewerName: review.author || 'Anonymous',
          date: new Date().toISOString(), // Placeholder since date not in this table structure
          helpfulCount: 0 // Placeholder since helpful_count not in this table structure
        }));

      console.log(`✅ Loaded ${processedReviews.length} reviews for accommodation`);
      return processedReviews;

    } catch (error) {
      console.error('❌ Error loading accommodation reviews:', error);
      console.error('Error details:', error);
      return [];
    }
  }
}
