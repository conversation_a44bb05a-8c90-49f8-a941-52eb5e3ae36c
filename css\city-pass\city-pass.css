:root {
    --color-primary: #F26522;
    --color-primary-rgb: 242, 101, 34; /* RGB values for primary color */
    --color-primary-hover: #e05a1a;
    --color-secondary: #3F20FB;
    --color-secondary-rgb: 63, 32, 251; /* RGB values for secondary color */
    --color-text: #1f2937;
    --color-text-inverse: #ffffff;
    --color-text-muted: #6b7280;
    --color-bg: #ffffff;
    --color-bg-alt: #f9fafb;
    --color-bg-accent: #f3f4f6;
    --color-border: #e5e7eb;
    --control-bg: rgba(255, 255, 255, 0.9);
    --control-color: #333;
  }

  .dark {
    --color-primary: #F26522;
    --color-primary-rgb: 242, 101, 34; /* RGB values for primary color */
    --color-primary-hover: #e05a1a;
    --color-secondary: #FFB347;
    --color-secondary-rgb: 255, 179, 71; /* RGB values for secondary color */
    --color-text: #f3f4f6;
    --color-text-inverse: #ffffff;
    --color-text-muted: #9ca3af;
    --color-bg: #1f2937;
    --color-bg-alt: #111827;
    --color-bg-accent: #374151;
    --color-border: #374151;
    --control-bg: rgba(31, 41, 55, 0.9);
    --control-color: #e5e7eb;
  }

  /* Transitions for theme switching */
  html.transition,
  html.transition *,
  html.transition *:before,
  html.transition *:after {
    transition: all 0.3s ease-in-out !important;
    transition-delay: 0 !important;
  }

  /* Scroll progress bar */
  .scroll-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: rgba(229, 231, 235, 0.3);
    z-index: 100;
  }

  .scroll-progress-bar {
    height: 100%;
    background-color: var(--color-primary);
    width: 0%;
    transition: width 0.1s ease-out;
  }

  /* Chart animations */
  .chart-bar {
    transition: height 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* Circular progress animation */
  #pass-price-circle {
    transition: stroke-dashoffset 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* Savings amount and percentage transitions */
  #savings-amount, #savings-percentage {
    transition: all 0.3s ease;
  }

  /* Custom styling for details/summary (FAQ accordion) */
  details summary::-webkit-details-marker {
    display: none;
  }

  /* Sticky CTA on mobile */
  @media (max-width: 768px) {
    .mobile-sticky-cta {
      position: sticky;
      bottom: 0;
      z-index: 10;
    }
  }

  /* Dark mode specific overrides */
  html.dark #city-pass-carousel-section {
    background: linear-gradient(180deg, var(--color-bg) 0%, var(--color-bg-alt) 100%);
  }

  html.dark .control-button {
    background: var(--control-bg);
  }

  html.dark .control-button svg {
    stroke: var(--control-color);
  }

  html.dark .decoration-blob:first-child {
    background: rgba(59, 130, 246, 0.2); /* blue-500 with opacity */
  }

  html.dark .decoration-blob:last-child {
    background: rgba(249, 115, 22, 0.2); /* orange-500 with opacity */
  }

  html.dark .card-overlay {
    background: linear-gradient(to top, rgba(0,0,0,0.95) 0%, rgba(0,0,0,0.8) 50%, rgba(0,0,0,0.2) 100%);
  }

  html.dark .pagination-dot {
    background-color: #4b5563; /* gray-600 */
  }

  html.dark .pagination-dot.active {
    background-color: var(--color-primary);
  }

/* Enhanced Hero Section Styles */
#hero-section {
  position: relative;
  overflow: hidden;
  border-radius: 0 0 2rem 2rem;
}

/* Parallax effect */
.hero-parallax-container {
  will-change: transform;
}

.hero-bg-layer {
  transform-origin: center;
  will-change: transform;
  transition: transform 0.5s ease-out;
}

/* Animated particles overlay */
.hero-particles {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="white" opacity="0.3"/><circle cx="30" cy="40" r="0.5" fill="white" opacity="0.3"/><circle cx="50" cy="20" r="0.8" fill="white" opacity="0.3"/><circle cx="70" cy="70" r="0.6" fill="white" opacity="0.3"/><circle cx="90" cy="30" r="0.7" fill="white" opacity="0.3"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.3"/><circle cx="60" cy="90" r="0.9" fill="white" opacity="0.3"/><circle cx="80" cy="60" r="0.7" fill="white" opacity="0.3"/></svg>');
  background-size: 150px 150px;
  animation: particlesDrift 60s linear infinite;
}

@keyframes particlesDrift {
  0% { background-position: 0 0; }
  100% { background-position: 300px 300px; }
}

/* Wave divider */
.wave-divider {
  width: 100%;
  height: 120px;
  display: block;
}

.wave-path {
  transition: fill 0.3s ease;
}

/* Text gradient effect */
.text-gradient {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Title glow effect */
.hero-title-glow {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3),
               0 0 20px rgba(255, 255, 255, 0.2),
               0 0 30px rgba(255, 255, 255, 0.1);
}

/* Staggered animations */
.hero-badge-animation {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards 0.2s;
}

.hero-text-reveal h1 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards 0.4s;
}

.hero-text-reveal p {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards 0.6s;
}

.hero-cta-animation {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards 0.8s;
}

.hero-trust-animation {
  opacity: 0;
  animation: fadeIn 1s ease forwards 1.2s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Button hover effects */
.hero-primary-btn {
  box-shadow: 0 4px 14px rgba(242, 101, 34, 0.3);
  will-change: transform;
}

.hero-primary-btn:hover {
  box-shadow: 0 6px 20px rgba(242, 101, 34, 0.4);
}

.hero-secondary-btn {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Parallax scroll effect */
@media (min-width: 768px) {
  #hero-section {
    perspective: 1000px;
  }

  .hero-bg-layer {
    transform: translateZ(-50px) scale(1.5);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .hero-text-reveal h1 {
    font-size: 2.5rem;
  }

  .hero-text-reveal p {
    font-size: 1rem;
  }

  .hero-trust-animation {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Section highlight animation */
@keyframes sectionHighlight {
  0% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0); }
  20% { box-shadow: 0 0 30px 5px rgba(var(--color-primary-rgb), 0.3); }
  100% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0); }
}

.highlight-section {
  position: relative;
  z-index: 1;
  animation: sectionHighlight 2s ease-out;
}

/* Benefits section animations */
@keyframes floatAnimation {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

#benefits-container .fas {
  transition: transform 0.3s ease;
}

#benefits-container > div:hover .fas {
  transform: scale(1.2);
}

/* Staggered animation for benefits cards */
#benefits-container > div {
  opacity: 0;
  transform: translateY(20px);
}

#benefits-container > div:nth-child(1) {
  animation: fadeInUp 0.6s ease forwards 0.2s;
}

#benefits-container > div:nth-child(2) {
  animation: fadeInUp 0.6s ease forwards 0.4s;
}

#benefits-container > div:nth-child(3) {
  animation: fadeInUp 0.6s ease forwards 0.6s;
}

/* Savings section animations */
.flex-col.lg\:flex-row > div {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease forwards;
}

.flex-col.lg\:flex-row > div:nth-child(1) {
  animation-delay: 0.3s;
}

.flex-col.lg\:flex-row > div:nth-child(2) {
  animation-delay: 0.5s;
}

.flex-col.lg\:flex-row > div:nth-child(3) {
  animation-delay: 0.7s;
}

/* Savings summary animation */
.mt-12.max-w-lg {
  opacity: 0;
  animation: fadeInUp 0.8s ease forwards 1s;
}