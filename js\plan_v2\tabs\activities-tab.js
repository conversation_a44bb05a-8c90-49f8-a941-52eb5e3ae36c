/**
 * Activities Tab Module
 * 
 * Handles rendering and functionality for the activities tab
 */

/**
 * Render activities tab with real data
 * @param {Object} dayData - Day data from trip plan
 * @param {Object} AppState - Global application state (unused but kept for consistency)
 * @returns {void}
 */
function renderActivitiesTab(dayData, AppState) {
  console.log('🎯 Rendering activities tab for day', dayData.day_number);

  const activitiesTab = document.getElementById('activities-tab');
  if (!activitiesTab) return;

  try {
    if (!dayData.activities || dayData.activities.length === 0) {
      activitiesTab.innerHTML = `
        <div class="no-data-message">
          <i class="fas fa-map-marker-alt"></i>
          <h3>No activity data</h3>
          <p>No activities scheduled for this day.</p>
        </div>
      `;
      return;
    }

    const activitiesHtml = dayData.activities.map(activity => `
      <div class="activity-card">
        <div class="activity-content">
          <div class="activity-header">
            <h3>${activity.name || 'Activity'}</h3>
            <div class="activity-time">${activity.time || 'Time TBD'}</div>
            <div class="activity-duration">${activity.duration || 'Duration TBD'}</div>
          </div>

          <div class="activity-details">
            <div class="detail-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>${activity.location?.address || activity.location?.city || 'Location not specified'}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-users"></i>
              <span>${activity.group_size || 'Small Group'}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-language"></i>
              <span>${activity.languages || 'English'}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-star"></i>
              <span>${activity.rating?.score || 'N/A'}/5 (${activity.rating?.review_count || 0} reviews)</span>
            </div>
          </div>

          <div class="activity-description">
            <p>${activity.description || 'Exciting activity to explore the local culture and attractions.'}</p>
            ${activity.highlights ? `
              <div class="activity-highlights">
                <h4>Activity Highlights:</h4>
                <ul>
                  ${activity.highlights.map(highlight => `<li>${highlight}</li>`).join('')}
                </ul>
              </div>
            ` : ''}
          </div>

          <div class="activity-pricing">
            <div class="price-info">
              <span class="price">$${activity.pricing?.price_per_person || 'N/A'}</span>
              <span class="period">/ person</span>
            </div>
            <div class="activity-actions">
              <button class="btn btn-outline">View Details</button>
              <button class="btn btn-primary">Modify Booking</button>
            </div>
          </div>
        </div>
      </div>
    `).join('');

    activitiesTab.innerHTML = activitiesHtml;

  } catch (error) {
    console.error('❌ Error rendering activities tab:', error);
    activitiesTab.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error loading activity data</h3>
        <p>Please try refreshing the page.</p>
      </div>
    `;
  }
}

export default renderActivitiesTab;
