<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Trips - Morocco Travel Planner</title>
    <link rel="stylesheet" href="../css/global.css">
    <link rel="stylesheet" href="../css/trips.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Loading your trips...</p>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Header -->
        <header class="trips-header">
            <div class="header-content">
                <div class="header-left">
                    <button class="back-button" onclick="window.location.href='./account.html'">
                        <i class="fas fa-arrow-left"></i>
                        Back to Account
                    </button>
                    <h1>My Trips</h1>
                </div>
                <div class="header-right">
                    <button class="primary-button" id="create-trip-btn">
                        <i class="fas fa-plus"></i>
                        Create New Trip
                    </button>
                </div>
            </div>
        </header>

        <!-- Filters -->
        <div class="trips-filters">
            <div class="filter-group">
                <span class="filter-label">Filter by:</span>
                <div class="filter-options">
                    <button class="filter-option active" data-filter="all">All</button>
                    <button class="filter-option" data-filter="upcoming">Upcoming</button>
                    <button class="filter-option" data-filter="past">Past</button>
                    <button class="filter-option" data-filter="draft">Drafts</button>
                </div>
            </div>
            <div class="filter-group">
                <span class="filter-label">Sort by:</span>
                <div class="filter-options">
                    <button class="filter-option active" data-sort="date">Date</button>
                    <button class="filter-option" data-sort="title">Title</button>
                    <button class="filter-option" data-sort="budget">Budget</button>
                </div>
            </div>
        </div>

        <!-- Trips Grid -->
        <div class="trips-grid" id="trips-grid">
            <!-- Trip cards will be populated dynamically -->
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="empty-state" style="display: none;">
            <div class="empty-state-icon">
                <i class="fas fa-route"></i>
            </div>
            <h3>No trips yet</h3>
            <p>You haven't created any trips yet. Start planning your next adventure!</p>
            <button class="primary-button" onclick="window.location.href='./wizard.html'">
                <i class="fas fa-plus"></i>
                Create Your First Trip
            </button>
        </div>

        <!-- Error State -->
        <div class="error-state" id="error-state" style="display: none;">
            <div class="error-state-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3>Error Loading Trips</h3>
            <p id="error-message">Something went wrong while loading your trips.</p>
            <button class="secondary-button" onclick="window.location.reload()">
                <i class="fas fa-redo"></i>
                Try Again
            </button>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script type="module" src="../js/pages/trips/index.js"></script>
</body>
</html>
