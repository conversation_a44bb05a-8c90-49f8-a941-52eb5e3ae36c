/* Base styles and variables */
:root {
  --color-primary: #F97316;
  --color-primary-light: #FDBA74;
  --color-primary-dark: #C2410C;
  --color-secondary: #14B8A6;
  --color-secondary-light: #5EEAD4;
  --color-accent: #3B82F6;
  --color-accent-light: #93C5FD;
  --color-success: #22C55E;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;

  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.75rem;
  --spacing-8: 2.25rem;
  --spacing-10: 2.75rem;
  --spacing-12: 3.5rem;
  --spacing-16: 4.5rem;

  --transition-fast: 200ms;
  --transition-normal: 350ms;
  --transition-slow: 550ms;

  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;

  /* Enhanced card styling variables */
  --card-bg-light: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  --card-bg-dark: linear-gradient(145deg, #1e293b 0%, #334155 100%);
  --card-border-light: rgba(255, 255, 255, 0.2);
  --card-border-dark: rgba(255, 255, 255, 0.1);
  --card-shadow-light: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(255, 255, 255, 0.05);
  --card-shadow-dark: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
  --card-shadow-hover-light: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
  --card-shadow-hover-dark: 0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.15);

  /* Gradient variables */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, #0d9488 100%);
  --gradient-accent: linear-gradient(135deg, var(--color-accent) 0%, #1d4ed8 100%);
  --gradient-rainbow: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
}

/* Dark mode styles */
.dark {
  --color-primary: #F97316;
  --color-primary-light: #FB923C;
  --color-primary-dark: #EA580C;
  --color-secondary: #14B8A6;
  --color-secondary-light: #2DD4BF;
  --color-accent: #3B82F6;
  --color-accent-light: #60A5FA;
  --color-success: #22C55E;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-gray-50: #18212F;
  --color-gray-100: #1E293B;
  --color-gray-200: #334155;
  --color-gray-300: #475569;
  --color-gray-400: #64748B;
  --color-gray-500: #94A3B8;
  --color-gray-600: #CBD5E1;
  --color-gray-700: #E2E8F0;
  --color-gray-800: #F1F5F9;
  --color-gray-900: #F8FAFC;

  background-color: var(--color-gray-50);
  color: var(--color-gray-800);
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: var(--font-sans);
  font-size: 16px;
  line-height: 1.5;
  color: var(--color-gray-800);
  background-color: var(--color-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

html::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

body {
  overflow-y: scroll;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

body::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

img {
  max-width: 100%;
  height: auto;
}

a {
  color: inherit;
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary);
}

button {
  cursor: pointer;
  font-family: inherit;
  background: none;
  border: none;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Utility Classes */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  border: 1px solid var(--color-primary);
  position: relative;
  overflow: hidden;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, #9a3412 100%);
  border-color: var(--color-primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(249, 115, 22, 0.4);
}

.btn-outline {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  color: var(--color-gray-700);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
  transition: left 0.5s ease;
}

.btn-outline:hover::before {
  left: 100%;
}

.dark .btn-outline {
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
  border-color: rgba(71, 85, 105, 0.8);
  color: var(--color-gray-300);
}

.btn-outline:hover {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  border-color: var(--color-primary);
  transform: translateY(-3px);
  box-shadow: 0 8px 16px -4px rgba(249, 115, 22, 0.3);
}

.dark .btn-outline:hover {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  border-color: var(--color-primary);
}

.btn-link {
  color: var(--color-primary);
  font-weight: 500;
  padding: 0;
  background: none;
  border: none;
  transition: color var(--transition-fast), transform var(--transition-fast);
}

.btn-link:hover {
  text-decoration: underline;
  color: var(--color-primary-dark);
  transform: translateY(-1px);
}

.btn-block {
  display: flex;
  width: 100%;
  justify-content: center;
}

.card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  padding: var(--spacing-6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .card {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  color: var(--color-gray-700);
  border-color: rgba(255, 255, 255, 0.1);
}

.card:hover {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(249, 115, 22, 0.3);
}

.card:hover::before {
  opacity: 1;
}

.dark .card:hover {
  border-color: rgba(249, 115, 22, 0.4);
}

/* Add smooth entrance animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.card {
  animation: fadeInUp 0.6s ease-out;
}

.budget-category {
  animation: scaleIn 0.4s ease-out;
}

.weather-day {
  animation: slideInRight 0.5s ease-out;
}

/* Utility classes for Tailwind-style usage */
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.text-primary {
  color: var(--color-primary);
}

/* Day Content Tabs Styles */
.day-content-tabs {
  margin-top: var(--spacing-6);
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--card-shadow-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.dark .day-content-tabs {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: var(--card-shadow-dark);
}

.day-tabs-navigation {
  display: flex;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-2);
  gap: var(--spacing-1);
}

.dark .day-tabs-navigation {
  background: linear-gradient(135deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);
  border-bottom-color: var(--color-gray-400);
}

.day-tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  font-weight: 500;
  color: var(--color-gray-600);
  background: transparent;
  border: 1px solid transparent;
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  flex: 1;
  justify-content: center;
  min-height: 48px;
}

.day-tab-button i {
  font-size: 1.1rem;
  transition: transform var(--transition-fast);
}

.day-tab-button span {
  font-size: 0.9rem;
  font-weight: 500;
}

.day-tab-button:hover {
  color: var(--color-primary);
  background: linear-gradient(145deg, rgba(249, 115, 22, 0.05) 0%, rgba(249, 115, 22, 0.1) 100%);
  border-color: rgba(249, 115, 22, 0.2);
  transform: translateY(-2px);
}

.day-tab-button:hover i {
  transform: scale(1.1);
}

.day-tab-button.active {
  color: white;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  border-color: var(--color-primary);
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.3);
  transform: translateY(-2px);
}

.day-tab-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.day-tabs-content {
  padding: var(--spacing-6);
}

.day-tab-pane {
  display: none;
  animation: fadeInUp 0.4s ease-out;
}

.day-tab-pane.active {
  display: block;
}

/* Accommodations Content Styles */
.accommodations-content {
  display: grid;
  gap: var(--spacing-6);
}

.accommodation-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.dark .accommodation-card {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-400);
}

.accommodation-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.accommodation-header {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.accommodation-header img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.accommodation-card:hover .accommodation-header img {
  transform: scale(1.05);
}

.accommodation-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.accommodation-content {
  padding: var(--spacing-5);
}

.accommodation-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
}

.dark .accommodation-title {
  color: var(--color-gray-200);
}

.accommodation-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-4);
}

.accommodation-location i {
  color: var(--color-primary);
}

.accommodation-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.accommodation-stars {
  display: flex;
  gap: var(--spacing-1);
}

.accommodation-stars i {
  color: var(--color-warning);
  font-size: 0.9rem;
}

.accommodation-score {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 600;
  color: var(--color-gray-700);
}

.accommodation-amenities {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.amenity-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-gray-100);
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  color: var(--color-gray-600);
}

.dark .amenity-tag {
  background: var(--color-gray-300);
  color: var(--color-gray-700);
}

.amenity-tag i {
  font-size: 0.7rem;
  color: var(--color-primary);
}

.accommodation-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
}

.dark .accommodation-pricing {
  border-top-color: var(--color-gray-400);
}

.accommodation-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-primary);
}

.accommodation-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* Restaurants Content Styles */
.restaurants-content {
  display: grid;
  gap: var(--spacing-4);
}

.restaurants-grid {
  display: grid;
  gap: var(--spacing-4);
}

.restaurant-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.dark .restaurant-card {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-400);
}

.restaurant-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.restaurant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  border-bottom: 1px solid var(--color-gray-200);
}

.dark .restaurant-header {
  background: linear-gradient(135deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);
  border-bottom-color: var(--color-gray-400);
}

.meal-type-badge {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.restaurant-time {
  font-size: 0.9rem;
  color: var(--color-gray-600);
  font-weight: 500;
}

.restaurant-content {
  padding: var(--spacing-4);
}

.restaurant-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
}

.dark .restaurant-name {
  color: var(--color-gray-200);
}

.restaurant-location,
.restaurant-cuisine {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-2);
  font-size: 0.9rem;
}

.restaurant-location i,
.restaurant-cuisine i {
  color: var(--color-primary);
  width: 16px;
}

.restaurant-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-gray-200);
}

.dark .restaurant-pricing {
  border-top-color: var(--color-gray-400);
}

.restaurant-price {
  font-weight: 600;
  color: var(--color-primary);
}

.restaurant-description {
  margin: var(--spacing-2) 0;
  padding: var(--spacing-2);
  background: var(--color-gray-50);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  color: var(--color-gray-600);
  line-height: 1.4;
}

.dark .restaurant-description {
  background: var(--color-gray-300);
  color: var(--color-gray-700);
}

.restaurant-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* Activities Content Styles */
.activities-content {
  display: grid;
  gap: var(--spacing-4);
}

.activities-grid {
  display: grid;
  gap: var(--spacing-4);
}

.activity-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.dark .activity-card {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-400);
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-secondary);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background: linear-gradient(135deg, var(--color-secondary-light) 0%, var(--color-secondary) 100%);
  color: white;
}

.activity-time {
  font-weight: 600;
  font-size: 0.9rem;
}

.activity-duration {
  font-size: 0.8rem;
  opacity: 0.9;
}

.activity-content {
  padding: var(--spacing-4);
}

.activity-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-2);
}

.dark .activity-name {
  color: var(--color-gray-200);
}

.activity-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-2);
  font-size: 0.9rem;
}

.activity-location i {
  color: var(--color-secondary);
  width: 16px;
}

.activity-description {
  color: var(--color-gray-600);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-3);
}

.activity-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-gray-200);
}

.dark .activity-pricing {
  border-top-color: var(--color-gray-400);
}

.activity-price {
  font-weight: 600;
  color: var(--color-secondary);
}

.activity-category {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-2);
  font-size: 0.9rem;
}

.activity-category i {
  color: var(--color-secondary);
  width: 16px;
}

.activity-highlights {
  margin: var(--spacing-3) 0;
  padding: var(--spacing-3);
  background: var(--color-gray-50);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--color-secondary);
}

.dark .activity-highlights {
  background: var(--color-gray-300);
}

.activity-highlights h5 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-gray-800);
}

.dark .activity-highlights h5 {
  color: var(--color-gray-200);
}

.activity-highlights ul {
  margin: 0;
  padding-left: var(--spacing-4);
  list-style-type: disc;
}

.activity-highlights li {
  font-size: 0.85rem;
  color: var(--color-gray-600);
  line-height: 1.4;
  margin-bottom: var(--spacing-1);
}

.activity-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

/* Timeline Content Styles */
.timeline-content {
  display: grid;
  gap: var(--spacing-4);
}

.day-timeline {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
}

.dark .day-timeline {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-400);
}

.timeline-header {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-light) 100%);
  color: white;
  padding: var(--spacing-4);
  text-align: center;
}

.timeline-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.timeline-items {
  padding: var(--spacing-4);
}

.timeline-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-3);
  background: var(--color-gray-50);
  border-left: 4px solid var(--color-accent);
  transition: all var(--transition-fast);
}

.dark .timeline-item {
  background: var(--color-gray-200);
}

.timeline-item:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.timeline-item.transport {
  border-left-color: var(--color-warning);
  background: linear-gradient(145deg, #fef3c7 0%, #fde68a 100%);
}

.dark .timeline-item.transport {
  background: linear-gradient(145deg, var(--color-gray-300) 0%, var(--color-gray-400) 100%);
}

.timeline-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
  font-size: 1.1rem;
}

.timeline-icon.hotel {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-light) 100%);
  color: white;
}

.timeline-icon.restaurant {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  color: white;
}

.timeline-icon.activity {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-light) 100%);
  color: white;
}

.timeline-icon.transport {
  background: linear-gradient(135deg, var(--color-warning) 0%, #fbbf24 100%);
  color: white;
}

.timeline-details {
  flex: 1;
}

.timeline-time {
  font-weight: 600;
  color: var(--color-gray-800);
  font-size: 0.9rem;
}

.dark .timeline-time {
  color: var(--color-gray-200);
}

.timeline-title {
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-1);
}

.dark .timeline-title {
  color: var(--color-gray-200);
}

.timeline-subtitle {
  color: var(--color-gray-600);
  font-size: 0.9rem;
}

.timeline-distance {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-1);
  font-size: 0.8rem;
  color: var(--color-gray-500);
}

.timeline-distance i {
  color: var(--color-warning);
}

/* Transport Content Styles */
.transport-content {
  display: grid;
  gap: var(--spacing-4);
}

.transport-placeholder {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  padding: var(--spacing-8);
  text-align: center;
}

.dark .transport-placeholder {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-400);
}

.transport-placeholder i {
  font-size: 4rem;
  color: var(--color-gray-400);
  margin-bottom: var(--spacing-4);
}

.transport-placeholder h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.dark .transport-placeholder h3 {
  color: var(--color-gray-300);
}

.transport-placeholder p {
  color: var(--color-gray-600);
  font-size: 1rem;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

/* Map Section Styles */
.map-section {
  padding: var(--spacing-12) 0;
  background: linear-gradient(145deg, var(--color-gray-50) 0%, #ffffff 100%);
  border-top: 1px solid var(--color-gray-200);
  border-bottom: 1px solid var(--color-gray-200);
}

.dark .map-section {
  background: linear-gradient(145deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  border-color: var(--color-gray-400);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.map-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-gray-800);
  margin: 0;
}

.dark .map-header h2 {
  color: var(--color-gray-200);
}

.map-controls {
  display: flex;
  gap: var(--spacing-3);
}

.map-container {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-6);
  align-items: start;
}

.trip-map {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  min-height: 500px;
  position: relative;
}

.dark .trip-map {
  background: linear-gradient(145deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);
  border-color: var(--color-gray-400);
}

.map-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  color: var(--color-gray-500);
}

.map-loading i {
  font-size: 4rem;
  color: var(--color-gray-400);
  margin-bottom: var(--spacing-4);
}

.map-loading h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.dark .map-loading h3 {
  color: var(--color-gray-300);
}

.map-loading p {
  color: var(--color-gray-600);
  text-align: center;
}

.map-legend {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  padding: var(--spacing-5);
  min-width: 200px;
}

.dark .map-legend {
  background: linear-gradient(145deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);
  border-color: var(--color-gray-400);
}

.map-legend h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: var(--spacing-2);
}

.dark .map-legend h4 {
  color: var(--color-gray-200);
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2);
  border-radius: var(--border-radius);
  transition: background-color var(--transition-fast);
}

.legend-item:hover {
  background: var(--color-gray-100);
}

.dark .legend-item:hover {
  background: var(--color-gray-400);
}

.legend-item i {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
}

.legend-item span {
  font-weight: 500;
  color: var(--color-gray-700);
}

.dark .legend-item span {
  color: var(--color-gray-300);
}

/* Folium Map Integration */
.folium-map {
  width: 100%;
  height: 500px;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

/* Map Responsive Design */
@media (max-width: 1024px) {
  .map-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .map-legend {
    order: -1;
  }

  .legend-items {
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .legend-item {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .map-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }

  .map-controls {
    justify-content: center;
  }

  .trip-map {
    min-height: 400px;
  }

  .map-loading {
    height: 400px;
  }
}

/* Empty State Styles */
.empty-state {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  color: var(--color-gray-500);
}

.empty-state i {
  font-size: 3rem;
  color: var(--color-gray-400);
  margin-bottom: var(--spacing-4);
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.dark .empty-state h3 {
  color: var(--color-gray-300);
}

.empty-state p {
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .day-tabs-navigation {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2);
  }

  .day-tab-button {
    flex: none;
    padding: var(--spacing-2) var(--spacing-3);
  }

  .day-tab-button span {
    display: block;
    font-size: 0.8rem;
  }

  .day-tab-button i {
    font-size: 1rem;
  }

  .day-tabs-content {
    padding: var(--spacing-4);
  }

  .accommodation-header {
    height: 150px;
  }

  .accommodation-content {
    padding: var(--spacing-4);
  }

  .accommodation-pricing {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }

  .accommodation-actions {
    justify-content: stretch;
  }

  .accommodation-actions .btn {
    flex: 1;
  }

  .restaurant-pricing,
  .activity-pricing {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: stretch;
  }

  .restaurant-pricing .btn,
  .activity-pricing .btn {
    width: 100%;
    justify-content: center;
  }

  .timeline-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .timeline-icon {
    margin-right: 0;
    margin-bottom: var(--spacing-2);
  }

  .timeline-distance {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .day-tabs-navigation {
    grid-template-columns: 1fr;
  }

  .day-tab-button {
    justify-content: flex-start;
    padding: var(--spacing-3);
  }

  .day-tab-button i {
    margin-right: var(--spacing-2);
  }

  .day-tab-button span {
    font-size: 0.9rem;
  }
}

/* ===== TRIP STATUS INDICATOR ===== */
.trip-status-indicator {
  position: fixed;
  top: 80px;
  right: 20px;
  background: var(--color-accent, #3b82f6);
  color: white;
  padding: 12px 16px;
  border-radius: var(--border-radius-lg);
  z-index: 1000;
  font-size: 14px;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all 0.3s ease;
  animation: slideInFromRight 0.5s ease-out;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.trip-status-indicator.trip-status-future {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-light) 100%);
}

.trip-status-indicator.trip-status-past {
  background: linear-gradient(135deg, var(--color-gray-500) 0%, var(--color-gray-600) 100%);
}

.trip-status-indicator.trip-status-error {
  background: linear-gradient(135deg, var(--color-error) 0%, #dc2626 100%);
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.trip-status-indicator:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Mobile responsive adjustments for trip status indicator */
@media (max-width: 768px) {
  .trip-status-indicator {
    top: 60px;
    right: 10px;
    font-size: 12px;
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .trip-status-indicator {
    position: relative;
    top: auto;
    right: auto;
    margin: var(--spacing-4) var(--spacing-4) 0;
    border-radius: var(--border-radius);
  }
}