<!doctype html>
<html lang="en" class="light">
<head>
  <!-- Existing head content remains the same -->
  <meta charset="UTF-8">
  <title>Tsafira | Explore Morocco</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Google Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap" rel="stylesheet">
  <!-- ApexCharts (if you need charts) -->
  <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
  <!-- Font Awesome -->
  <script>
    window.FontAwesomeConfig = {
      autoReplaceSvg: 'nest'
    };
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

  <!-- Tailwind CSS with dark mode enabled -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class', // Enable dark mode with class-based approach
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: '#F26522',
              dark: '#ff7733',
            },
            secondary: {
              DEFAULT: '#3F20FB',
              dark: '#4f46e5',
            },
            accent: {
              DEFAULT: '#FFF8F2',
              dark: '#1f1f1f',
            },
            dark: {
              bg: '#1f2937',
              'bg-alt': '#111827',
              'bg-accent': '#2d3748',
              border: '#374151',
            }
          },
        }
      },
      variants: {
        extend: {
          backgroundColor: ['dark', 'active', 'group-hover'],
          textColor: ['dark', 'active', 'group-hover'],
          borderColor: ['dark'],
        }
      },
    };
  </script>

  <!-- Base styles -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/index.css">
  <link rel="stylesheet" href="css/enhanced-form.css">


  <style>
    /* Additional styles for dark mode transitions */
    html.transition,
    html.transition *,
    html.transition *:before,
    html.transition *:after {
      transition: all 0.3s ease-in-out !important;
      transition-delay: 0 !important;
    }
  </style>
</head>

<body class="h-full text-gray-800 dark:text-gray-100 bg-white dark:bg-dark-bg" data-page="pages/index">
  <!-- Scroll Progress Bar -->
  <div class="scroll-progress-container dark:bg-gray-700">
    <div class="scroll-progress-bar"></div>
  </div>

  <!-- Header Placeholder with Dark Mode Toggle -->
  <header id="header-placeholder" class="bg-white dark:bg-dark-bg border-b border-gray-200 dark:border-dark-border"> <!-- Dark mode toggle button will be inserted here by JavaScript -->
  </header>

  <!-- Hero Section -->
  <section id="hero" class="relative h-[800px] flex items-center">
    <img class="absolute inset-0 w-full h-full object-cover" src="assets/image/hero/homepage.jpg" alt="stunning moroccan desert landscape with traditional architecture, dramatic lighting, cinematic composition" />
    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
    <div class="container mx-auto px-6 relative z-10">
      <div class="max-w-3xl">
        <h1 class="text-5xl md:text-6xl text-white font-bold mb-6">Explore Morocco Like Never Before</h1>
        <p class="text-xl text-white mb-8">
          Create your perfect Moroccan adventure with our personalized trip planner. Let us craft an unforgettable journey based on your interests and budget.
        </p>
        <a href="pages/wizard.html" class="bg-primary hover:bg-primary-dark text-white px-8 py-4 rounded-full text-lg transition duration-300">
          Create Itinerary
        </a>
      </div>
    </div>
  </section>

  <!-- Trip Wizard Section -->
  <section id="trip-wizard" class="py-20 bg-gray-50 dark:bg-dark-bg-alt relative overflow-hidden">
    <!-- Background decorative elements -->
    <div class="absolute top-0 right-0 w-64 h-64 bg-primary/5 dark:bg-primary-dark/5 rounded-full -mr-32 -mt-32"></div>
    <div class="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 dark:bg-primary-dark/5 rounded-full -ml-48 -mb-48"></div>

    <div class="container mx-auto px-6 relative z-10">
      <div class="max-w-4xl mx-auto enhanced-form-container p-8 md:p-10">
        <!-- Decorative elements -->
        <div class="form-decoration form-decoration-1"></div>
        <div class="form-decoration form-decoration-2"></div>

        <!-- Form header -->
        <div class="form-header">
          <h2 class="form-title text-3xl md:text-4xl font-bold text-center mb-2">Plan Your Perfect Trip</h2>
          <p class="text-center text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Tell us about your dream Moroccan adventure and we'll create a personalized itinerary just for you.</p>
        </div>

        <form id="trip-form" class="space-y-8 relative z-10">
          <!-- Budget Range -->
          <div class="form-field animate-fadeInUp">
            <label class="form-label">
              <i class="fas fa-coins text-primary dark:text-primary-dark"></i>
              <span>Budget Range</span>
            </label>
            <div class="slider-container">
              <input id="quick-budget" type="range" min="500" max="10000" value="5000" step="500" class="enhanced-range" />
              <div class="range-tooltip" id="budget-tooltip">$5,000</div>
              <div class="range-labels">
                <span class="range-label" data-value="500">$500</span>
                <span class="range-label" data-value="2500">$2,500</span>
                <span class="range-label" data-value="5000">$5,000</span>
                <span class="range-label" data-value="7500">$7,500</span>
                <span class="range-label" data-value="10000">$10,000</span>
              </div>
            </div>
          </div>

          <!-- Travel Dates -->
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Check-in Date -->
            <div class="form-field animate-fadeInUp delay-100">
              <label class="form-label">
                <i class="fas fa-calendar-check text-primary dark:text-primary-dark"></i>
                <span>Check-in Date</span>
              </label>
              <div class="relative">
                <input id="quick-start-date" type="date" class="enhanced-input" />
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <i class="fas fa-calendar-alt text-gray-400 dark:text-gray-500"></i>
                </div>
              </div>
            </div>

            <!-- Check-out Date -->
            <div class="form-field animate-fadeInUp delay-200">
              <label class="form-label">
                <i class="fas fa-calendar-check text-primary dark:text-primary-dark"></i>
                <span>Check-out Date</span>
              </label>
              <div class="relative">
                <input id="quick-end-date" type="date" class="enhanced-input" />
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <i class="fas fa-calendar-alt text-gray-400 dark:text-gray-500"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- Travel Preferences -->
          <div class="form-field animate-fadeInUp delay-300">
            <label class="form-label">
              <i class="fas fa-heart text-primary dark:text-primary-dark"></i>
              <span>Travel Preferences</span>
            </label>
            <div class="enhanced-checkbox-group">
              <div class="enhanced-checkbox">
                <input id="quick-culture" type="checkbox" />
                <label for="quick-culture" class="checkbox-label">
                  <i class="fas fa-landmark"></i>
                  <span>Culture</span>
                </label>
              </div>
              <div class="enhanced-checkbox">
                <input id="quick-nature" type="checkbox" />
                <label for="quick-nature" class="checkbox-label">
                  <i class="fas fa-mountain"></i>
                  <span>Nature</span>
                </label>
              </div>
              <div class="enhanced-checkbox">
                <input id="quick-luxury" type="checkbox" />
                <label for="quick-luxury" class="checkbox-label">
                  <i class="fas fa-star"></i>
                  <span>Luxury</span>
                </label>
              </div>
              <div class="enhanced-checkbox">
                <input id="quick-sightseeing" type="checkbox" />
                <label for="quick-sightseeing" class="checkbox-label">
                  <i class="fas fa-camera"></i>
                  <span>Sightseeing</span>
                </label>
              </div>
            </div>
          </div>

          <!-- City Card Option -->
          <div class="city-card-option animate-fadeInUp delay-400">
            <div class="flex items-center flex-wrap">
              <div class="enhanced-checkbox">
                <input id="quick-city-card" type="checkbox" />
                <label for="quick-city-card" class="checkbox-label">
                  <i class="fas fa-ticket"></i>
                  <span>Include City Card</span>
                </label>
              </div>
              <div class="ml-3">
                <span class="recommended-badge">
                  <i class="fas fa-star-half-alt"></i>
                  Recommended
                </span>
              </div>
              <div class="ml-2 text-gray-500 dark:text-gray-400 text-sm flex items-center">
                <i class="fas fa-circle-info cursor-help mr-1"></i>
                <span class="text-xs">Save up to 40% on attractions</span>
              </div>
            </div>

            <!-- City Card Duration Options (Hidden by default) -->
            <div id="city-card-options" class="hidden mt-4">
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Select duration:</p>
              <div class="duration-options">
                <div class="duration-option">
                  <input type="radio" id="duration-2" name="card-duration" value="2" checked />
                  <label for="duration-2" class="duration-label">2 Days</label>
                </div>
                <div class="duration-option">
                  <input type="radio" id="duration-4" name="card-duration" value="4" />
                  <label for="duration-4" class="duration-label">4 Days</label>
                </div>
                <div class="duration-option">
                  <input type="radio" id="duration-7" name="card-duration" value="7" />
                  <label for="duration-7" class="duration-label">7 Days</label>
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <button id="quick-generate-btn" type="button" class="submit-button">
            <span>Generate Your Itinerary</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </form>
      </div>
    </div>
  </section>

  <!-- How It Works -->
  <section id="how-it-works" class="py-20 bg-white dark:bg-dark-bg">
    <div class="container mx-auto px-6">
      <h2 class="text-4xl font-bold text-center mb-16 dark:text-gray-100">Plan Your Perfect Moroccan Adventure</h2>
      <div class="grid md:grid-cols-3 gap-12">
        <div class="text-center">
          <div class="w-20 h-20 bg-orange-100 dark:bg-dark-bg-accent rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="text-3xl text-primary dark:text-primary-dark fas fa-list-check"></i>
          </div>
          <h3 class="text-xl font-bold mb-4 dark:text-gray-200">Enter Your Preferences</h3>
          <p class="text-gray-600 dark:text-gray-400">Tell us about your travel style, budget, and interests</p>
        </div>
        <div class="text-center">
          <div class="w-20 h-20 bg-orange-100 dark:bg-dark-bg-accent rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="text-3xl text-primary dark:text-primary-dark fas fa-route"></i>
          </div>
          <h3 class="text-xl font-bold mb-4 dark:text-gray-200">Receive Personalized Itinerary</h3>
          <p class="text-gray-600 dark:text-gray-400">Get a custom day-by-day plan tailored just for you</p>
        </div>
        <div class="text-center">
          <div class="w-20 h-20 bg-orange-100 dark:bg-dark-bg-accent rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="text-3xl text-primary dark:text-primary-dark fas fa-plane-departure"></i>
          </div>
          <h3 class="text-xl font-bold mb-4 dark:text-gray-200">Enjoy Your Trip</h3>
          <p class="text-gray-600 dark:text-gray-400">Experience Morocco with confidence and ease</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Destination Showcase -->
  <section id="destinations" class="py-20 bg-gray-50 dark:bg-dark-bg-alt">
    <div class="container mx-auto px-6">
      <h2 class="text-4xl font-bold text-center mb-4 dark:text-gray-100">Discover Morocco's Treasures</h2>
      <p class="text-gray-600 dark:text-gray-400 text-center mb-12 max-w-2xl mx-auto">
        From ancient medinas to stunning coastlines, explore the diverse wonders of Morocco.
      </p>
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Marrakech -->
        <a href="pages/city.html?id=marrakech" class="group relative overflow-hidden rounded-2xl block">
          <img class="w-full h-[400px] object-cover transition duration-300 group-hover:scale-110" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/e3b187242b-b79b06920ff67beb54a0.png" alt="marrakech medina with traditional market and people, vibrant colors, professional travel photography" />
          <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
            <div class="absolute bottom-6 left-6 text-white">
              <h3 class="text-2xl font-bold mb-2">Marrakech</h3>
              <p>The Red City's magic</p>
            </div>
          </div>
        </a>
        <!-- Casablanca -->
        <a href="pages/city.html?id=casablanca" class="group relative overflow-hidden rounded-2xl block">
          <img class="w-full h-[400px] object-cover transition duration-300 group-hover:scale-110" src="https://media-cdn.tripadvisor.com/media/attractions-splice-spp-674x446/11/e7/a0/db.jpg" alt="hassan ii mosque in casablanca at sunset, dramatic architecture, professional travel photography" />
          <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
            <div class="absolute bottom-6 left-6 text-white">
              <h3 class="text-2xl font-bold mb-2">Casablanca</h3>
              <p>Modern Morocco</p>
            </div>
          </div>
        </a>
        <!-- Fes -->
        <a href="pages/city.html?id=fes" class="group relative overflow-hidden rounded-2xl block">
          <img class="w-full h-[400px] object-cover transition duration-300 group-hover:scale-110" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/61800c4990-d856c6a0dc229bfe40cf.png" alt="fes medina traditional leather tanneries, colorful dye pits, professional travel photography" />
          <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
            <div class="absolute bottom-6 left-6 text-white">
              <h3 class="text-2xl font-bold mb-2">Fes</h3>
              <p>Cultural Heritage</p>
            </div>
          </div>
        </a>
        <!-- Chefchaouen -->
        <a href="pages/city.html?id=chefchaouen" class="group relative overflow-hidden rounded-2xl block">
          <img class="w-full h-[400px] object-cover transition duration-300 group-hover:scale-110" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/09eff2f8d1-cff53d80229e5752e8a3.png" alt="blue city chefchaouen morocco, narrow streets with blue buildings, professional travel photography" />
          <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
            <div class="absolute bottom-6 left-6 text-white">
              <h3 class="text-2xl font-bold mb-2">Chefchaouen</h3>
              <p>The Blue Pearl</p>
            </div>
          </div>
        </a>
      </div>
      <div class="text-center mt-12">
        <a href="pages/destinations.html" class="inline-flex items-center text-primary dark:text-primary-dark hover:text-orange-600 dark:hover:text-orange-500 cursor-pointer">
          View All Destinations <i class="ml-2 fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  </section>

  <!-- Experience Categories -->
  <section id="experiences" class="py-20 bg-white dark:bg-dark-bg">
  <div class="container mx-auto px-6">
    <h2 class="text-4xl font-bold text-center mb-16 dark:text-gray-100">Tailor Your Experience</h2>
    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="p-6 bg-white dark:bg-dark-bg-accent rounded-xl shadow-lg">
        <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mb-6 mx-auto">
          <i class="text-3xl text-primary dark:text-primary-dark fas fa-mosque"></i>
        </div>
        <h3 class="text-xl font-bold mb-4 dark:text-gray-200 text-center">Cultural Immersion</h3>
        <p class="text-gray-600 dark:text-gray-400 text-center">Local traditions, artisan workshops, and authentic experiences</p>
      </div>

      <div class="p-6 bg-white dark:bg-dark-bg-accent rounded-xl shadow-lg">
        <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mb-6 mx-auto">
          <i class="text-3xl text-primary dark:text-primary-dark fas fa-mountain"></i>
        </div>
        <h3 class="text-xl font-bold mb-4 dark:text-gray-200 text-center">Natural Wonders</h3>
        <p class="text-gray-600 dark:text-gray-400 text-center">Desert adventures, mountain treks, and coastal escapes</p>
      </div>

      <div class="p-6 bg-white dark:bg-dark-bg-accent rounded-xl shadow-lg">
        <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mb-6 mx-auto">
          <i class="text-3xl text-primary dark:text-primary-dark fas fa-star"></i>
        </div>
        <h3 class="text-xl font-bold mb-4 dark:text-gray-200 text-center">Luxury Stays</h3>
        <p class="text-gray-600 dark:text-gray-400 text-center">Premium riads, desert camps, and boutique hotels</p>
      </div>

      <div class="p-6 bg-white dark:bg-dark-bg-accent rounded-xl shadow-lg">
        <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mb-6 mx-auto">
          <i class="text-3xl text-primary dark:text-primary-dark fas fa-landmark"></i>
        </div>
        <h3 class="text-xl font-bold mb-4 dark:text-gray-200 text-center">Historical Sightseeing</h3>
        <p class="text-gray-600 dark:text-gray-400 text-center">Ancient medinas, palaces, and architectural wonders</p>
      </div>
    </div>
  </div>
</section>

  <!-- Featured Itineraries -->
  <section id="featured-itineraries" class="py-20 bg-white dark:bg-dark-bg">
    <div class="container mx-auto px-6">
      <h2 class="text-4xl font-bold text-center mb-16 dark:text-gray-100">Popular Morocco Experiences</h2>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-white dark:bg-dark-bg-accent rounded-xl overflow-hidden shadow-lg">
          <img class="w-full h-48 object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/142411fd97-29251fbcae2275a38d4e.png" alt="Cultural tour in Marrakech">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <span class="bg-orange-100 dark:bg-dark-bg text-primary dark:text-primary-dark px-3 py-1 rounded-full text-sm">7 Days</span>
              <span class="text-gray-600 dark:text-gray-400">From $1,200</span>
            </div>
            <h3 class="text-xl font-bold mb-4 dark:text-gray-200">7-Day Cultural Tour</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Immerse yourself in Morocco's rich heritage through medinas, workshops, and local encounters.</p>
            <div class="flex items-center text-primary dark:text-primary-dark">
              <span class="text-sm">View Itinerary</span>
              <i class="ml-2 fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
        <div class="bg-white dark:bg-dark-bg-accent rounded-xl overflow-hidden shadow-lg">
          <img class="w-full h-48 object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/cbe933b1f5-b9374fbbe91e32276832.png" alt="Coastal getaway">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <span class="bg-orange-100 dark:bg-dark-bg text-primary dark:text-primary-dark px-3 py-1 rounded-full text-sm">5 Days</span>
              <span class="text-gray-600 dark:text-gray-400">From $800</span>
            </div>
            <h3 class="text-xl font-bold mb-4 dark:text-gray-200">5-Day Coastal Getaway</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Explore Morocco's stunning coastline, from pristine beaches to historic ports.</p>
            <div class="flex items-center text-primary dark:text-primary-dark">
              <span class="text-sm">View Itinerary</span>
              <i class="ml-2 fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
        <div class="bg-white dark:bg-dark-bg-accent rounded-xl overflow-hidden shadow-lg">
          <img class="w-full h-48 object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/b4f634ce60-8ea4daecc1b729ae6d56.png" alt="Morocco explorer">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <span class="bg-orange-100 dark:bg-dark-bg text-primary dark:text-primary-dark px-3 py-1 rounded-full text-sm">10 Days</span>
              <span class="text-gray-600 dark:text-gray-400">From $2,000</span>
            </div>
            <h3 class="text-xl font-bold mb-4 dark:text-gray-200">10-Day Morocco Explorer</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">The ultimate Moroccan adventure combining cities, desert, and mountains.</p>
            <div class="flex items-center text-primary dark:text-primary-dark">
              <span class="text-sm">View Itinerary</span>
              <i class="ml-2 fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center mt-12">
        <button class="bg-primary dark:bg-primary-dark text-white px-8 py-3 rounded-full hover:bg-primary-dark dark:hover:bg-opacity-90">
          <a href="/pages/wizard.html">Create Custom Itinerary</a>
        </button>
      </div>
    </div>
  </section>

  <!-- Why Choose Us (Continued) -->
  <section id="benefits" class="py-20 bg-gray-50 dark:bg-dark-bg-alt">
    <div class="container mx-auto px-6">
      <h2 class="text-4xl font-bold text-center mb-16 dark:text-gray-100">Why Choose Tsafira?</h2>
      <div class="grid md:grid-cols-3 gap-12">
        <div class="text-center">
          <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="text-3xl text-primary dark:text-primary-dark fas fa-wand-magic-sparkles"></i>
          </div>
          <h3 class="text-xl font-bold mb-4 dark:text-gray-200">Personalized Itineraries</h3>
          <p class="text-gray-600 dark:text-gray-400">Customized travel plans that match your unique preferences and style</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="text-3xl text-primary dark:text-primary-dark fas fa-coins"></i>
          </div>
          <h3 class="text-xl font-bold mb-4 dark:text-gray-200">Budget-Friendly Planning</h3>
          <p class="text-gray-600 dark:text-gray-400">Optimize your travel budget with smart recommendations and local insights</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-orange-100 dark:bg-dark-bg rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="text-3xl text-primary dark:text-primary-dark fas fa-map-location-dot"></i>
          </div>
          <h3 class="text-xl font-bold mb-4 dark:text-gray-200">Local Expertise</h3>
          <p class="text-gray-600 dark:text-gray-400">Access to authentic experiences curated by Moroccan travel experts</p>
        </div>
      </div>
    </div>
  </section>

  <!-- City Pass Cards Section -->
  <section id="city-pass" class="py-20 bg-white dark:bg-dark-bg relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-gray-50 to-transparent dark:from-dark-bg-alt dark:to-transparent"></div>
    <div class="absolute -top-24 -right-24 w-96 h-96 bg-primary/5 dark:bg-primary-dark/5 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-24 -left-24 w-96 h-96 bg-primary/5 dark:bg-primary-dark/5 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-6 relative z-10">
      <div class="text-center mb-16">
        <span class="inline-block px-4 py-1 bg-orange-100 dark:bg-dark-bg-accent text-primary dark:text-primary-dark rounded-full text-sm font-medium mb-4">City Cards</span>
        <h2 class="text-4xl font-bold mb-4 dark:text-gray-100">Enhance Your Trip with a City Card</h2>
        <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Free museums, easy transport, one smart card per city.</p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <!-- Marrakech City Card -->
        <div class="city-pass-card group">
          <div class="relative overflow-hidden rounded-xl">
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent z-10"></div>
            <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/e3b187242b-b79b06920ff67beb54a0.png" alt="Marrakech City Card" class="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110">
            <div class="absolute top-4 left-4 z-20">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary text-white">
                <i class="fas fa-star mr-1"></i> Most Popular
              </span>
            </div>
            <div class="absolute bottom-0 left-0 w-full p-6 z-20">
              <h3 class="text-2xl font-bold text-white mb-1">Marrakech Pass</h3>
              <div class="flex items-center text-white/80 text-sm mb-4">
                <i class="fas fa-ticket-alt mr-2"></i>
                <span>From $45 for 2 days</span>
              </div>
              <div class="flex flex-wrap gap-2 mb-4">
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">20+ Attractions</span>
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">Public Transport</span>
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">Skip the Line</span>
              </div>
              <a href="./pages/city-card.html?city=marrakech" class="inline-flex items-center justify-center w-full py-3 px-4 bg-white/90 hover:bg-white text-primary font-medium rounded-lg transition-colors duration-300 backdrop-blur-sm">
                <i class="fas fa-info-circle mr-2"></i> View Details
              </a>
            </div>
          </div>
        </div>

        <!-- Casablanca City Card -->
        <div class="city-pass-card group">
          <div class="relative overflow-hidden rounded-xl">
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent z-10"></div>
            <img src="https://media-cdn.tripadvisor.com/media/attractions-splice-spp-674x446/11/e7/a0/db.jpg" alt="Casablanca City Card" class="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110">
            <div class="absolute top-4 left-4 z-20">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                <i class="fas fa-water mr-1"></i> Coastal City
              </span>
            </div>
            <div class="absolute bottom-0 left-0 w-full p-6 z-20">
              <h3 class="text-2xl font-bold text-white mb-1">Casablanca Pass</h3>
              <div class="flex items-center text-white/80 text-sm mb-4">
                <i class="fas fa-ticket-alt mr-2"></i>
                <span>From $40 for 2 days</span>
              </div>
              <div class="flex flex-wrap gap-2 mb-4">
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">15+ Attractions</span>
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">Public Transport</span>
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">Guided Tours</span>
              </div>
              <a href="./pages/city-card.html?city=casablanca" class="inline-flex items-center justify-center w-full py-3 px-4 bg-white/90 hover:bg-white text-primary font-medium rounded-lg transition-colors duration-300 backdrop-blur-sm">
                <i class="fas fa-info-circle mr-2"></i> View Details
              </a>
            </div>
          </div>
        </div>

        <!-- Fes City Card -->
        <div class="city-pass-card group">
          <div class="relative overflow-hidden rounded-xl">
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent z-10"></div>
            <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/61800c4990-d856c6a0dc229bfe40cf.png" alt="Fes City Card" class="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110">
            <div class="absolute top-4 left-4 z-20">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-500 text-white">
                <i class="fas fa-landmark mr-1"></i> Cultural
              </span>
            </div>
            <div class="absolute bottom-0 left-0 w-full p-6 z-20">
              <h3 class="text-2xl font-bold text-white mb-1">Fes Pass</h3>
              <div class="flex items-center text-white/80 text-sm mb-4">
                <i class="fas fa-ticket-alt mr-2"></i>
                <span>From $35 for 2 days</span>
              </div>
              <div class="flex flex-wrap gap-2 mb-4">
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">18+ Attractions</span>
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">Medina Tours</span>
                <span class="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs text-white">Artisan Workshops</span>
              </div>
              <a href="./pages/city-card.html?city=fes" class="inline-flex items-center justify-center w-full py-3 px-4 bg-white/90 hover:bg-white text-primary font-medium rounded-lg transition-colors duration-300 backdrop-blur-sm">
                <i class="fas fa-info-circle mr-2"></i> View Details
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Benefits of City Cards -->
      <div class="mt-20 max-w-5xl mx-auto">
        <div class="grid md:grid-cols-4 gap-6">
          <div class="flex flex-col items-center text-center p-6 rounded-xl bg-gray-50 dark:bg-dark-bg-accent transition-transform duration-300 hover:-translate-y-2">
            <div class="w-14 h-14 bg-primary/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-money-bill-wave text-xl text-primary dark:text-primary-dark"></i>
            </div>
            <h4 class="font-bold mb-2 dark:text-gray-200">Save Money</h4>
            <p class="text-gray-600 dark:text-gray-400 text-sm">Up to 60% off compared to buying individual tickets</p>
          </div>

          <div class="flex flex-col items-center text-center p-6 rounded-xl bg-gray-50 dark:bg-dark-bg-accent transition-transform duration-300 hover:-translate-y-2">
            <div class="w-14 h-14 bg-primary/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-clock text-xl text-primary dark:text-primary-dark"></i>
            </div>
            <h4 class="font-bold mb-2 dark:text-gray-200">Save Time</h4>
            <p class="text-gray-600 dark:text-gray-400 text-sm">Skip the lines at popular attractions and museums</p>
          </div>

          <div class="flex flex-col items-center text-center p-6 rounded-xl bg-gray-50 dark:bg-dark-bg-accent transition-transform duration-300 hover:-translate-y-2">
            <div class="w-14 h-14 bg-primary/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-bus text-xl text-primary dark:text-primary-dark"></i>
            </div>
            <h4 class="font-bold mb-2 dark:text-gray-200">Free Transport</h4>
            <p class="text-gray-600 dark:text-gray-400 text-sm">Unlimited use of public transportation within the city</p>
          </div>

          <div class="flex flex-col items-center text-center p-6 rounded-xl bg-gray-50 dark:bg-dark-bg-accent transition-transform duration-300 hover:-translate-y-2">
            <div class="w-14 h-14 bg-primary/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-map-marked-alt text-xl text-primary dark:text-primary-dark"></i>
            </div>
            <h4 class="font-bold mb-2 dark:text-gray-200">Flexibility</h4>
            <p class="text-gray-600 dark:text-gray-400 text-sm">Choose your own itinerary and visit attractions at your own pace</p>
          </div>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="text-center mt-16">
        <a href="./pages/city-pass.html" class="inline-flex items-center px-8 py-4 bg-primary hover:bg-primary-dark text-white font-medium rounded-full transition-colors duration-300 shadow-lg hover:shadow-xl">
          <i class="fas fa-ticket-alt mr-2"></i> Explore All City Cards
          <i class="fas fa-arrow-right ml-2"></i>
        </a>
      </div>
    </div>
  </section>

  <!-- Testimonials -->
  <section id="testimonials" class="py-20 bg-gray-50 dark:bg-dark-bg-alt relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute top-0 right-0 w-64 h-64 bg-primary/5 dark:bg-primary-dark/5 rounded-full -mr-32 -mt-32"></div>
    <div class="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 dark:bg-primary-dark/5 rounded-full -ml-48 -mb-48"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-primary/3 dark:bg-primary-dark/3 rounded-full opacity-50 blur-3xl pointer-events-none"></div>

    <div class="container mx-auto px-6 relative z-10">
      <div class="text-center mb-16">
        <span class="inline-block px-4 py-1 bg-orange-100 dark:bg-dark-bg-accent text-primary dark:text-primary-dark rounded-full text-sm font-medium mb-4">Testimonials</span>
        <h2 class="text-4xl font-bold dark:text-gray-100">What Our Travelers Say</h2>
      </div>

      <div class="max-w-5xl mx-auto relative">
        <!-- Navigation arrows -->
        <button id="testimonial-prev" class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 md:-translate-x-12 z-10 bg-white dark:bg-dark-bg-accent w-10 h-10 rounded-full shadow-lg flex items-center justify-center focus:outline-none hover:bg-gray-100 dark:hover:bg-dark-bg transition duration-300">
          <i class="fas fa-chevron-left text-gray-600 dark:text-gray-300"></i>
        </button>

        <button id="testimonial-next" class="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 md:translate-x-12 z-10 bg-white dark:bg-dark-bg-accent w-10 h-10 rounded-full shadow-lg flex items-center justify-center focus:outline-none hover:bg-gray-100 dark:hover:bg-dark-bg transition duration-300">
          <i class="fas fa-chevron-right text-gray-600 dark:text-gray-300"></i>
        </button>

        <!-- Testimonial container -->
        <div class="testimonial-container relative overflow-hidden rounded-2xl">
          <!-- Fallback content in case JavaScript fails -->
          <div class="testimonial-slide active" data-index="0">
            <div class="grid md:grid-cols-2 gap-8">
              <div class="testimonial-card p-6 md:p-8">
                <div class="flex items-center mb-6">
                  <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-1.jpg" alt="Sarah M." class="testimonial-avatar mr-4">
                  <div>
                    <h4 class="font-bold text-lg dark:text-gray-100">Sarah M.</h4>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">New York, USA</p>
                  </div>
                </div>
                <div class="testimonial-stars mb-4">
                  <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                </div>
                <div class="testimonial-quote mb-4">
                  <p class="text-gray-700 dark:text-gray-300">"Tsafira made planning our Moroccan adventure effortless. The personalized itinerary perfectly matched our interests and budget."</p>
                </div>
              </div>
              <div class="testimonial-card p-6 md:p-8">
                <div class="flex items-center mb-6">
                  <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg" alt="James R." class="testimonial-avatar mr-4">
                  <div>
                    <h4 class="font-bold text-lg dark:text-gray-100">James R.</h4>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">London, UK</p>
                  </div>
                </div>
                <div class="testimonial-stars mb-4">
                  <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                </div>
                <div class="testimonial-quote mb-4">
                  <p class="text-gray-700 dark:text-gray-300">"An incredible experience from start to finish. The local expertise really showed in the unique experiences we had."</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Dots navigation -->
        <div class="flex justify-center mt-8 space-x-3" id="carousel-dots">
          <!-- Fallback dots in case JavaScript fails -->
          <button class="carousel-dot bg-orange-600" data-index="0" aria-label="Go to slide 1"></button>
          <button class="carousel-dot bg-gray-300" data-index="1" aria-label="Go to slide 2"></button>
        </div>
      </div>
    </div>
  </section>

  <!-- Call-to-Action -->
  <section id="cta" class="relative py-20">
    <div class="absolute inset-0">
      <img class="w-full h-full object-cover" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/d774e5d0b5-d438ccc560ba8fad89ed.png" alt="moroccan desert sunset with camels, dramatic landscape, professional travel photography">
      <div class="absolute inset-0 bg-black opacity-50"></div>
    </div>
    <div class="container mx-auto px-4 relative">
      <div class="max-w-2xl mx-auto text-center text-white">
        <h2 class="text-4xl font-bold mb-4">Ready to Experience Morocco?</h2>
        <p class="text-xl mb-8">Let us help you create the perfect journey tailored to your dreams.</p>
        <button class="bg-primary dark:bg-primary-dark text-white px-8 py-3 rounded-full hover:bg-primary-dark dark:hover:bg-opacity-90">
          <a href="/pages/wizard.html">Start Planning</a>
        </button>
      </div>
    </div>
  </section>

  <!-- Newsletter -->
  <section id="newsletter" class="py-20 bg-cream-50 dark:bg-dark-bg">
    <div class="container mx-auto px-4">
      <div class="max-w-2xl mx-auto text-center">
        <h2 class="text-3xl font-bold mb-4 dark:text-gray-100">Stay Updated with Travel Inspiration</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-8">Get exclusive travel tips, destination guides, and special offers delivered to your inbox.</p>
        <form class="space-y-4">
          <div class="flex gap-4 relative">
            <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-lg border border-gray-300 dark:border-dark-border dark:bg-dark-bg-accent dark:text-gray-100 transition-colors duration-200">
            <button type="submit" class="bg-primary dark:bg-primary-dark text-white px-8 py-3 rounded-lg hover:bg-primary-dark dark:hover:bg-opacity-90 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">Subscribe</button>
          </div>
          <label class="flex items-center justify-center space-x-2 cursor-pointer">
            <input type="checkbox" class="rounded text-primary dark:text-primary-dark focus:ring-primary dark:focus:ring-primary-dark">
            <span class="text-sm text-gray-600 dark:text-gray-400">I agree to receive travel updates and newsletters</span>
          </label>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer Placeholder -->
  <footer id="footer-placeholder" class="bg-white dark:bg-dark-bg border-t border-gray-200 dark:border-dark-border"></footer>

<!-- Scripts -->
<script type="module" src="js/main.js"></script>
<script type="module" src="js/pages/index-page.js"></script>

</body>
</html>