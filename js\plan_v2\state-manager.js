/**
 * State Manager - Phase 2 Implementation
 * 
 * This module will handle application state management with a clean,
 * predictable state flow to replace the current scattered state handling.
 */

/**
 * State Manager Class
 * 
 * Responsibilities:
 * - Centralized application state
 * - State change notifications
 * - State persistence
 * - State validation
 * - Undo/redo functionality
 */
export class StateManager {
  constructor() {
    this.state = this.getInitialState();
    this.listeners = new Map();
    this.history = [];
    this.historyIndex = -1;
    this.maxHistorySize = 50;
  }

  /**
   * Get initial application state
   * @returns {Object} Initial state
   */
  getInitialState() {
    return {
      // Trip data
      trip: {
        id: null,
        name: '',
        startDate: null,
        endDate: null,
        status: 'future', // future, active, past
        currentDay: 1,
        totalDays: 0
      },

      // Navigation state
      navigation: {
        selectedDay: 1,
        activeTab: 'timeline',
        previousDay: null,
        nextDay: null
      },

      // UI state
      ui: {
        isLoading: false,
        theme: 'light',
        sidebarOpen: false,
        statusBanner: {
          visible: false,
          type: 'info',
          title: '',
          message: ''
        }
      },

      // Data state
      data: {
        tripData: null,
        weatherData: null,
        dayData: new Map(), // day -> data mapping
        lastUpdated: null,
        errors: []
      },

      // User preferences
      preferences: {
        autoSave: true,
        notifications: true,
        units: 'metric', // metric, imperial
        language: 'en'
      }
    };
  }

  /**
   * Get current state
   * @param {string} path - Optional path to specific state property
   * @returns {any} State or state property
   */
  getState(path = null) {
    if (!path) return this.state;
    
    return path.split('.').reduce((obj, key) => obj?.[key], this.state);
  }

  /**
   * Update state
   * @param {string} path - Path to state property
   * @param {any} value - New value
   * @param {boolean} addToHistory - Whether to add to history
   */
  setState(path, value, addToHistory = true) {
    const oldState = JSON.parse(JSON.stringify(this.state));
    
    // Update state
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((obj, key) => {
      if (!obj[key]) obj[key] = {};
      return obj[key];
    }, this.state);
    
    target[lastKey] = value;

    // Add to history
    if (addToHistory) {
      this.addToHistory(oldState);
    }

    // Notify listeners
    this.notifyListeners(path, value, oldState);

    // Persist state
    this.persistState();

    console.log(`🔄 StateManager: Updated ${path}:`, value);
  }

  /**
   * Update multiple state properties
   * @param {Object} updates - Object with path -> value mappings
   * @param {boolean} addToHistory - Whether to add to history
   */
  setMultipleState(updates, addToHistory = true) {
    const oldState = JSON.parse(JSON.stringify(this.state));

    // Apply all updates
    Object.entries(updates).forEach(([path, value]) => {
      const keys = path.split('.');
      const lastKey = keys.pop();
      const target = keys.reduce((obj, key) => {
        if (!obj[key]) obj[key] = {};
        return obj[key];
      }, this.state);
      
      target[lastKey] = value;
    });

    // Add to history
    if (addToHistory) {
      this.addToHistory(oldState);
    }

    // Notify listeners for each update
    Object.entries(updates).forEach(([path, value]) => {
      this.notifyListeners(path, value, oldState);
    });

    // Persist state
    this.persistState();

    console.log('🔄 StateManager: Updated multiple states:', updates);
  }

  /**
   * Subscribe to state changes
   * @param {string} path - State path to watch
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  subscribe(path, callback) {
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Set());
    }
    
    this.listeners.get(path).add(callback);

    // Return unsubscribe function
    return () => {
      const pathListeners = this.listeners.get(path);
      if (pathListeners) {
        pathListeners.delete(callback);
        if (pathListeners.size === 0) {
          this.listeners.delete(path);
        }
      }
    };
  }

  /**
   * Notify listeners of state changes
   * @param {string} path - Changed path
   * @param {any} newValue - New value
   * @param {Object} oldState - Previous state
   */
  notifyListeners(path, newValue, oldState) {
    // Notify exact path listeners
    const pathListeners = this.listeners.get(path);
    if (pathListeners) {
      pathListeners.forEach(callback => {
        try {
          callback(newValue, oldState);
        } catch (error) {
          console.error('StateManager: Listener error:', error);
        }
      });
    }

    // Notify wildcard listeners (e.g., 'trip.*')
    this.listeners.forEach((listeners, listenerPath) => {
      if (listenerPath.endsWith('*')) {
        const basePath = listenerPath.slice(0, -1);
        if (path.startsWith(basePath)) {
          listeners.forEach(callback => {
            try {
              callback(newValue, oldState);
            } catch (error) {
              console.error('StateManager: Wildcard listener error:', error);
            }
          });
        }
      }
    });
  }

  /**
   * Add state to history
   * @param {Object} state - State to add
   */
  addToHistory(state) {
    // Remove any history after current index
    this.history = this.history.slice(0, this.historyIndex + 1);
    
    // Add new state
    this.history.push(state);
    this.historyIndex++;

    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.historyIndex--;
    }
  }

  /**
   * Undo last state change
   * @returns {boolean} Whether undo was successful
   */
  undo() {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.state = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
      this.notifyListeners('*', this.state, null);
      this.persistState();
      console.log('↶ StateManager: Undo performed');
      return true;
    }
    return false;
  }

  /**
   * Redo last undone state change
   * @returns {boolean} Whether redo was successful
   */
  redo() {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.state = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
      this.notifyListeners('*', this.state, null);
      this.persistState();
      console.log('↷ StateManager: Redo performed');
      return true;
    }
    return false;
  }

  /**
   * Persist state to localStorage
   */
  persistState() {
    try {
      const persistableState = {
        navigation: this.state.navigation,
        ui: {
          theme: this.state.ui.theme
        },
        preferences: this.state.preferences
      };
      
      localStorage.setItem('planV2State', JSON.stringify(persistableState));
    } catch (error) {
      console.warn('StateManager: Failed to persist state:', error);
    }
  }

  /**
   * Load state from localStorage
   */
  loadPersistedState() {
    try {
      const persistedState = localStorage.getItem('planV2State');
      if (persistedState) {
        const parsed = JSON.parse(persistedState);
        
        // Merge with current state
        this.state = {
          ...this.state,
          ...parsed
        };
        
        console.log('📥 StateManager: Loaded persisted state');
      }
    } catch (error) {
      console.warn('StateManager: Failed to load persisted state:', error);
    }
  }

  /**
   * Reset state to initial values
   */
  reset() {
    const oldState = this.state;
    this.state = this.getInitialState();
    this.history = [];
    this.historyIndex = -1;
    
    this.notifyListeners('*', this.state, oldState);
    this.persistState();
    
    console.log('🔄 StateManager: State reset');
  }

  /**
   * Validate state integrity
   * @returns {Array} Array of validation errors
   */
  validateState() {
    const errors = [];

    // Validate trip data
    if (this.state.trip.currentDay < 1) {
      errors.push('Current day cannot be less than 1');
    }

    if (this.state.trip.currentDay > this.state.trip.totalDays) {
      errors.push('Current day cannot exceed total days');
    }

    // Validate navigation
    if (this.state.navigation.selectedDay < 1) {
      errors.push('Selected day cannot be less than 1');
    }

    return errors;
  }
}

// Export singleton instance
export const stateManager = new StateManager();
