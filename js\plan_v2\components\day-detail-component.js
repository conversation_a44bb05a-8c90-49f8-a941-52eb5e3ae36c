/**
 * Day Detail Component - Phase 2 Implementation
 * 
 * This component will handle the display of day-specific content including
 * accommodations, restaurants, activities, and transport for each day.
 */

/**
 * Day Detail Component Class
 * 
 * Responsibilities:
 * - Render accommodation details
 * - Display restaurant information
 * - Show activity details
 * - Handle transport information
 * - Manage tab switching between content types
 */
export class DayDetailComponent {
  constructor(container, stateManager) {
    this.container = container;
    this.stateManager = stateManager;
    this.elements = {};
    this.unsubscribers = [];
    this.currentDay = 1;
    
    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    console.log('📅 DayDetailComponent: Initializing...');
    
    this.cacheElements();
    this.setupEventListeners();
    this.subscribeToState();
    
    console.log('📅 DayDetailComponent: Initialized');
  }

  /**
   * Cache DOM elements
   */
  cacheElements() {
    this.elements = {
      // Tab panes
      accommodationsTab: this.container.querySelector('#accommodations-tab'),
      restaurantsTab: this.container.querySelector('#restaurants-tab'),
      activitiesTab: this.container.querySelector('#activities-tab'),
      transportTab: this.container.querySelector('#transport-tab'),
      
      // Content containers
      accommodationCard: this.container.querySelector('.accommodation-card'),
      restaurantsGrid: this.container.querySelector('.restaurants-grid'),
      activityCard: this.container.querySelector('.activity-card'),
      transportCard: this.container.querySelector('.transport-card')
    };
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Accommodation actions
    const accommodationActions = this.container.querySelectorAll('.accommodation-actions .btn');
    accommodationActions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.textContent.trim();
        this.handleAccommodationAction(action);
      });
    });

    // Restaurant actions
    const restaurantActions = this.container.querySelectorAll('.restaurant-actions .btn');
    restaurantActions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.textContent.trim();
        const restaurantCard = e.target.closest('.restaurant-card');
        const restaurantName = restaurantCard.querySelector('h4').textContent;
        this.handleRestaurantAction(action, restaurantName);
      });
    });

    // Activity actions
    const activityActions = this.container.querySelectorAll('.activity-actions .btn');
    activityActions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.textContent.trim();
        this.handleActivityAction(action);
      });
    });

    // Transport actions
    const transportActions = this.container.querySelectorAll('.transport-actions .btn');
    transportActions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.textContent.trim();
        this.handleTransportAction(action);
      });
    });
  }

  /**
   * Subscribe to state changes
   */
  subscribeToState() {
    // Subscribe to selected day changes
    const dayUnsubscribe = this.stateManager.subscribe('navigation.selectedDay', (day) => {
      this.currentDay = day;
      this.loadDayData(day);
    });
    
    this.unsubscribers.push(dayUnsubscribe);

    // Subscribe to day data changes
    const dataUnsubscribe = this.stateManager.subscribe('data.dayData', (dayData) => {
      this.renderDayData(dayData);
    });
    
    this.unsubscribers.push(dataUnsubscribe);
  }

  /**
   * Load data for specific day
   * @param {number} day - Day number
   */
  async loadDayData(day) {
    console.log(`📅 DayDetailComponent: Loading data for day ${day}`);
    
    // Phase 2: Load actual day data from API
    // const dayData = await this.dataManager.fetchDayData(day);
    // this.stateManager.setState(`data.dayData.${day}`, dayData);
    
    // For Phase 1, use placeholder data
    this.renderPlaceholderData(day);
  }

  /**
   * Render day data
   * @param {Object} dayData - Day data from state
   */
  renderDayData(dayData) {
    if (!dayData) return;
    
    const currentDayData = dayData.get ? dayData.get(this.currentDay) : dayData[this.currentDay];
    if (!currentDayData) return;
    
    console.log(`📅 DayDetailComponent: Rendering data for day ${this.currentDay}`);
    
    this.renderAccommodation(currentDayData.accommodation);
    this.renderRestaurants(currentDayData.restaurants);
    this.renderActivities(currentDayData.activities);
    this.renderTransport(currentDayData.transport);
  }

  /**
   * Render accommodation data
   * @param {Object} accommodation - Accommodation data
   */
  renderAccommodation(accommodation) {
    if (!accommodation || !this.elements.accommodationCard) return;
    
    console.log('📅 DayDetailComponent: Rendering accommodation');
    
    // Update accommodation details
    const nameEl = this.elements.accommodationCard.querySelector('h3');
    const locationEl = this.elements.accommodationCard.querySelector('.detail-item:nth-child(1) span');
    const roomEl = this.elements.accommodationCard.querySelector('.detail-item:nth-child(2) span');
    const priceEl = this.elements.accommodationCard.querySelector('.price');
    
    if (nameEl) nameEl.textContent = accommodation.name;
    if (locationEl) locationEl.textContent = accommodation.location;
    if (roomEl) roomEl.textContent = accommodation.roomType;
    if (priceEl) priceEl.textContent = `$${accommodation.price}`;
  }

  /**
   * Render restaurants data
   * @param {Array} restaurants - Restaurants data
   */
  renderRestaurants(restaurants) {
    if (!restaurants || !this.elements.restaurantsGrid) return;
    
    console.log('📅 DayDetailComponent: Rendering restaurants');
    
    const restaurantCards = this.elements.restaurantsGrid.querySelectorAll('.restaurant-card');
    
    restaurantCards.forEach((card, index) => {
      if (restaurants[index]) {
        const restaurant = restaurants[index];
        
        const nameEl = card.querySelector('h4');
        const timeEl = card.querySelector('.restaurant-time');
        const locationEl = card.querySelector('.detail-item:nth-child(1) span');
        const cuisineEl = card.querySelector('.detail-item:nth-child(2) span');
        const priceEl = card.querySelector('.price');
        
        if (nameEl) nameEl.textContent = restaurant.name;
        if (timeEl) timeEl.textContent = restaurant.time;
        if (locationEl) locationEl.textContent = restaurant.location;
        if (cuisineEl) cuisineEl.textContent = restaurant.cuisine;
        if (priceEl) priceEl.textContent = `$${restaurant.price}/person`;
      }
    });
  }

  /**
   * Render activities data
   * @param {Array} activities - Activities data
   */
  renderActivities(activities) {
    if (!activities || !this.elements.activityCard) return;
    
    console.log('📅 DayDetailComponent: Rendering activities');
    
    // For now, render the first activity
    const activity = activities[0];
    if (!activity) return;
    
    const nameEl = this.elements.activityCard.querySelector('h3');
    const timeEl = this.elements.activityCard.querySelector('.activity-time');
    const locationEl = this.elements.activityCard.querySelector('.detail-item:nth-child(1) span');
    const priceEl = this.elements.activityCard.querySelector('.price');
    
    if (nameEl) nameEl.textContent = activity.name;
    if (timeEl) timeEl.textContent = activity.time;
    if (locationEl) locationEl.textContent = activity.location;
    if (priceEl) priceEl.textContent = `$${activity.price}`;
  }

  /**
   * Render transport data
   * @param {Object} transport - Transport data
   */
  renderTransport(transport) {
    if (!transport || !this.elements.transportCard) return;
    
    console.log('📅 DayDetailComponent: Rendering transport');
    
    const routeEl = this.elements.transportCard.querySelector('.transport-route');
    const pickupTimeEl = this.elements.transportCard.querySelector('.transport-point:nth-child(1) .point-time');
    const arrivalTimeEl = this.elements.transportCard.querySelector('.transport-point:nth-child(3) .point-time');
    const priceEl = this.elements.transportCard.querySelector('.price-item.total strong');
    
    if (routeEl) routeEl.textContent = transport.route;
    if (pickupTimeEl) pickupTimeEl.textContent = transport.pickupTime;
    if (arrivalTimeEl) arrivalTimeEl.textContent = transport.arrivalTime;
    if (priceEl) priceEl.textContent = `$${transport.price}`;
  }

  /**
   * Render placeholder data for Phase 1
   * @param {number} day - Day number
   */
  renderPlaceholderData(day) {
    // This method provides static data for Phase 1 demonstration
    console.log(`📅 DayDetailComponent: Rendering placeholder data for day ${day}`);
    
    // The HTML already contains placeholder data, so no changes needed for Phase 1
    // Phase 2 will replace this with dynamic data loading
  }

  /**
   * Handle accommodation actions
   * @param {string} action - Action type
   */
  handleAccommodationAction(action) {
    console.log(`📅 DayDetailComponent: Accommodation action: ${action}`);
    
    switch (action) {
      case 'View Details':
        this.showAccommodationDetails();
        break;
      case 'Change Hotel':
        this.changeAccommodation();
        break;
    }
  }

  /**
   * Handle restaurant actions
   * @param {string} action - Action type
   * @param {string} restaurantName - Restaurant name
   */
  handleRestaurantAction(action, restaurantName) {
    console.log(`📅 DayDetailComponent: Restaurant action: ${action} for ${restaurantName}`);
    
    switch (action) {
      case 'View Menu':
        this.showRestaurantMenu(restaurantName);
        break;
      case 'Directions':
        this.showDirections(restaurantName);
        break;
    }
  }

  /**
   * Handle activity actions
   * @param {string} action - Action type
   */
  handleActivityAction(action) {
    console.log(`📅 DayDetailComponent: Activity action: ${action}`);
    
    switch (action) {
      case 'View Details':
        this.showActivityDetails();
        break;
      case 'Modify Booking':
        this.modifyActivityBooking();
        break;
    }
  }

  /**
   * Handle transport actions
   * @param {string} action - Action type
   */
  handleTransportAction(action) {
    console.log(`📅 DayDetailComponent: Transport action: ${action}`);
    
    switch (action) {
      case 'Contact Rental':
        this.contactRental();
        break;
      case 'View Route':
        this.showRoute();
        break;
      case 'Modify Booking':
        this.modifyTransportBooking();
        break;
    }
  }

  /**
   * Show accommodation details
   */
  showAccommodationDetails() {
    // Phase 2: Implement accommodation details modal
    console.log('📅 DayDetailComponent: Showing accommodation details');
  }

  /**
   * Change accommodation
   */
  changeAccommodation() {
    // Phase 2: Implement accommodation change flow
    console.log('📅 DayDetailComponent: Changing accommodation');
  }

  /**
   * Show restaurant menu
   * @param {string} restaurantName - Restaurant name
   */
  showRestaurantMenu(restaurantName) {
    // Phase 2: Implement restaurant menu display
    console.log(`📅 DayDetailComponent: Showing menu for ${restaurantName}`);
  }

  /**
   * Show directions
   * @param {string} locationName - Location name
   */
  showDirections(locationName) {
    // Phase 2: Implement directions display
    console.log(`📅 DayDetailComponent: Showing directions to ${locationName}`);
  }

  /**
   * Show activity details
   */
  showActivityDetails() {
    // Phase 2: Implement activity details modal
    console.log('📅 DayDetailComponent: Showing activity details');
  }

  /**
   * Modify activity booking
   */
  modifyActivityBooking() {
    // Phase 2: Implement activity booking modification
    console.log('📅 DayDetailComponent: Modifying activity booking');
  }

  /**
   * Contact rental company
   */
  contactRental() {
    // Phase 2: Implement rental contact flow
    console.log('📅 DayDetailComponent: Contacting rental company');
  }

  /**
   * Show route
   */
  showRoute() {
    // Phase 2: Implement route display
    console.log('📅 DayDetailComponent: Showing route');
  }

  /**
   * Modify transport booking
   */
  modifyTransportBooking() {
    // Phase 2: Implement transport booking modification
    console.log('📅 DayDetailComponent: Modifying transport booking');
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Unsubscribe from state changes
    this.unsubscribers.forEach(unsubscribe => unsubscribe());
    this.unsubscribers = [];
    
    console.log('📅 DayDetailComponent: Destroyed');
  }
}
