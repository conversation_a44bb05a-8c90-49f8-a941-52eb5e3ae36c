/**
 * Account Data Manager
 * Handles loading and managing user data from Supabase database
 * Replaces the mock userData.json approach with real database integration
 */

import { supabase } from '../../supabaseClient.js';
import { getCurrentUser } from '../../core/auth.js';

class AccountDataManager {
  constructor() {
    this.userData = null;
    this.userProfile = null;
    this.userPreferences = null;
    this.userTrips = null;
    this.userNotifications = null;
    this.loaded = false;
  }

  /**
   * Load all user data from database
   * @returns {Object} Complete user data object
   */
  async loadUserData() {
    try {
      console.log('Loading user data from database...');

      // Get current authenticated user
      const user = await getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Load all user data in parallel
      const [profile, preferences, notifications, interests] = await Promise.all([
        this.loadUserProfile(user.id),
        this.loadUserPreferences(user.id),
        this.loadUserNotifications(user.id),
        this.loadUserInterests(user.id)
      ]);

      // Combine all data
      this.userData = {
        userData: {
          ...user,
          ...profile,
          email: user.email, // Ensure email comes from auth
          preferences: this.formatPreferences(preferences),
          travelPreferences: this.formatTravelPreferences(interests),
          notifications: this.formatNotificationSettings(preferences)
        },
        itineraries: [], // Trips moved to separate trips page
        notifications: notifications || [],
        stats: this.calculateUserStats([])
      };

      this.loaded = true;
      console.log('User data loaded successfully:', this.userData);
      return this.userData;
    } catch (error) {
      console.error('Error loading user data:', error);
      throw error;
    }
  }

  /**
   * Load user profile from database
   * @param {string} userId User ID
   * @returns {Object|null} User profile
   */
  async loadUserProfile(userId) {
    try {
      const { data, error } = await supabase
        .from('profile')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error loading user profile:', error);
        return null;
      }

      this.userProfile = data;
      return data;
    } catch (error) {
      console.error('Error loading user profile:', error);
      return null;
    }
  }

  /**
   * Load user system preferences
   * @param {string} userId User ID
   * @returns {Object|null} User preferences
   */
  async loadUserPreferences(userId) {
    try {
      const { data, error } = await supabase
        .from('user_system_preference')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error loading user preferences:', error);
        return null;
      }

      this.userPreferences = data;
      return data;
    } catch (error) {
      console.error('Error loading user preferences:', error);
      return null;
    }
  }



  /**
   * Load user notifications
   * @param {string} userId User ID
   * @returns {Array} User notifications
   */
  async loadUserNotifications(userId) {
    try {
      const { data, error } = await supabase
        .from('notification')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error loading user notifications:', error);
        return [];
      }

      // Format notifications for the UI
      const formattedNotifications = data.map(notification => ({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        timestamp: notification.timestamp,
        isRead: notification.is_read,
        actionUrl: notification.action_url
      }));

      this.userNotifications = formattedNotifications;
      return formattedNotifications;
    } catch (error) {
      console.error('Error loading user notifications:', error);
      return [];
    }
  }

  /**
   * Load user interests with weights
   * @param {string} userId User ID
   * @returns {Array} User interests with weights
   */
  async loadUserInterests(userId) {
    try {
      // First try to get user interests
      const { data, error } = await supabase
        .from('user_interest_junction')
        .select(`
          weight,
          interest_id,
          interest_category (
            id,
            name,
            description,
            type
          )
        `)
        .eq('user_id', userId);

      if (error) {
        console.error('Error loading user interests:', error);
        return [];
      }

      // Format interests for the UI
      const formattedInterests = data.map(item => ({
        id: item.interest_category.id,
        name: item.interest_category.name,
        description: item.interest_category.description,
        type: item.interest_category.type,
        weight: parseFloat(item.weight) || 0.5
      }));

      return formattedInterests;
    } catch (error) {
      console.error('Error loading user interests:', error);
      return [];
    }
  }



  /**
   * Format preferences for UI
   * @param {Object} preferences Raw preferences from database
   * @returns {Object} Formatted preferences
   */
  formatPreferences(preferences) {
    if (!preferences) {
      return {
        currency: 'USD',
        language: 'English',
        timezone: 'UTC',
        dateFormat: 'YYYY-MM-DD',
        theme: 'auto'
      };
    }

    return {
      currency: preferences.default_currency || 'USD',
      language: preferences.default_language || 'English',
      timezone: preferences.timezone || 'UTC',
      dateFormat: preferences.date_format || 'YYYY-MM-DD',
      theme: preferences.theme_preference || 'auto'
    };
  }

  /**
   * Format travel preferences from interests
   * @param {Array} interests User interests with weights
   * @returns {Object} Travel preferences with interest IDs as keys
   */
  formatTravelPreferences(interests) {
    // Default preferences with interest IDs as keys
    const defaultPreferences = {
      2: { name: 'Historical Sites & Museums', weight: 0.5 },
      3: { name: 'Local Cuisine & Food Tours', weight: 0.5 },
      4: { name: 'Outdoor Activities & Nature', weight: 0.5 },
      5: { name: 'Shopping & Markets', weight: 0.5 },
      6: { name: 'Arts & Cultural Events', weight: 0.5 }
    };

    if (!interests || interests.length === 0) {
      return defaultPreferences;
    }

    // Update with actual user preferences
    interests.forEach(interest => {
      if (defaultPreferences[interest.id]) {
        defaultPreferences[interest.id].weight = interest.weight;
      }
    });

    return defaultPreferences;
  }

  /**
   * Format notification settings
   * @param {Object} preferences User preferences
   * @returns {Object} Notification settings
   */
  formatNotificationSettings(preferences) {
    if (!preferences) {
      return {
        email: true,
        trip_updates: true,
        sms: false,
        marketing: false
      };
    }

    return {
      email: preferences.email_notifications || false,
      trip_updates: preferences.trip_updates || false,
      sms: preferences.sms_notifications || false,
      marketing: preferences.marketing || false
    };
  }



  /**
   * Get default avatar for user
   * @param {Object} user User object
   * @returns {string} Avatar URL
   */
  getDefaultAvatar(user) {
    const name = user?.name || user?.email?.split('@')[0] || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=f97316&color=fff&size=128`;
  }

  /**
   * Calculate user statistics
   * @param {Array} trips User trips
   * @returns {Object} User statistics
   */
  calculateUserStats(trips) {
    if (!trips || trips.length === 0) {
      return {
        totalTrips: 0,
        upcomingTrips: 0,
        completedTrips: 0,
        totalSpent: 0
      };
    }

    const now = new Date();
    const upcoming = trips.filter(trip => new Date(trip.startDate) > now);
    const completed = trips.filter(trip => new Date(trip.endDate) < now);
    const totalSpent = trips.reduce((sum, trip) => sum + (trip.budget || 0), 0);

    return {
      totalTrips: trips.length,
      upcomingTrips: upcoming.length,
      completedTrips: completed.length,
      totalSpent: totalSpent
    };
  }

  /**
   * Get user data (load if not already loaded)
   * @returns {Object} User data
   */
  async getUserData() {
    if (!this.loaded) {
      await this.loadUserData();
    }
    return this.userData;
  }

  /**
   * Refresh user data
   * @returns {Object} Refreshed user data
   */
  async refreshUserData() {
    this.loaded = false;
    return await this.loadUserData();
  }

  /**
   * Upload user avatar to Supabase Storage
   * @param {File} file Avatar image file
   * @returns {Object} Upload result with avatar URL
   */
  async uploadAvatar(file) {
    try {
      const user = await getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/avatar.${fileExt}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        throw error;
      }

      // Get public URL with cache-busting timestamp
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      // Add cache-busting timestamp to ensure browser doesn't cache old image
      const cacheBustedUrl = `${publicUrl}?t=${Date.now()}`;

      // Update profile with new avatar URL (without cache-busting for storage)
      const { error: updateError } = await supabase
        .from('profile')
        .update({ avatar_url: publicUrl })
        .eq('id', user.id);

      if (updateError) {
        throw updateError;
      }

      // Return cache-busted URL for immediate UI update
      return { success: true, avatarUrl: cacheBustedUrl };
    } catch (error) {
      console.error('Error uploading avatar:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user profile data
   * @param {Object} profileData Profile data to update
   * @returns {Object} Update result
   */
  async updateProfile(profileData) {
    try {
      const user = await getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('profile')
        .update(profileData)
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user preferences
   * @param {Object} preferences Preferences to update
   * @returns {Object} Update result
   */
  async updatePreferences(preferences) {
    try {
      const user = await getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Use upsert to handle cases where no preference record exists
      const { data, error } = await supabase
        .from('user_system_preference')
        .upsert({
          user_id: user.id,
          ...preferences
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user travel interests
   * @param {Object} interests Object with interest IDs as keys and weights as values
   * @returns {Object} Update result
   */
  async updateTravelInterests(interests) {
    try {
      const user = await getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Delete existing user interests
      await supabase
        .from('user_interest_junction')
        .delete()
        .eq('user_id', user.id);

      // Insert new interests
      const interestData = [];
      for (const [interestId, weight] of Object.entries(interests)) {
        const numericWeight = parseFloat(weight);
        if (numericWeight > 0) {
          interestData.push({
            user_id: user.id,
            interest_id: parseInt(interestId),
            weight: numericWeight
          });
        }
      }

      if (interestData.length > 0) {
        const { error: insertError } = await supabase
          .from('user_interest_junction')
          .insert(interestData);

        if (insertError) {
          throw insertError;
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating travel interests:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create and export singleton instance
const accountDataManager = new AccountDataManager();
export default accountDataManager;
