/**
 * Trips Data Manager - Handles all trip data operations
 */

import { supabase } from '../../supabaseClient.js';
import { getCurrentUser } from '../../core/auth.js';

class TripsDataManager {
    constructor() {
        this.trips = [];
        this.loaded = false;
    }

    /**
     * Load user trips from Supabase
     * @returns {Array} Array of user trips
     */
    async loadUserTrips() {
        try {
            console.log('Loading user trips from database...');

            // Get current user
            const user = await getCurrentUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            console.log('Loading trips for user:', user.id);

            // Query trips from Supabase
            const { data, error } = await supabase
                .from('trip')
                .select('*')
                .eq('user_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Supabase error loading trips:', error);
                throw new Error(`Failed to load trips: ${error.message}`);
            }

            console.log(`Successfully loaded ${data?.length || 0} trips from database`);
            
            // Store trips
            this.trips = data || [];
            this.loaded = true;

            return this.trips;

        } catch (error) {
            console.error('Error in loadUserTrips:', error);
            throw error;
        }
    }

    /**
     * Get a specific trip by ID
     * @param {string} tripId Trip ID
     * @returns {Object|null} Trip object or null if not found
     */
    async getTripById(tripId) {
        try {
            // If trips not loaded, load them first
            if (!this.loaded) {
                await this.loadUserTrips();
            }

            // Find trip in loaded data
            const trip = this.trips.find(t => t.id.toString() === tripId.toString());
            if (trip) {
                return trip;
            }

            // If not found in loaded data, query database directly
            console.log('Trip not found in loaded data, querying database...');
            
            const user = await getCurrentUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            const { data, error } = await supabase
                .from('trip')
                .select('*')
                .eq('id', tripId)
                .eq('user_id', user.id)
                .single();

            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Trip not found
                }
                throw new Error(`Failed to load trip: ${error.message}`);
            }

            return data;

        } catch (error) {
            console.error('Error getting trip by ID:', error);
            throw error;
        }
    }

    /**
     * Create a new trip
     * @param {Object} tripData Trip data
     * @returns {Object} Created trip
     */
    async createTrip(tripData) {
        try {
            const user = await getCurrentUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            const newTrip = {
                user_id: user.id,
                title: tripData.title,
                description: tripData.description,
                start_date: tripData.start_date,
                end_date: tripData.end_date,
                budget_total: tripData.budget_total,
                currency: tripData.currency || 'MAD',
                status: tripData.status || 'draft',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const { data, error } = await supabase
                .from('trip')
                .insert([newTrip])
                .select()
                .single();

            if (error) {
                throw new Error(`Failed to create trip: ${error.message}`);
            }

            // Add to local trips array
            this.trips.unshift(data);

            console.log('Trip created successfully:', data);
            return data;

        } catch (error) {
            console.error('Error creating trip:', error);
            throw error;
        }
    }

    /**
     * Update an existing trip
     * @param {string} tripId Trip ID
     * @param {Object} updateData Data to update
     * @returns {Object} Updated trip
     */
    async updateTrip(tripId, updateData) {
        try {
            const user = await getCurrentUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            const updates = {
                ...updateData,
                updated_at: new Date().toISOString()
            };

            const { data, error } = await supabase
                .from('trip')
                .update(updates)
                .eq('id', tripId)
                .eq('user_id', user.id)
                .select()
                .single();

            if (error) {
                throw new Error(`Failed to update trip: ${error.message}`);
            }

            // Update local trips array
            const index = this.trips.findIndex(t => t.id.toString() === tripId.toString());
            if (index !== -1) {
                this.trips[index] = data;
            }

            console.log('Trip updated successfully:', data);
            return data;

        } catch (error) {
            console.error('Error updating trip:', error);
            throw error;
        }
    }

    /**
     * Delete a trip
     * @param {string} tripId Trip ID
     * @returns {boolean} Success status
     */
    async deleteTrip(tripId) {
        try {
            const user = await getCurrentUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            const { error } = await supabase
                .from('trip')
                .delete()
                .eq('id', tripId)
                .eq('user_id', user.id);

            if (error) {
                throw new Error(`Failed to delete trip: ${error.message}`);
            }

            // Remove from local trips array
            this.trips = this.trips.filter(t => t.id.toString() !== tripId.toString());

            console.log('Trip deleted successfully');
            return true;

        } catch (error) {
            console.error('Error deleting trip:', error);
            throw error;
        }
    }

    /**
     * Get trip statistics
     * @returns {Object} Trip statistics
     */
    getStatistics() {
        if (!this.loaded || this.trips.length === 0) {
            return {
                total: 0,
                upcoming: 0,
                past: 0,
                drafts: 0,
                totalBudget: 0
            };
        }

        const now = new Date();
        const stats = {
            total: this.trips.length,
            upcoming: 0,
            past: 0,
            drafts: 0,
            totalBudget: 0
        };

        this.trips.forEach(trip => {
            const startDate = new Date(trip.start_date);
            const endDate = new Date(trip.end_date);

            if (trip.status === 'draft') {
                stats.drafts++;
            } else if (startDate > now) {
                stats.upcoming++;
            } else if (endDate < now) {
                stats.past++;
            }

            stats.totalBudget += trip.budget_total || 0;
        });

        return stats;
    }

    /**
     * Refresh trips data
     * @returns {Array} Refreshed trips array
     */
    async refresh() {
        this.loaded = false;
        return await this.loadUserTrips();
    }

    /**
     * Get cached trips (if already loaded)
     * @returns {Array} Cached trips array
     */
    getCachedTrips() {
        return this.trips;
    }

    /**
     * Check if trips are loaded
     * @returns {boolean} Load status
     */
    isLoaded() {
        return this.loaded;
    }
}

// Create and export singleton instance
const tripsDataManager = new TripsDataManager();
export default tripsDataManager;
