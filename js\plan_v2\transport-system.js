/**
 * Unified Transport Management System
 * 
 * This module combines transport calculation, data processing, and state management
 * into a single, cohesive system that handles the complete transport workflow.
 * 
 * Features:
 * - Transport calculation using OSRM API
 * - Database integration (read from get_trip_plan_json, write to trip_transport_calculated)
 * - Budget allocation updates
 * - Consistent data structure output for UI consumption
 * - Robust error handling with graceful fallbacks
 * - State management for transport data
 */

import { supabase } from '../supabaseClient.js';

export class TransportSystem {
  constructor() {
    // Transport calculation constants
    this.DAILY_CAR_RENTAL_USD = 20.0;
    this.USD_TO_MAD_RATE = 9.5;
    this.FUEL_EFFICIENCY_KMPL = 17.5; // km per liter
    this.GASOLINE_PRICE_USD = 1.20; // per liter
    
    // Taxi pricing constants
    this.TAXI_BASE_FARE_MAD = 7.50;
    this.TAXI_RATE_PER_80M_MAD = 0.20;
    this.TAXI_MARGIN = 1.20; // 20% margin
    
    // Walking threshold
    this.WALKING_THRESHOLD_KM = 0.5;
    this.WALKING_SPEED_KMH = 4.0;
    
    // OSRM API base URL
    this.OSRM_BASE_URL = 'https://osrm-morocco-308196743891.europe-west1.run.app';
    
    // Transport category ID for budget allocation
    this.TRANSPORT_CATEGORY_ID = 4;

    // Train pricing constants
    this.TRAIN_BASE_FARE_USD = 15.0; // Base fare for train travel
    this.TRAIN_RATE_PER_KM_USD = 0.08; // Rate per kilometer for train
    this.TRAIN_COMFORT_MULTIPLIER = 1.1; // 10% premium for comfort

    // State management
    this.currentTripId = null;
    this.transportData = null;
    this.isCalculating = false;
    this.cache = new Map();
    this.trainStationsCache = new Map();
  }

  /**
   * Initialize the transport system for a specific trip
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Initialization result
   */
  async initialize(tripId) {
    console.log(`🚀 TransportSystem: Initializing for trip ${tripId}`);
    
    try {
      this.currentTripId = tripId;
      
      // Check if transport data already exists
      const existingData = await this.getExistingTransportData(tripId);
      
      if (existingData.success && existingData.data.length > 0) {
        console.log('✅ Found existing transport data');
        this.transportData = await this.formatTransportDataForUI(existingData.data);
        return {
          success: true,
          hasExistingData: true,
          transportData: this.transportData
        };
      } else {
        console.log('⚠️ No existing transport data found');
        return {
          success: true,
          hasExistingData: false,
          transportData: null
        };
      }
      
    } catch (error) {
      console.error('❌ TransportSystem initialization failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get existing transport data from database
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Existing transport data
   */
  async getExistingTransportData(tripId) {
    try {
      const { data: transportData, error } = await supabase
        .from('trip_transport_calculated')
        .select('*')
        .eq('trip_id', tripId)
        .order('day_number', { ascending: true })
        .order('transport_sequence', { ascending: true });

      if (error) throw error;

      return {
        success: true,
        data: transportData || []
      };

    } catch (error) {
      console.error('❌ Error fetching existing transport data:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Calculate complete transport system for the trip
   * @param {boolean} forceRecalculate - Force recalculation even if data exists
   * @returns {Promise<Object>} Calculation result
   */
  async calculateTransport(forceRecalculate = false) {
    if (!this.currentTripId) {
      throw new Error('Transport system not initialized with trip ID');
    }

    if (this.isCalculating) {
      console.warn('⚠️ Transport calculation already in progress');
      return { success: false, error: 'Calculation already in progress' };
    }

    this.isCalculating = true;

    try {
      console.log(`🔄 Starting transport calculation for trip ${this.currentTripId}`);
      
      // Clear existing data if force recalculate
      if (forceRecalculate) {
        await this.clearExistingTransportData(this.currentTripId);
      }
      
      // Get trip data for calculations
      const tripData = await this.getTripDataForCalculation(this.currentTripId);
      if (!tripData.success) {
        throw new Error(tripData.error);
      }
      
      // Calculate transport for each day
      const calculationResults = {
        tripId: this.currentTripId,
        dailyTransport: [],
        interCityTransport: [],
        totalCostUSD: 0,
        totalSegments: 0
      };
      
      // Process each day
      for (const day of tripData.days) {
        console.log(`📅 Calculating transport for Day ${day.dayNumber}`);
        const dailyResult = await this.calculateDailyTransport(day);
        calculationResults.dailyTransport.push(dailyResult);
        calculationResults.totalCostUSD += dailyResult.totalCostUSD;
        calculationResults.totalSegments += dailyResult.segments.length;
      }
      
      // Calculate inter-city transport
      const interCityResults = await this.calculateInterCityTransport(tripData.days);
      calculationResults.interCityTransport = interCityResults;
      calculationResults.totalCostUSD += interCityResults.reduce((sum, route) => sum + route.costUSD, 0);
      calculationResults.totalSegments += interCityResults.length;
      
      // Save to database
      await this.saveTransportToDatabase(calculationResults);
      
      // Update budget allocations
      await this.updateBudgetAllocations(this.currentTripId, calculationResults.totalCostUSD, calculationResults.dailyTransport);
      
      // Format data for UI consumption
      this.transportData = await this.formatCalculationResultsForUI(calculationResults);
      
      console.log(`✅ Transport calculation completed. Total: $${calculationResults.totalCostUSD.toFixed(2)}, ${calculationResults.totalSegments} segments`);
      
      return {
        success: true,
        transportData: this.transportData,
        summary: {
          totalSegments: calculationResults.totalSegments,
          totalCostUSD: calculationResults.totalCostUSD,
          totalDays: calculationResults.dailyTransport.length,
          interCityRoutes: calculationResults.interCityTransport.length
        }
      };
      
    } catch (error) {
      console.error('❌ Transport calculation failed:', error);
      return {
        success: false,
        error: error.message
      };
    } finally {
      this.isCalculating = false;
    }
  }

  /**
   * Get transport data formatted for UI consumption
   * @param {number} dayNumber - Specific day number (optional)
   * @returns {Object} Transport data formatted for UI
   */
  getTransportDataForUI(dayNumber = null) {
    if (!this.transportData) {
      return {
        success: false,
        error: 'No transport data available'
      };
    }

    if (dayNumber) {
      const dayData = this.transportData.dailyTransport.find(day => day.dayNumber === dayNumber);
      const interCityData = this.transportData.interCityTransport.find(route => route.fromDay === dayNumber);
      
      return {
        success: true,
        currentDay: dayData || null,
        interCity: interCityData || null,
        totalCost: this.transportData.totalCostUSD
      };
    }

    return {
      success: true,
      data: this.transportData
    };
  }

  /**
   * Get transport status for the current trip
   * @returns {Object} Transport status information
   */
  getTransportStatus() {
    return {
      isInitialized: this.currentTripId !== null,
      hasData: this.transportData !== null,
      isCalculating: this.isCalculating,
      tripId: this.currentTripId,
      totalCost: this.transportData?.totalCostUSD || 0,
      totalSegments: this.transportData?.totalSegments || 0
    };
  }

  /**
   * Get trip data for transport calculations
   * @param {number} tripId - Trip ID
   * @returns {Promise<Object>} Trip data formatted for calculations
   */
  async getTripDataForCalculation(tripId) {
    try {
      const { data: tripData, error } = await supabase.rpc('get_trip_plan_json', {
        p_trip_id: tripId,
        p_current_day: 1
      });

      if (error) throw error;
      if (!tripData || !tripData.days) throw new Error('No trip data found');

      // Transform data for transport calculations
      const days = tripData.days.map(day => ({
        dayNumber: day.day_number,
        date: day.date,
        cityName: day.location?.city || day.city_name || 'Unknown City',

        // Extract locations with coordinates
        accommodation: this.extractLocationData(day.lodging),
        breakfast: this.extractMealLocation(day.meals, 'breakfast'),
        lunch: this.extractMealLocation(day.meals, 'lunch'),
        dinner: this.extractMealLocation(day.meals, 'dinner'),
        activities: (day.activities || []).map(activity => this.extractLocationData(activity)).filter(Boolean)
      }));

      return {
        success: true,
        days: days.filter(day => day.accommodation || day.activities.length > 0)
      };

    } catch (error) {
      console.error('❌ Error getting trip data for calculation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract location data from trip item with robust coordinate handling
   * @param {Object} item - Trip item (lodging, activity, etc.)
   * @returns {Object|null} Location data with coordinates and location ID
   */
  extractLocationData(item) {
    if (!item || !item.location) {
      console.warn('⚠️ Missing item or location data:', item);
      return null;
    }

    let lat, lng;

    // Try multiple coordinate extraction patterns
    if (item.location.coordinates) {
      lat = parseFloat(item.location.coordinates.lat);
      lng = parseFloat(item.location.coordinates.lng);
    } else if (item.location.latitude && item.location.longitude) {
      lat = parseFloat(item.location.latitude);
      lng = parseFloat(item.location.longitude);
    } else if (item.coordinates) {
      lat = parseFloat(item.coordinates.lat);
      lng = parseFloat(item.coordinates.lng);
    }

    // Validate coordinates
    if (isNaN(lat) || isNaN(lng) || Math.abs(lat) > 90 || Math.abs(lng) > 180) {
      console.warn(`⚠️ Invalid coordinates for ${item.name || 'Unknown'}: lat=${lat}, lng=${lng}`);
      return null;
    }

    return {
      name: item.name || item.title || 'Unknown Location',
      address: item.location.address || '',
      coordinates: { lat, lng },
      locationId: item.location.id || item.location_id || null // Include location ID for city lookup
    };
  }

  /**
   * Extract meal location by type
   * @param {Array} meals - Array of meals
   * @param {string} mealType - Type of meal (breakfast, lunch, dinner)
   * @returns {Object|null} Meal location data
   */
  extractMealLocation(meals, mealType) {
    if (!meals || !Array.isArray(meals)) {
      return null;
    }

    const meal = meals.find(m => m.type === mealType);
    if (!meal || !meal.restaurant) {
      return null;
    }

    return this.extractLocationData(meal.restaurant);
  }

  /**
   * Calculate daily transport segments
   * @param {Object} day - Day data with locations
   * @returns {Promise<Object>} Daily transport calculation
   */
  async calculateDailyTransport(day) {
    const segments = [];
    let totalCostUSD = 0;

    // Build transport sequence: Hotel→Breakfast→Activity→Lunch→Hotel→Dinner→Hotel
    const transportSequence = this.buildTransportSequence(day);

    for (let i = 0; i < transportSequence.length; i++) {
      const { from, to, description } = transportSequence[i];

      if (!from || !to) {
        console.warn(`⚠️ Missing location for segment: ${description}`);
        continue;
      }

      const segment = await this.calculateTransportSegment(from, to, description, i + 1);
      segments.push(segment);
      totalCostUSD += segment.costUSD;
    }

    return {
      dayNumber: day.dayNumber,
      date: day.date,
      cityName: day.cityName,
      segments,
      totalCostUSD
    };
  }

  /**
   * Build transport sequence for a day
   * @param {Object} day - Day data
   * @returns {Array} Transport sequence
   */
  buildTransportSequence(day) {
    const sequence = [];

    if (!day.accommodation) {
      console.warn(`⚠️ No accommodation found for day ${day.dayNumber}`);
      return sequence;
    }

    // 1. Hotel → Breakfast Restaurant
    if (day.breakfast) {
      sequence.push({
        from: day.accommodation,
        to: day.breakfast,
        description: 'Hotel to Breakfast Restaurant'
      });
    }

    // 2. Restaurant/Hotel → Activity
    if (day.activities.length > 0) {
      sequence.push({
        from: day.breakfast || day.accommodation,
        to: day.activities[0],
        description: 'To Activity'
      });
    }

    // 3. Activity → Lunch Restaurant
    if (day.lunch && day.activities.length > 0) {
      sequence.push({
        from: day.activities[0],
        to: day.lunch,
        description: 'Activity to Lunch Restaurant'
      });
    }

    // 4. Restaurant → Hotel
    if (day.lunch) {
      sequence.push({
        from: day.lunch,
        to: day.accommodation,
        description: 'Lunch Restaurant to Hotel'
      });
    }

    // 5. Hotel → Dinner Restaurant
    if (day.dinner) {
      sequence.push({
        from: day.accommodation,
        to: day.dinner,
        description: 'Hotel to Dinner Restaurant'
      });

      // 6. Restaurant → Hotel
      sequence.push({
        from: day.dinner,
        to: day.accommodation,
        description: 'Dinner Restaurant to Hotel'
      });
    }

    return sequence;
  }

  /**
   * Calculate transport segment between two locations with alternative options
   * @param {Object} from - Origin location
   * @param {Object} to - Destination location
   * @param {string} description - Segment description
   * @param {number} sequence - Sequence number
   * @returns {Promise<Object>} Transport segment calculation with alternatives
   */
  async calculateTransportSegment(from, to, description, sequence) {
    try {
      // Get distance and duration
      const routeData = await this.getRouteData(from.coordinates, to.coordinates);

      // Get all transport options
      const transportOptions = this.getAllTransportOptions(routeData.distance, routeData.duration);

      // Sort by cost to determine primary option
      transportOptions.sort((a, b) => a.costUSD - b.costUSD);
      const primaryOption = transportOptions[0];
      const alternativeOptions = transportOptions.slice(1);

      return {
        sequence,
        description,
        from: from.name,
        to: to.name,
        distance: routeData.distance,
        duration: routeData.duration,
        mode: primaryOption.type,
        costUSD: primaryOption.costUSD,
        costMAD: primaryOption.costMAD,
        reasoning: primaryOption.reasoning,
        primaryOption: {
          mode: primaryOption.type,
          costUSD: primaryOption.costUSD,
          costMAD: primaryOption.costMAD,
          reasoning: primaryOption.reasoning
        },
        alternativeOptions: alternativeOptions.map(option => ({
          mode: option.type,
          costUSD: option.costUSD,
          costMAD: option.costMAD,
          reasoning: option.reasoning
        }))
      };

    } catch (error) {
      console.error(`❌ Error calculating segment ${description}:`, error);

      // Return fallback calculation
      return {
        sequence,
        description,
        from: from.name,
        to: to.name,
        distance: 5.0,
        duration: 15,
        mode: 'taxi',
        costUSD: 5.0,
        costMAD: 47.5,
        reasoning: 'Fallback calculation due to routing error',
        primaryOption: {
          mode: 'taxi',
          costUSD: 5.0,
          costMAD: 47.5,
          reasoning: 'Fallback calculation due to routing error'
        },
        alternativeOptions: []
      };
    }
  }

  /**
   * Get route data using OSRM API with fallback
   * @param {Object} from - Origin coordinates {lat, lng}
   * @param {Object} to - Destination coordinates {lat, lng}
   * @returns {Promise<Object>} Route data with distance and duration
   */
  async getRouteData(from, to) {
    try {
      // Validate coordinates
      if (!from || !to || typeof from.lat !== 'number' || typeof from.lng !== 'number' ||
          typeof to.lat !== 'number' || typeof to.lng !== 'number') {
        throw new Error('Invalid coordinates provided');
      }

      const url = `${this.OSRM_BASE_URL}/route/v1/driving/${from.lng},${from.lat};${to.lng},${to.lat}?overview=false&alternatives=false&steps=false`;

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`OSRM API error: ${response.status}`);
      }

      const data = await response.json();
      if (!data.routes || data.routes.length === 0) {
        throw new Error('No route found in OSRM response');
      }

      const route = data.routes[0];
      return {
        distance: route.distance / 1000, // Convert to km
        duration: route.duration / 60 // Convert to minutes
      };

    } catch (error) {
      console.warn(`⚠️ OSRM API failed, using fallback calculation:`, error.message);

      // Fallback to straight-line distance with road factor
      const straightDistance = this.calculateStraightLineDistance(from, to);
      return {
        distance: straightDistance * 1.3, // Apply road factor
        duration: (straightDistance * 1.3) / 30 * 60 // Assume 30 km/h average speed
      };
    }
  }

  /**
   * Calculate straight-line distance between two coordinates
   * @param {Object} from - Origin coordinates {lat, lng}
   * @param {Object} to - Destination coordinates {lat, lng}
   * @returns {number} Distance in kilometers
   */
  calculateStraightLineDistance(from, to) {
    const R = 6371; // Earth's radius in kilometers
    const φ1 = from.lat * Math.PI / 180;
    const φ2 = to.lat * Math.PI / 180;
    const Δφ = (to.lat - from.lat) * Math.PI / 180;
    const Δλ = (to.lng - from.lng) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * Get all available transport options for a route
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Array} Array of all transport options
   */
  getAllTransportOptions(distanceKm, durationMinutes) {
    const options = [];

    // Walking for short distances
    if (distanceKm <= this.WALKING_THRESHOLD_KM) {
      options.push({
        type: 'walking',
        costUSD: 0,
        costMAD: 0,
        reasoning: `Short distance (${distanceKm.toFixed(2)}km), walking is optimal`
      });
    }

    // Calculate taxi cost
    const taxiCostMAD = (this.TAXI_BASE_FARE_MAD + (distanceKm * 1000 / 80.0) * this.TAXI_RATE_PER_80M_MAD) * this.TAXI_MARGIN;
    const taxiCostUSD = taxiCostMAD / this.USD_TO_MAD_RATE;

    options.push({
      type: 'taxi',
      costUSD: taxiCostUSD,
      costMAD: taxiCostMAD,
      reasoning: `Taxi: $${this.TAXI_BASE_FARE_MAD.toFixed(2)} MAD base + distance charges`
    });

    // Calculate car rental cost (prorated daily fee + fuel) - only for longer distances
    if (distanceKm > this.WALKING_THRESHOLD_KM) {
      const fuelCostUSD = (distanceKm / this.FUEL_EFFICIENCY_KMPL) * this.GASOLINE_PRICE_USD;
      const proratedRentalFeeUSD = this.DAILY_CAR_RENTAL_USD / 6.0; // Assume 6 segments per day
      const carCostUSD = fuelCostUSD + proratedRentalFeeUSD;
      const carCostMAD = carCostUSD * this.USD_TO_MAD_RATE;

      options.push({
        type: 'car_rental',
        costUSD: carCostUSD,
        costMAD: carCostMAD,
        reasoning: `Car rental: $${proratedRentalFeeUSD.toFixed(2)} prorated fee + $${fuelCostUSD.toFixed(2)} fuel`
      });
    }

    return options;
  }

  /**
   * Select best transport option based on distance and duration (legacy method)
   * @param {number} distanceKm - Distance in kilometers
   * @param {number} durationMinutes - Duration in minutes
   * @returns {Object} Best transport option
   */
  selectBestTransportOption(distanceKm, durationMinutes) {
    const options = this.getAllTransportOptions(distanceKm, durationMinutes);
    return options.sort((a, b) => a.costUSD - b.costUSD)[0];
  }

  /**
   * Calculate inter-city transport
   * @param {Array} days - Array of day data
   * @returns {Promise<Array>} Inter-city transport routes
   */
  async calculateInterCityTransport(days) {
    const interCityRoutes = [];

    // Find city transitions
    for (let i = 0; i < days.length - 1; i++) {
      const currentDay = days[i];
      const nextDay = days[i + 1];

      if (currentDay.cityName !== nextDay.cityName) {
        try {
          const route = await this.calculateInterCityRoute(currentDay, nextDay);
          interCityRoutes.push(route);
        } catch (error) {
          console.error(`❌ Error calculating inter-city route ${currentDay.cityName} → ${nextDay.cityName}:`, error);
        }
      }
    }

    return interCityRoutes;
  }

  /**
   * Calculate inter-city route with train option support
   * @param {Object} fromDay - Origin day data
   * @param {Object} toDay - Destination day data
   * @returns {Promise<Object>} Inter-city route calculation with alternatives
   */
  async calculateInterCityRoute(fromDay, toDay) {
    const fromCoords = fromDay.accommodation?.coordinates;
    const toCoords = toDay.accommodation?.coordinates;

    if (!fromCoords || !toCoords) {
      throw new Error('Missing accommodation coordinates for inter-city route');
    }

    // Get route data for distance calculation
    const routeData = await this.getRouteData(fromCoords, toCoords);

    // Get city IDs for train station lookup
    const fromCityId = await this.getCityIdFromAccommodation(fromDay.accommodation);
    const toCityId = await this.getCityIdFromAccommodation(toDay.accommodation);

    // Calculate car rental option
    const carOption = this.calculateCarRentalOption(routeData);

    // Calculate train option if available
    const trainOption = await this.calculateTrainOption(fromCityId, toCityId, routeData, fromDay.cityName, toDay.cityName);

    // Determine primary and alternative options
    const options = [carOption];
    if (trainOption) {
      options.push(trainOption);
    }

    // Sort by cost to determine primary option
    options.sort((a, b) => a.costUSD - b.costUSD);
    const primaryOption = options[0];
    const alternativeOptions = options.slice(1);

    return {
      fromCity: fromDay.cityName,
      toCity: toDay.cityName,
      fromDay: fromDay.dayNumber,
      toDay: toDay.dayNumber,
      distance: routeData.distance,
      duration: routeData.duration,
      mode: primaryOption.mode,
      costUSD: primaryOption.costUSD,
      costMAD: primaryOption.costMAD,
      description: `Inter-city transport from ${fromDay.cityName} to ${toDay.cityName}`,
      reasoning: primaryOption.reasoning,
      primaryOption: primaryOption,
      alternativeOptions: alternativeOptions,
      trainStations: trainOption ? trainOption.stations : null
    };
  }

  /**
   * Get city ID from accommodation data
   * @param {Object} accommodation - Accommodation data with location info
   * @returns {Promise<number|null>} City ID or null if not found
   */
  async getCityIdFromAccommodation(accommodation) {
    if (!accommodation) {
      console.warn('⚠️ No accommodation data provided');
      return null;
    }

    // Try to get location ID from various possible fields
    const locationId = accommodation.locationId ||
                      accommodation.location_id ||
                      accommodation.location?.id ||
                      null;

    if (!locationId) {
      console.warn('⚠️ No location ID found in accommodation data:', accommodation);
      return null;
    }

    try {
      // Check cache first
      const cacheKey = `city_id_${locationId}`;
      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }

      // Query location table to get city_id
      const { data: locationData, error } = await supabase
        .from('location')
        .select('city_id')
        .eq('id', locationId)
        .single();

      if (error) {
        console.warn('⚠️ Error fetching city ID from location:', error);
        return null;
      }

      const cityId = locationData?.city_id;
      if (cityId) {
        // Cache the result
        this.cache.set(cacheKey, cityId);
        console.log(`✅ Found city ID ${cityId} for location ${locationId}`);
      }

      return cityId;

    } catch (error) {
      console.error('❌ Error getting city ID from accommodation:', error);
      return null;
    }
  }

  /**
   * Calculate car rental option for inter-city transport
   * @param {Object} routeData - Route data with distance and duration
   * @returns {Object} Car rental option
   */
  calculateCarRentalOption(routeData) {
    const fuelCostUSD = (routeData.distance / this.FUEL_EFFICIENCY_KMPL) * this.GASOLINE_PRICE_USD;
    const totalCostUSD = fuelCostUSD + this.DAILY_CAR_RENTAL_USD;
    const totalCostMAD = totalCostUSD * this.USD_TO_MAD_RATE;

    return {
      mode: 'car_rental',
      costUSD: totalCostUSD,
      costMAD: totalCostMAD,
      reasoning: `Car rental: $${this.DAILY_CAR_RENTAL_USD} daily fee + $${fuelCostUSD.toFixed(2)} fuel costs`,
      duration: routeData.duration,
      distance: routeData.distance
    };
  }

  /**
   * Calculate train option for inter-city transport
   * @param {number} fromCityId - Source city ID
   * @param {number} toCityId - Destination city ID
   * @param {Object} routeData - Route data for distance
   * @param {string} fromCityName - Source city name
   * @param {string} toCityName - Destination city name
   * @returns {Promise<Object|null>} Train option or null if not available
   */
  async calculateTrainOption(fromCityId, toCityId, routeData, fromCityName, toCityName) {
    if (!fromCityId || !toCityId) {
      console.log('⚠️ Missing city IDs for train route calculation');
      return null;
    }

    try {
      // Get train stations for both cities
      const fromStations = await this.getTrainStationsForCity(fromCityId);
      const toStations = await this.getTrainStationsForCity(toCityId);

      if (fromStations.length === 0 || toStations.length === 0) {
        console.log(`⚠️ No train stations found: ${fromCityName} (${fromStations.length}) → ${toCityName} (${toStations.length})`);
        return null;
      }

      // Calculate train pricing
      const trainCostUSD = this.TRAIN_BASE_FARE_USD + (routeData.distance * this.TRAIN_RATE_PER_KM_USD * this.TRAIN_COMFORT_MULTIPLIER);
      const trainCostMAD = trainCostUSD * this.USD_TO_MAD_RATE;

      // Estimate train duration (typically slower than car but more direct)
      const trainDuration = routeData.duration * 1.2; // 20% longer than car

      console.log(`🚂 Train option available: ${fromCityName} → ${toCityName}, $${trainCostUSD.toFixed(2)}`);

      return {
        mode: 'train',
        costUSD: trainCostUSD,
        costMAD: trainCostMAD,
        reasoning: `Train: $${this.TRAIN_BASE_FARE_USD} base fare + $${(routeData.distance * this.TRAIN_RATE_PER_KM_USD * this.TRAIN_COMFORT_MULTIPLIER).toFixed(2)} distance fee`,
        duration: trainDuration,
        distance: routeData.distance,
        stations: {
          from: fromStations,
          to: toStations
        }
      };

    } catch (error) {
      console.error('❌ Error calculating train option:', error);
      return null;
    }
  }

  /**
   * Get train stations for a specific city
   * @param {number} cityId - City ID
   * @returns {Promise<Array>} Array of train stations
   */
  async getTrainStationsForCity(cityId) {
    try {
      // Check cache first
      const cacheKey = `train_stations_${cityId}`;
      if (this.trainStationsCache.has(cacheKey)) {
        return this.trainStationsCache.get(cacheKey);
      }

      // Query train stations for the city
      const { data: stations, error } = await supabase
        .from('station')
        .select(`
          id,
          name,
          station_type,
          location:location_id (
            id,
            city_id,
            address,
            latitude,
            longitude
          )
        `)
        .eq('station_type', 'train_station')
        .eq('location.city_id', cityId);

      if (error) {
        console.warn(`⚠️ Error fetching train stations for city ${cityId}:`, error);
        return [];
      }

      const processedStations = (stations || []).map(station => ({
        id: station.id,
        name: station.name,
        address: station.location?.address || 'Address not available',
        coordinates: {
          lat: station.location?.latitude,
          lng: station.location?.longitude
        }
      })).filter(station =>
        station.coordinates.lat && station.coordinates.lng
      );

      // Cache the result
      this.trainStationsCache.set(cacheKey, processedStations);

      console.log(`🚂 Found ${processedStations.length} train stations for city ${cityId}`);
      return processedStations;

    } catch (error) {
      console.error(`❌ Error getting train stations for city ${cityId}:`, error);
      return [];
    }
  }

  /**
   * Clear existing transport data for a trip
   * @param {number} tripId - Trip ID
   * @returns {Promise<void>}
   */
  async clearExistingTransportData(tripId) {
    try {
      console.log(`🗑️ Clearing existing transport data for trip ${tripId}`);

      // Delete existing transport records
      const { error: transportError } = await supabase
        .from('trip_transport_calculated')
        .delete()
        .eq('trip_id', tripId);

      if (transportError) throw transportError;

      // Clear transport budget allocations
      const { error: budgetError } = await supabase
        .from('budget_allocation')
        .delete()
        .eq('trip_id', tripId)
        .eq('category_id', this.TRANSPORT_CATEGORY_ID);

      if (budgetError) throw budgetError;

      console.log(`✅ Cleared existing transport data for trip ${tripId}`);

    } catch (error) {
      console.error('❌ Error clearing existing transport data:', error);
      throw error;
    }
  }

  /**
   * Save transport data to database
   * @param {Object} calculationResults - Transport calculation results
   * @returns {Promise<void>}
   */
  async saveTransportToDatabase(calculationResults) {
    console.log(`💾 Saving transport data to database for trip ${calculationResults.tripId}`);

    const transportRecords = [];

    // Save daily transport segments
    for (const day of calculationResults.dailyTransport) {
      for (const segment of day.segments) {
        const record = {
          trip_id: calculationResults.tripId,
          day_number: day.dayNumber,
          transport_type: segment.mode || 'unknown',
          from_location: segment.from || 'Unknown',
          to_location: segment.to || 'Unknown',
          distance_km: segment.distance || 0,
          duration_minutes: Math.round(segment.duration || 0),
          price_usd: segment.costUSD || 0,
          price_mad: segment.costMAD || 0,
          description: segment.reasoning || 'No description',
          is_primary_recommendation: true,
          transport_sequence: segment.sequence || 1,
          segment_description: segment.description || 'Transport segment',
          reasoning: segment.reasoning || 'No reasoning provided',
          is_inter_city: false
        };

        if (this.validateTransportRecord(record)) {
          transportRecords.push(record);
        }
      }
    }

    // Save inter-city transport (primary options)
    for (const route of calculationResults.interCityTransport) {
      const record = {
        trip_id: calculationResults.tripId,
        day_number: route.fromDay,
        transport_type: route.mode || 'car_rental',
        from_location: route.fromCity || 'Unknown',
        to_location: route.toCity || 'Unknown',
        distance_km: route.distance || 0,
        duration_minutes: Math.round(route.duration || 0),
        price_usd: route.costUSD || 0,
        price_mad: route.costMAD || 0,
        description: route.reasoning || 'Inter-city transport',
        is_primary_recommendation: true,
        transport_sequence: 1,
        segment_description: route.description || 'Inter-city transport',
        reasoning: route.reasoning || 'Inter-city transport reasoning',
        is_inter_city: true
      };

      if (this.validateTransportRecord(record)) {
        transportRecords.push(record);
      }

      // Save alternative inter-city options
      if (route.alternativeOptions && route.alternativeOptions.length > 0) {
        route.alternativeOptions.forEach((altOption, index) => {
          const altRecord = {
            trip_id: calculationResults.tripId,
            day_number: route.fromDay,
            transport_type: altOption.mode || 'unknown',
            from_location: route.fromCity || 'Unknown',
            to_location: route.toCity || 'Unknown',
            distance_km: route.distance || 0,
            duration_minutes: Math.round(altOption.duration || route.duration || 0),
            price_usd: altOption.costUSD || 0,
            price_mad: altOption.costMAD || 0,
            description: altOption.reasoning || 'Alternative inter-city transport',
            is_primary_recommendation: false,
            transport_sequence: index + 2, // Start from 2 for alternatives
            segment_description: `Alternative: ${route.description || 'Inter-city transport'}`,
            reasoning: altOption.reasoning || 'Alternative transport option',
            is_inter_city: true
          };

          if (this.validateTransportRecord(altRecord)) {
            transportRecords.push(altRecord);
          }
        });
      }
    }

    // Batch insert all records
    if (transportRecords.length > 0) {
      const { error } = await supabase
        .from('trip_transport_calculated')
        .insert(transportRecords);

      if (error) {
        console.error('❌ Database insert error:', error);
        throw error;
      }

      console.log(`✅ Successfully saved ${transportRecords.length} transport records to database`);
    }
  }

  /**
   * Validate transport record before saving
   * @param {Object} record - Transport record to validate
   * @returns {boolean} True if valid, false otherwise
   */
  validateTransportRecord(record) {
    const requiredFields = [
      'trip_id', 'day_number', 'transport_type', 'from_location', 'to_location',
      'distance_km', 'duration_minutes', 'price_usd', 'price_mad'
    ];

    for (const field of requiredFields) {
      if (record[field] === null || record[field] === undefined) {
        console.error(`❌ Missing required field: ${field}`, record);
        return false;
      }

      // Check numeric fields
      if (['distance_km', 'duration_minutes', 'price_usd', 'price_mad'].includes(field)) {
        if (typeof record[field] !== 'number' || isNaN(record[field]) || record[field] < 0) {
          console.error(`❌ Invalid numeric value for ${field}: ${record[field]}`, record);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Update budget allocations for transport
   * @param {number} tripId - Trip ID
   * @param {number} totalCostUSD - Total transport cost in USD
   * @param {Array} dailyTransport - Daily transport data
   * @returns {Promise<void>}
   */
  async updateBudgetAllocations(tripId, totalCostUSD, dailyTransport) {
    console.log(`💰 Updating budget allocations for trip ${tripId}`);

    try {
      // Get trip days for correct trip_day_id mapping
      const { data: tripDays, error: tripDayError } = await supabase
        .from('trip_day')
        .select('id, day_number')
        .eq('trip_id', tripId)
        .order('day_number');

      if (tripDayError) throw tripDayError;
      if (!tripDays || tripDays.length === 0) {
        throw new Error(`No trip days found for trip ${tripId}`);
      }

      // Create mapping from day number to trip_day ID
      const dayNumberToTripDayId = {};
      tripDays.forEach(tripDay => {
        dayNumberToTripDayId[tripDay.day_number] = tripDay.id;
      });

      // Create trip-level budget allocation
      const tripLevelAllocation = {
        trip_id: tripId,
        category_id: this.TRANSPORT_CATEGORY_ID,
        allocated_amount: totalCostUSD,
        spent_amount: totalCostUSD,
        currency: 'USD',
        trip_day_id: null
      };

      // Create daily budget allocations
      const dailyAllocations = [];
      for (const day of dailyTransport) {
        if (day.totalCostUSD > 0) {
          const tripDayId = dayNumberToTripDayId[day.dayNumber];
          if (tripDayId) {
            dailyAllocations.push({
              trip_id: tripId,
              category_id: this.TRANSPORT_CATEGORY_ID,
              allocated_amount: day.totalCostUSD,
              spent_amount: day.totalCostUSD,
              currency: 'USD',
              trip_day_id: tripDayId
            });
          }
        }
      }

      // Insert all budget allocations
      const allAllocations = [tripLevelAllocation, ...dailyAllocations];
      if (allAllocations.length > 0) {
        const { error } = await supabase
          .from('budget_allocation')
          .insert(allAllocations);

        if (error) throw error;
        console.log(`✅ Successfully created ${allAllocations.length} budget allocations`);
      }

    } catch (error) {
      console.error('❌ Error updating budget allocations:', error);
      throw error;
    }
  }

  /**
   * Format existing transport data for UI consumption
   * @param {Array} transportRecords - Raw transport records from database
   * @returns {Promise<Object>} Formatted transport data
   */
  async formatTransportDataForUI(transportRecords) {
    const dailyTransport = [];
    const interCityTransport = [];
    let totalCostUSD = 0;
    let totalSegments = 0;

    // Group by day and type
    const dayGroups = {};

    for (const record of transportRecords) {
      totalCostUSD += record.price_usd;
      totalSegments++;

      if (record.is_inter_city) {
        // Group inter-city records by day to handle alternatives
        let existingRoute = interCityTransport.find(route => route.fromDay === record.day_number);

        if (!existingRoute) {
          // Create new inter-city route
          existingRoute = {
            fromCity: record.from_location,
            toCity: record.to_location,
            fromDay: record.day_number,
            distance: record.distance_km,
            duration: record.duration_minutes,
            mode: record.transport_type,
            costUSD: record.price_usd,
            costMAD: record.price_mad,
            description: record.segment_description,
            reasoning: record.reasoning,
            primaryOption: null,
            alternativeOptions: []
          };
          interCityTransport.push(existingRoute);
        }

        // Add as primary or alternative option
        const option = {
          mode: record.transport_type,
          costUSD: record.price_usd,
          costMAD: record.price_mad,
          reasoning: record.reasoning,
          duration: record.duration_minutes
        };

        if (record.is_primary_recommendation) {
          existingRoute.primaryOption = option;
          // Update main route properties with primary option
          existingRoute.mode = option.mode;
          existingRoute.costUSD = option.costUSD;
          existingRoute.costMAD = option.costMAD;
          existingRoute.reasoning = option.reasoning;
        } else {
          existingRoute.alternativeOptions.push(option);
        }
      } else {
        if (!dayGroups[record.day_number]) {
          dayGroups[record.day_number] = {
            dayNumber: record.day_number,
            segments: [],
            totalCostUSD: 0
          };
        }

        // Find existing segment or create new one
        let existingSegment = dayGroups[record.day_number].segments.find(
          seg => seg.sequence === record.transport_sequence
        );

        if (!existingSegment) {
          existingSegment = {
            sequence: record.transport_sequence,
            description: record.segment_description,
            from: record.from_location,
            to: record.to_location,
            distance: record.distance_km,
            duration: record.duration_minutes,
            mode: record.transport_type,
            costUSD: record.price_usd,
            costMAD: record.price_mad,
            reasoning: record.reasoning,
            primaryOption: null,
            alternativeOptions: []
          };
          dayGroups[record.day_number].segments.push(existingSegment);
        }

        // Add as primary or alternative option
        const option = {
          mode: record.transport_type,
          costUSD: record.price_usd,
          costMAD: record.price_mad,
          reasoning: record.reasoning
        };

        if (record.is_primary_recommendation) {
          existingSegment.primaryOption = option;
          // Update main segment properties with primary option
          existingSegment.mode = option.mode;
          existingSegment.costUSD = option.costUSD;
          existingSegment.costMAD = option.costMAD;
          existingSegment.reasoning = option.reasoning;

          dayGroups[record.day_number].totalCostUSD += record.price_usd;
        } else {
          existingSegment.alternativeOptions.push(option);
        }
      }
    }

    // Convert to arrays and sort
    for (const dayNumber in dayGroups) {
      const day = dayGroups[dayNumber];
      day.segments.sort((a, b) => a.sequence - b.sequence);
      dailyTransport.push(day);
    }

    dailyTransport.sort((a, b) => a.dayNumber - b.dayNumber);

    return {
      dailyTransport,
      interCityTransport,
      totalCostUSD,
      totalSegments
    };
  }

  /**
   * Format calculation results for UI consumption
   * @param {Object} calculationResults - Raw calculation results
   * @returns {Promise<Object>} Formatted transport data
   */
  async formatCalculationResultsForUI(calculationResults) {
    return {
      dailyTransport: calculationResults.dailyTransport,
      interCityTransport: calculationResults.interCityTransport,
      totalCostUSD: calculationResults.totalCostUSD,
      totalSegments: calculationResults.totalSegments
    };
  }
}
