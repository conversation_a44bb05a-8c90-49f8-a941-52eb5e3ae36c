/**
 * Trips Controller - Manages the trips page functionality
 */

import tripsDataManager from './tripsDataManager.js';

class TripsController {
    constructor() {
        this.initialized = false;
        this.trips = [];
        this.filteredTrips = [];
        this.currentFilter = 'all';
        this.currentSort = 'date';
    }

    /**
     * Initialize the trips controller
     */
    async initialize() {
        if (this.initialized) {
            console.log('Trips controller already initialized');
            return;
        }

        try {
            console.log('Initializing trips controller...');

            // Load trips data
            await this.loadTrips();

            // Setup event listeners
            this.setupEventListeners();

            // Render trips
            this.renderTrips();

            this.initialized = true;
            console.log('Trips controller initialized successfully');

        } catch (error) {
            console.error('Error initializing trips controller:', error);
            throw error;
        }
    }

    /**
     * Load trips from the data manager
     */
    async loadTrips() {
        try {
            console.log('Loading trips...');
            this.trips = await tripsDataManager.loadUserTrips();
            this.filteredTrips = [...this.trips];
            console.log(`Loaded ${this.trips.length} trips`);
        } catch (error) {
            console.error('Error loading trips:', error);
            throw new Error('Failed to load trips');
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Create trip button
        const createTripBtn = document.getElementById('create-trip-btn');
        if (createTripBtn) {
            createTripBtn.addEventListener('click', () => {
                window.location.href = './wizard.html';
            });
        }

        // Filter buttons
        const filterButtons = document.querySelectorAll('[data-filter]');
        filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const filter = e.target.dataset.filter;
                this.setFilter(filter);
                this.updateActiveButton(e.target, filterButtons);
            });
        });

        // Sort buttons
        const sortButtons = document.querySelectorAll('[data-sort]');
        sortButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const sort = e.target.dataset.sort;
                this.setSort(sort);
                this.updateActiveButton(e.target, sortButtons);
            });
        });
    }

    /**
     * Update active button state
     */
    updateActiveButton(activeButton, allButtons) {
        allButtons.forEach(btn => btn.classList.remove('active'));
        activeButton.classList.add('active');
    }

    /**
     * Set filter and re-render trips
     */
    setFilter(filter) {
        this.currentFilter = filter;
        this.applyFiltersAndSort();
        this.renderTrips();
    }

    /**
     * Set sort and re-render trips
     */
    setSort(sort) {
        this.currentSort = sort;
        this.applyFiltersAndSort();
        this.renderTrips();
    }

    /**
     * Apply current filters and sorting
     */
    applyFiltersAndSort() {
        // Apply filter
        this.filteredTrips = this.trips.filter(trip => {
            if (this.currentFilter === 'all') return true;
            
            const now = new Date();
            const startDate = new Date(trip.start_date);
            const endDate = new Date(trip.end_date);

            switch (this.currentFilter) {
                case 'upcoming':
                    return startDate > now;
                case 'past':
                    return endDate < now;
                case 'draft':
                    return trip.status === 'draft';
                default:
                    return true;
            }
        });

        // Apply sort
        this.filteredTrips.sort((a, b) => {
            switch (this.currentSort) {
                case 'date':
                    return new Date(b.start_date) - new Date(a.start_date);
                case 'title':
                    return a.title.localeCompare(b.title);
                case 'budget':
                    return (b.budget_total || 0) - (a.budget_total || 0);
                default:
                    return 0;
            }
        });
    }

    /**
     * Render trips to the DOM
     */
    renderTrips() {
        const tripsGrid = document.getElementById('trips-grid');
        const emptyState = document.getElementById('empty-state');

        if (!tripsGrid) {
            console.error('Trips grid element not found');
            return;
        }

        // Clear existing content
        tripsGrid.innerHTML = '';

        // Show empty state if no trips
        if (this.filteredTrips.length === 0) {
            tripsGrid.style.display = 'none';
            if (emptyState) {
                emptyState.style.display = 'block';
            }
            return;
        }

        // Hide empty state and show grid
        tripsGrid.style.display = 'grid';
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // Render trip cards
        this.filteredTrips.forEach(trip => {
            const tripCard = this.createTripCard(trip);
            tripsGrid.appendChild(tripCard);
        });
    }

    /**
     * Create a trip card element
     */
    createTripCard(trip) {
        const card = document.createElement('div');
        card.className = 'trip-card';
        card.dataset.tripId = trip.id;

        // Determine trip status
        const now = new Date();
        const startDate = new Date(trip.start_date);
        const endDate = new Date(trip.end_date);
        
        let status = trip.status || 'draft';
        if (status !== 'draft') {
            if (startDate > now) {
                status = 'upcoming';
            } else if (endDate < now) {
                status = 'past';
            } else {
                status = 'ongoing';
            }
        }

        // Format dates
        const formattedStartDate = this.formatDate(trip.start_date);
        const formattedEndDate = this.formatDate(trip.end_date);
        const formattedCreatedDate = this.formatDate(trip.created_at);
        const duration = this.calculateDuration(trip.start_date, trip.end_date);

        // Format budget
        const budget = trip.budget_total ? `${trip.budget_total} ${trip.currency || 'MAD'}` : 'No budget set';

        // Default image if none provided
        const imageUrl = trip.image_url || 'https://images.unsplash.com/photo-1564507004663-b6dfb3c824d5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8bW9yb2Njb3xlbnwwfHwwfHx8MA%3D%3D';

        card.innerHTML = `
            <div class="trip-image">
                <img src="${imageUrl}" alt="${trip.title}" loading="lazy">
                <div class="trip-location">
                    <i class="fas fa-map-marker-alt"></i>
                    Morocco
                </div>
                <div class="trip-status ${status}">${status.charAt(0).toUpperCase() + status.slice(1)}</div>
            </div>
            <div class="trip-details">
                <div class="trip-header">
                    <h3 class="trip-title">${trip.title}</h3>
                </div>
                <div class="trip-dates">
                    <i class="far fa-calendar-alt"></i>
                    ${formattedStartDate} - ${formattedEndDate} (${duration})
                </div>
                <div class="trip-budget">
                    <i class="fas fa-dollar-sign"></i>
                    ${budget}
                </div>
                <div class="trip-created">
                    <i class="far fa-clock"></i>
                    Created ${formattedCreatedDate}
                </div>
                <p class="trip-description">${trip.description || 'No description available'}</p>
            </div>
        `;

        // Add click handler to navigate to trip details
        card.addEventListener('click', () => {
            window.location.href = `./plan_v2.html?id=${trip.id}`;
        });

        return card;
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        if (!dateString) return 'No date';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    /**
     * Calculate duration between two dates
     */
    calculateDuration(startDate, endDate) {
        if (!startDate || !endDate) return 'Unknown duration';
        
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return '1 day';
        return `${diffDays} days`;
    }

    /**
     * Refresh trips data
     */
    async refresh() {
        try {
            await this.loadTrips();
            this.applyFiltersAndSort();
            this.renderTrips();
        } catch (error) {
            console.error('Error refreshing trips:', error);
            this.showError('Failed to refresh trips');
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // You can implement a toast notification system here
        console.error(message);
    }
}

// Create and export singleton instance
const tripsController = new TripsController();
export default tripsController;
