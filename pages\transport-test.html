<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transport System Test - Tsafira Travel Planner</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/plan_v2/base.css">
    <link rel="stylesheet" href="../css/plan_v2/layout.css">
    <link rel="stylesheet" href="../css/plan_v2/components.css">
    <link rel="stylesheet" href="../css/plan_v2/timeline.css">
    <link rel="stylesheet" href="../css/plan_v2/responsive.css">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: var(--color-bg-alt);
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-card);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
            color: white;
            padding: var(--spacing-8);
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0 0 var(--spacing-2) 0;
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
        }
        
        .test-header p {
            margin: 0;
            font-size: var(--font-size-lg);
            opacity: 0.9;
        }
        
        .test-content {
            padding: var(--spacing-8);
        }
        
        .test-section {
            margin-bottom: var(--spacing-8);
            padding: var(--spacing-6);
            background: var(--color-bg-alt);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
        }
        
        .test-section h2 {
            margin: 0 0 var(--spacing-4) 0;
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text);
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }
        
        .test-section h2 i {
            color: var(--color-primary);
        }
        
        .trip-input {
            display: flex;
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-4);
            align-items: center;
        }
        
        .trip-input label {
            font-weight: var(--font-weight-medium);
            color: var(--color-text);
            min-width: 80px;
        }
        
        .trip-input input {
            flex: 1;
            padding: var(--spacing-3);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            font-size: var(--font-size-base);
        }
        
        .trip-input button {
            padding: var(--spacing-3) var(--spacing-4);
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .trip-input button:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
        }
        
        .trip-input button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-display {
            background: var(--color-bg);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-4);
            margin-top: var(--spacing-4);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-2) 0;
            border-bottom: 1px solid var(--color-border);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: var(--font-weight-medium);
            color: var(--color-text);
        }
        
        .status-value {
            color: var(--color-text-muted);
        }
        
        .status-value.success {
            color: var(--success-color);
        }
        
        .status-value.error {
            color: var(--error-color);
        }
        
        .transport-display {
            margin-top: var(--spacing-6);
        }
        
        .log-output {
            background: #1f2937;
            color: #f3f4f6;
            padding: var(--spacing-4);
            border-radius: var(--radius-md);
            font-family: 'Courier New', monospace;
            font-size: var(--font-size-sm);
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .instructions {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: var(--radius-md);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }
        
        .instructions h3 {
            margin: 0 0 var(--spacing-2) 0;
            color: var(--info-color);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
        }
        
        .instructions p {
            margin: 0;
            color: var(--color-text);
            font-size: var(--font-size-sm);
            line-height: 1.5;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .test-header {
                padding: var(--spacing-6);
            }
            
            .test-content {
                padding: var(--spacing-4);
            }
            
            .test-section {
                padding: var(--spacing-4);
            }
            
            .trip-input {
                flex-direction: column;
                align-items: stretch;
            }
            
            .trip-input label {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🚗 Transport System Test</h1>
            <p>Comprehensive transport calculation system for Tsafira Travel Planner</p>
        </div>
        
        <div class="test-content">
            <div class="instructions">
                <h3>📋 Instructions</h3>
                <p>
                    This page tests the NEW CLEAN transport calculation system. The system has been completely
                    rebuilt to fix all previous issues including OSRM API errors and database constraint violations.
                </p>
                <p><strong>Recommended Test Trip:</strong> Trip 6 (has complete location data)</p>
                <p><strong>For comprehensive testing, use:</strong> <a href="../transport-test-comprehensive.html" style="color: #10b981; font-weight: bold;">Comprehensive Transport Test</a></p>
            </div>
            
            <div class="test-section">
                <h2>
                    <i class="fas fa-play-circle"></i>
                    Initialize Test
                </h2>
                
                <div class="trip-input">
                    <label for="tripId">Trip ID:</label>
                    <input type="number" id="tripId" placeholder="Enter trip ID (e.g., 1)" min="1">
                    <button id="initializeBtn">Initialize</button>
                </div>
                
                <div id="statusDisplay" class="status-display" style="display: none;">
                    <div class="status-item">
                        <span class="status-label">Status:</span>
                        <span id="statusValue" class="status-value">Not initialized</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Trip ID:</span>
                        <span id="tripIdValue" class="status-value">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Transport Data:</span>
                        <span id="transportDataValue" class="status-value">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Last Calculated:</span>
                        <span id="lastCalculatedValue" class="status-value">-</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h2>
                    <i class="fas fa-terminal"></i>
                    Console Output
                </h2>
                <div id="logOutput" class="log-output">
                    Waiting for initialization...
                </div>
            </div>
            
            <div id="transportDisplay" class="test-section transport-display" style="display: none;">
                <h2>
                    <i class="fas fa-route"></i>
                    Transport Data
                </h2>
                <div id="transportContent">
                    <!-- Transport data will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Supabase Configuration -->
    <script>
        // Initialize Supabase client using the same configuration as working pages
        const { createClient } = supabase;
        const supabaseUrl = 'https://ciupgijiybhnnesffhta.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNpdXBnaWppeWJobm5lc2ZmaHRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxMDE5OTgsImV4cCI6MjA2MzY3Nzk5OH0.D4G_DIMdr8aCuVN6jPyscVCa5oGHqPj0yWZYPImizmc';

        // Create and make supabase client available globally
        window.supabase = createClient(supabaseUrl, supabaseKey);

        // Also make it available as a global variable for ES6 modules
        globalThis.supabase = window.supabase;

        console.log('✅ Supabase client initialized with correct API key');
    </script>
    
    <!-- Transport System Scripts -->
    <script type="module">
        import { CleanTransportService } from '../js/services/transport-service-clean.js';
        
        class TransportTest {
            constructor() {
                this.transportService = new CleanTransportService();
                this.currentTripId = null;
                this.logOutput = document.getElementById('logOutput');
                
                this.setupEventListeners();
                this.setupConsoleCapture();
            }
            
            setupEventListeners() {
                const initializeBtn = document.getElementById('initializeBtn');
                const tripIdInput = document.getElementById('tripId');
                
                initializeBtn.addEventListener('click', () => {
                    const tripId = parseInt(tripIdInput.value);
                    if (tripId && tripId > 0) {
                        this.initializeTest(tripId);
                    } else {
                        this.log('❌ Please enter a valid trip ID', 'error');
                    }
                });
                
                tripIdInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        initializeBtn.click();
                    }
                });
            }
            
            setupConsoleCapture() {
                const originalLog = console.log;
                const originalError = console.error;
                const originalWarn = console.warn;
                
                console.log = (...args) => {
                    originalLog.apply(console, args);
                    this.log(args.join(' '), 'info');
                };
                
                console.error = (...args) => {
                    originalError.apply(console, args);
                    this.log(args.join(' '), 'error');
                };
                
                console.warn = (...args) => {
                    originalWarn.apply(console, args);
                    this.log(args.join(' '), 'warn');
                };
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';

                // Handle objects and errors better
                let logMessage = message;
                if (typeof message === 'object') {
                    logMessage = JSON.stringify(message, null, 2);
                }

                this.logOutput.textContent += `[${timestamp}] ${prefix} ${logMessage}\n`;
                this.logOutput.scrollTop = this.logOutput.scrollHeight;
            }
            
            async initializeTest(tripId) {
                this.currentTripId = tripId;
                this.log(`🚀 Initializing transport test for trip ${tripId}`, 'info');
                
                // Update status display
                this.updateStatus('Initializing...', tripId);
                
                try {
                    // Always calculate fresh transport data with the clean system
                    this.log('🔄 Calculating transport with clean system...', 'info');
                    await this.calculateTransport(tripId);
                    
                } catch (error) {
                    this.log(`❌ Initialization failed: ${error.message}`, 'error');
                    this.updateStatus('Error', tripId, false);
                }
            }
            
            async calculateTransport(tripId) {
                this.log('🔄 Starting clean transport calculation...', 'info');
                this.updateStatus('Calculating...', tripId);

                try {
                    const result = await this.transportService.calculateAndSaveTransport(tripId);

                    if (result.success) {
                        this.log('✅ Transport calculation completed successfully!', 'info');
                        this.log(`📈 Summary: ${result.summary.totalSegments} segments, $${result.summary.totalCostUSD.toFixed(2)} total`, 'info');

                        this.updateStatus('Completed', tripId, true, new Date());
                        await this.displayTransportData(result);
                    } else {
                        throw new Error(result.error);
                    }

                } catch (error) {
                    this.log(`❌ Transport calculation failed: ${error.message}`, 'error');
                    this.updateStatus('Error', tripId, false);
                }
            }
            
            async displayTransportData(result) {
                const transportDisplay = document.getElementById('transportDisplay');
                const transportContent = document.getElementById('transportContent');

                try {
                    const data = result.transportData;

                    transportContent.innerHTML = `
                        <div class="transport-summary">
                            <h3>📊 Transport Summary</h3>
                            <p><strong>Total Cost:</strong> $${result.summary.totalCostUSD.toFixed(2)}</p>
                            <p><strong>Total Segments:</strong> ${result.summary.totalSegments}</p>
                            <p><strong>Daily Transport:</strong> ${result.summary.totalDays} days</p>
                            <p><strong>Inter-city Routes:</strong> ${result.summary.interCityRoutes} routes</p>
                        </div>

                        <div class="transport-details">
                            <h4>Daily Transport Breakdown:</h4>
                            ${data.dailyTransport.map(day => `
                                <div class="day-transport">
                                    <h5>Day ${day.dayNumber} - ${day.cityName} - $${day.totalCostUSD.toFixed(2)}</h5>
                                    <ul>
                                        ${day.segments.map(segment => `
                                            <li>
                                                <strong>${segment.description}:</strong>
                                                ${segment.transportType} -
                                                ${segment.distance.toFixed(2)}km -
                                                $${segment.costUSD.toFixed(2)}
                                                <br><small>${segment.reasoning}</small>
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            `).join('')}

                            ${data.interCityTransport.length > 0 ? `
                                <h4>Inter-city Transport:</h4>
                                ${data.interCityTransport.map(route => `
                                    <div class="day-transport">
                                        <h5>${route.fromCity} → ${route.toCity} (Day ${route.fromDay} to ${route.toDay}) - $${route.costUSD.toFixed(2)}</h5>
                                        <ul>
                                            <li><strong>Distance:</strong> ${route.distance.toFixed(2)}km</li>
                                            <li><strong>Duration:</strong> ${Math.round(route.duration)} minutes</li>
                                            <li><strong>Transport:</strong> ${route.transportType}</li>
                                            <li><small>${route.reasoning}</small></li>
                                        </ul>
                                    </div>
                                `).join('')}
                            ` : ''}
                        </div>
                    `;

                    transportDisplay.style.display = 'block';
                    this.log('✅ Transport data displayed successfully!', 'info');

                } catch (error) {
                    this.log(`❌ Failed to display transport data: ${error.message}`, 'error');
                }
            }
            
            updateStatus(status, tripId, hasData = null, lastCalculated = null) {
                document.getElementById('statusDisplay').style.display = 'block';
                document.getElementById('statusValue').textContent = status;
                document.getElementById('statusValue').className = `status-value ${status === 'Error' ? 'error' : 'success'}`;
                document.getElementById('tripIdValue').textContent = tripId;
                
                if (hasData !== null) {
                    document.getElementById('transportDataValue').textContent = hasData ? 'Available' : 'Not available';
                    document.getElementById('transportDataValue').className = `status-value ${hasData ? 'success' : 'error'}`;
                }
                
                if (lastCalculated) {
                    document.getElementById('lastCalculatedValue').textContent = lastCalculated.toLocaleString();
                }
            }
        }
        
        // Initialize test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new TransportTest();
        });
    </script>
</body>
</html>
