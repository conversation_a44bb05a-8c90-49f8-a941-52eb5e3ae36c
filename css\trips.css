/* Trips Page Styles - Consistent with Tsafira Design System */

/* Import design system variables from account.css */
:root {
  /* Primary Colors */
  --primary: #ff7e33;
  --primary-hover: #f05e0a;
  --primary-light: #fff0e8;
  --primary-dark: #c24700;

  /* Secondary Colors */
  --secondary: #3b82f6;
  --secondary-hover: #2563eb;
  --secondary-light: #eff6ff;

  /* Neutral Colors */
  --bg-color: #ffffff;
  --bg-color-secondary: #f9fafb;
  --text-color: #1f2937;
  --text-color-secondary: #4b5563;
  --text-muted: #6b7280;
  --border-color: #e5e7eb;
  --border-color-focus: #d1d5db;

  /* UI Elements */
  --card-bg: #ffffff;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --input-bg: #ffffff;

  /* Status Colors */
  --success-color: #10b981;
  --success-light: #ecfdf5;
  --warning-color: #f59e0b;
  --warning-light: #fffbeb;
  --error-color: #ef4444;
  --error-light: #fef2f2;
  --info-color: #3b82f6;
  --info-light: #eff6ff;

  /* Animation */
  --transition-speed: 0.3s;
  --transition-function: cubic-bezier(0.4, 0, 0.2, 1);

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
}

/* Dark Mode Variables */
.dark-mode {
  --primary: #ff7e33;
  --primary-hover: #f05e0a;
  --primary-light: #3d2b1f;
  --primary-dark: #ffb080;

  --secondary: #60a5fa;
  --secondary-hover: #3b82f6;
  --secondary-light: #1e293b;

  --bg-color: #0f172a;
  --bg-color-secondary: #1e293b;
  --text-color: #f3f4f6;
  --text-color-secondary: #e5e7eb;
  --text-muted: #9ca3af;
  --border-color: #334155;
  --border-color-focus: #475569;

  --card-bg: #1e293b;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
  --input-bg: #334155;

  --success-color: #059669;
  --success-light: #064e3b;
  --warning-color: #d97706;
  --warning-light: #422006;
  --error-color: #dc2626;
  --error-light: #450a0a;
  --info-color: #3b82f6;
  --info-light: #172554;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--font-sans);
}

body {
  background-color: var(--bg-color-secondary);
  color: var(--text-color);
  min-height: 100vh;
  line-height: 1.5;
  font-size: 16px;
  font-weight: 400;
  transition: background-color var(--transition-speed) var(--transition-function),
              color var(--transition-speed) var(--transition-function);
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  min-height: 100vh;
}

/* Header */
.trips-header {
  margin-bottom: 2rem;
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: background-color var(--transition-speed) var(--transition-function),
              border-color var(--transition-speed) var(--transition-function);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.125rem;
  background-color: var(--bg-color-secondary);
  color: var(--text-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-speed) var(--transition-function);
  cursor: pointer;
}

.back-button:hover {
  background-color: var(--input-bg);
  border-color: var(--border-color-focus);
  transform: translateY(-1px);
  color: var(--text-color);
}

.back-button i {
  font-size: 0.875rem;
  transition: transform var(--transition-speed) var(--transition-function);
}

.back-button:hover i {
  transform: translateX(-2px);
}

.trips-header h1 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.875rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* Filters */
.trips-filters {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.25rem;
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  flex-wrap: wrap;
  transition: background-color var(--transition-speed) var(--transition-function),
              border-color var(--transition-speed) var(--transition-function);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-secondary);
  white-space: nowrap;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--bg-color-secondary);
  border-radius: var(--radius-full);
  padding: 0.25rem;
  border: 1px solid var(--border-color);
}

.filter-option {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border: none;
  background: transparent;
  color: var(--text-color-secondary);
  transition: all var(--transition-speed) var(--transition-function);
  user-select: none;
}

.filter-option:hover {
  background-color: var(--primary-light);
  color: var(--primary);
}

.filter-option.active {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 2px 4px rgba(255, 126, 51, 0.2);
}

/* Trips Grid */
.trips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .trips-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .trips-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Trip Card */
.trip-card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: transform var(--transition-speed) var(--transition-function),
              background-color var(--transition-speed) var(--transition-function),
              border-color var(--transition-speed) var(--transition-function),
              box-shadow var(--transition-speed) var(--transition-function);
  position: relative;
  cursor: pointer;
}

.trip-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover);
  border-color: var(--primary);
}

.trip-image {
  height: 12rem;
  overflow: hidden;
  position: relative;
}

.trip-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
  z-index: 1;
}

.trip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s var(--transition-function);
}

.trip-card:hover .trip-image img {
  transform: scale(1.1);
}

.trip-location {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.trip-location i {
  font-size: 0.875rem;
}

.trip-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  z-index: 2;
}

.trip-status.draft {
  background-color: var(--info-light);
  color: var(--info-color);
  border: 1px solid var(--info-color);
}

.trip-status.upcoming {
  background-color: var(--warning-light);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.trip-status.past {
  background-color: var(--success-light);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.trip-status.ongoing {
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid var(--primary);
}

/* Trip Details */
.trip-details {
  padding: 1.25rem;
}

.trip-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.trip-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.3;
}

.trip-dates {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.trip-dates i {
  font-size: 0.875rem;
}

.trip-budget {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: var(--primary);
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.trip-budget i {
  font-size: 0.875rem;
}

.trip-created {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: var(--text-muted);
  font-size: 0.75rem;
  margin-bottom: 1rem;
}

.trip-created i {
  font-size: 0.75rem;
}

.trip-description {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  margin-bottom: 1.25rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Buttons - Consistent with Design System */
.primary-button {
  background-color: var(--primary);
  color: white;
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: var(--radius-full);
  font-weight: 500;
  font-size: 0.9375rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--transition-speed) var(--transition-function);
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 1;
  cursor: pointer;
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, var(--primary-hover), var(--primary));
  opacity: 0;
  z-index: -1;
  transition: opacity var(--transition-speed) var(--transition-function);
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3);
}

.primary-button:hover::before {
  opacity: 1;
}

.primary-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(249, 115, 22, 0.2);
}

.primary-button i {
  font-size: 1rem;
  transition: transform var(--transition-speed) var(--transition-function);
}

.primary-button:hover i {
  transform: translateX(-2px);
}

.secondary-button {
  background-color: var(--secondary-light);
  color: var(--secondary);
  padding: 0.875rem 1.75rem;
  border: 1px solid var(--secondary);
  border-radius: var(--radius-full);
  font-weight: 500;
  font-size: 0.9375rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--transition-speed) var(--transition-function);
  cursor: pointer;
}

.secondary-button:hover {
  background-color: var(--secondary);
  color: white;
  transform: translateY(-1px);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px dashed var(--border-color);
  margin-top: 1rem;
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-color);
}

.empty-state p {
  color: var(--text-muted);
  margin-bottom: 1.5rem;
  max-width: 30rem;
  margin-left: auto;
  margin-right: auto;
}

/* Error State */
.error-state {
  text-align: center;
  padding: 3rem 2rem;
  background-color: var(--error-light);
  border-radius: var(--radius-lg);
  border: 1px solid var(--error-color);
  margin-top: 1rem;
}

.error-state-icon {
  font-size: 3rem;
  color: var(--error-color);
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.error-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--error-color);
  margin-bottom: 0.75rem;
}

.error-state p {
  color: var(--error-color);
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-speed) var(--transition-function);
}

.dark-mode .loading-screen {
  background: rgba(15, 23, 42, 0.95);
}

.loading-content {
  text-align: center;
  background-color: var(--card-bg);
  padding: 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.loading-content p {
  color: var(--text-color-secondary);
  font-weight: 500;
  margin: 0;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toast {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1rem 1.25rem;
  box-shadow: var(--card-shadow-hover);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 20rem;
  max-width: 24rem;
  transform: translateX(100%);
  animation: slideIn 0.3s var(--transition-function) forwards;
}

.toast.success {
  border-color: var(--success-color);
}

.toast.error {
  border-color: var(--error-color);
}

.toast.warning {
  border-color: var(--warning-color);
}

.toast.info {
  border-color: var(--info-color);
}

@keyframes slideIn {
  to {
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .trips-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .trips-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-left {
    justify-content: space-between;
    align-items: center;
  }

  .trips-header h1 {
    font-size: 1.5rem;
  }

  .trips-filters {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .filter-options {
    width: 100%;
    justify-content: space-between;
  }

  .trips-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .trip-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .trip-image {
    height: 10rem;
  }

  .trip-details {
    padding: 1rem;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    justify-content: center;
  }

  .toast-container {
    left: 1rem;
    right: 1rem;
  }

  .toast {
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
  }

  .trips-header {
    margin-bottom: 1rem;
  }

  .trips-filters {
    margin-bottom: 1rem;
  }

  .filter-options {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .filter-option {
    flex: 1;
    min-width: 0;
    text-align: center;
  }
}
